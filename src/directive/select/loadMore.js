import { debounce } from "lodash";

export default {
  bind(el, binding) {
    // 获取传递的 popper-class-name 属性
    const popperClassName = el.getAttribute("popper-class-name");

    let scrollWrap = document.querySelector(
      `.${popperClassName} .el-select-dropdown__wrap`
    );

    // 防抖处理滚动事件
    const handle = debounce((e) => {
      console.log("scrollWrap.scrollTop", scrollWrap.scrollTop);
      console.log("scrollWrap.scrollHeight", scrollWrap.scrollHeight);
      console.log("scrollWrap.clientHeight", scrollWrap.clientHeight);
      let scrollDistance = scrollWrap.scrollHeight - scrollWrap.scrollTop;
      // 预留10个像素的位置用于触底
      if (scrollWrap.clientHeight + 10 > scrollDistance) {
        binding.value(); // 触底通知外界
      }
    }, 170);

    // 绑定滚动事件
    scrollWrap?.addEventListener("scroll", handle);
    // 方法挂载到元素上以便解绑时使用
    el._hanlde = handle;
  },
  unbind(el) {
    let scrollWrap = document.querySelector(".el-scrollbar__wrap");
    scrollWrap?.removeEventListener("scroll", el._hanlde);
    el._hanlde = null;
  }
};
