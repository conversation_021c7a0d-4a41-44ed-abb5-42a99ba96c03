import { debounce } from "lodash";

export default {
  bind(el, binding) {
    // 获取传递的 popper-class-name 属性
    const popperClassName = el.getAttribute("popper-class-name");
    console.log("popperClassName", popperClassName);

    // 防抖处理滚动事件
    const handle = debounce((e) => {
      // 每次滚动时重新获取scrollWrap，确保元素存在
      let scrollWrap = document.querySelector(
        `.${popperClassName} .el-select-dropdown__wrap`
      );

      if (!scrollWrap) {
        // 尝试其他可能的选择器
        scrollWrap = document.querySelector(".el-select-dropdown__wrap");
      }

      if (scrollWrap) {
        console.log("scrollWrap.scrollTop", scrollWrap.scrollTop);
        console.log("scrollWrap.scrollHeight", scrollWrap.scrollHeight);
        console.log("scrollWrap.clientHeight", scrollWrap.clientHeight);
        let scrollDistance = scrollWrap.scrollHeight - scrollWrap.scrollTop;
        // 预留10个像素的位置用于触底
        if (scrollWrap.clientHeight + 10 > scrollDistance) {
          binding.value(); // 触底通知外界
        }
      }
    }, 170);

    // 延迟绑定滚动事件的函数
    const bindScrollEvent = () => {
      let scrollWrap = document.querySelector(
        `.${popperClassName} .el-select-dropdown__wrap`
      );

      if (!scrollWrap) {
        scrollWrap = document.querySelector(".el-select-dropdown__wrap");
      }

      console.log("Trying to bind scroll event, scrollWrap:", scrollWrap);

      if (scrollWrap && !scrollWrap._loadMoreBound) {
        scrollWrap.addEventListener("scroll", handle);
        scrollWrap._loadMoreBound = true; // 标记已绑定，避免重复绑定
        el._scrollWrap = scrollWrap; // 保存引用用于解绑
        console.log("Scroll event bound successfully");
      }
    };

    // 使用 MutationObserver 监听 DOM 变化，当下拉框出现时绑定事件
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          // 检查是否有新的下拉框元素添加
          const dropdown = document.querySelector(`.${popperClassName}`);
          if (dropdown) {
            setTimeout(bindScrollEvent, 100); // 给一点时间让内部元素渲染
          }
        }
      });
    });

    // 开始观察 body 的子元素变化（因为下拉框通常添加到 body 下）
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    // 方法挂载到元素上以便解绑时使用
    el._handle = handle;
    el._observer = observer;
    el._bindScrollEvent = bindScrollEvent;
  },
  unbind(el) {
    // 停止观察
    if (el._observer) {
      el._observer.disconnect();
      el._observer = null;
    }

    // 移除滚动事件监听
    if (el._scrollWrap) {
      el._scrollWrap.removeEventListener("scroll", el._handle);
      el._scrollWrap._loadMoreBound = false;
      el._scrollWrap = null;
    }

    el._handle = null;
    el._bindScrollEvent = null;
  }
};
