<template>
  <div
    v-loading.fullscreen.lock="loading"
    :class="{ 'site-sidebar--fold': sidebarFold }"
    class="site-wrapper"
    element-loading-text="加载中"
  >
    <template v-if="!loading">
      <main-navbar />
      <main-sidebar v-if="!hiddenSidebarBar" />
      <div
        v-if="hiddenSidebarBar"
        style="min-height: 100vh; margin-left: 0"
        class="site-content__wrapper"
      >
        <main-content v-if="!$store.state.common.contentIsNeedRefresh" />
      </div>
      <div v-else style="min-height: 100vh" class="site-content__wrapper">
        <main-content v-if="!$store.state.common.contentIsNeedRefresh" />
      </div>
    </template>
  </div>
</template>

<script>
import MainNavbar from "./main-navbar";
import MainSidebar from "./main-sidebar";
import MainContent from "./main-content";
import { clearLoginInfo } from "@/utils";
import { isURL } from "@/utils/validate";

export default {
  provide() {
    return {
      // 刷新
      refresh() {
        this.$store.commit("common/updateContentIsNeedRefresh", true);
        this.$nextTick(() => {
          this.$store.commit("common/updateContentIsNeedRefresh", false);
        });
      }
    };
  },
  data() {
    return {
      hiddenSidebarBar: false,
      loading: true
    };
  },
  components: {
    MainNavbar,
    MainSidebar,
    MainContent
  },
  computed: {
    documentClientHeight: {
      get() {
        return this.$store.state.common.documentClientHeight;
      },
      set(val) {
        this.$store.commit("common/updateDocumentClientHeight", val);
      }
    },
    sidebarFold: {
      get() {
        return this.$store.state.common.sidebarFold;
      }
    },
    userId: {
      get() {
        return this.$store.state.user.id;
      },
      set(val) {
        this.$store.commit("user/updateId", val);
      }
    },
    userName: {
      get() {
        return this.$store.state.user.name;
      },
      set(val) {
        this.$store.commit("user/updateName", val);
      }
    },
    roleId: {
      get() {
        return this.$store.state.user.roleId;
      },
      set(roleId) {
        this.$store.commit("user/updateRoleId", roleId);
      }
    },
    roles: {
      get() {
        return this.$store.state.user.roles;
      },
      set(roles) {
        this.$store.commit("user/updateRoles", roles);
      }
    }
  },
  created() {
    this.getUserInfo();
  },
  mounted() {
    this.hiddenSidebarBar = this.$route.meta.hidden;
    this.resetDocumentClientHeight();
  },
  watch: {
    $route: "routeHandle"
  },
  methods: {
    routeHandle(route) {
      this.hiddenSidebarBar = route.meta.hidden;
    },
    // 重置窗口可视高度
    resetDocumentClientHeight() {
      this.documentClientHeight = document.documentElement.clientHeight;
      window.onresize = () => {
        this.documentClientHeight = document.documentElement.clientHeight;
      };
    },
    // 获取当前管理员信息
    getUserInfo() {
      this.$http({
        url: this.$http.adornUrl("/sys/user/info"),
        method: "get",
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.loading = false;
          this.userId = data.user.userId;
          this.userName = data.user.username;

          // 用户角色信息
          this.roles = data.roles;
          if (sessionStorage.getItem("roleId") === null) {
            // sessionStorage.setItem('roleId', this.roles[0].roleId)

            // 默认使用 roleId 最大的角色
            this.roleId = [...this.roles].sort((r1, r2) => {
              return r1.roleId - r2.roleId;
            })[this.roles.length - 1].roleId;

            sessionStorage.setItem("roleId", this.roleId);
          }
          const roleId = Number(sessionStorage.getItem("roleId"));
          // 防止管理员修改用户角色后找不到对应 roleId（重新登陆）
          if (this.roles.filter((it) => it.roleId === roleId).length > 0) {
            this.roleId = roleId;
          } else {
            clearLoginInfo();
          }
        }
      });
    }
  }
};
</script>

<style>
.back-head {
  font-size: 16px;
  margin-bottom: 20px;
}

.back-head .go-back {
  color: #3474fe;
  margin-right: 5px;
}

.status-width {
  width: 90px;
}
</style>
