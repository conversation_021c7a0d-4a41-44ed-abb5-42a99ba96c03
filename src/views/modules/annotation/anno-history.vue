<template>
  <div>
    <div v-if="projectEnv === null" class="back-head">
      <a class="go-back" href="javascript:0;" @click="$router.go(-1)">
        <i class="el-icon-arrow-left"></i>返回
      </a>
      文书列表
    </div>
    <history :batchId="batchId"></history>
  </div>
</template>

<script scoped>
import history from "@/views/modules/annotation/components/history";

export default {
  name: "PHistory",
  props: {},
  components: { history },
  computed: {
    batchId: function () {
      const projectEnv = JSON.parse(sessionStorage.getItem("workEnv"));
      if (projectEnv !== null) {
        return projectEnv.batchId;
      } else {
        return this.$route.query.batchId;
      }
    }
  },
  data() {
    return {};
  },
  methods: {},
  mounted() {},
  created() {}
};
</script>

<style scoped></style>
