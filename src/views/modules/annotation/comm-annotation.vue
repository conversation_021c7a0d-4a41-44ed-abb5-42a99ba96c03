<template>
  <main class="main" v-loading="commAnnoLoading">
    <!-- 历史版本模式提示横幅 -->
    <div v-if="isHistoryMode" class="history-mode-banner">
      <el-alert
        title="正在查看历史版本，仅显示讨论相关的实体和属性"
        type="info"
        :closable="false"
        show-icon
      >
        <template slot="description">
          正在查看讨论创建时的历史版本，仅显示与该讨论相关的实体和属性。此模式为只读模式，无法进行任何编辑操作。
        </template>
      </el-alert>
    </div>

    <el-row :gutter="20" v-if="articleFlag">
      <el-col :span="15">
        <!--文本标注界面-->
        <section class="tag-section">
          <pre-select
            :source-list="sourceList"
            :show-pre-select="showPreSelect"
            :role-id="roleId"
            :has-select-tag="hasSelectTag"
            :curr-anno-uni-id="currAnnoUniId"
            :annotator-id="annotatorId"
            ref="refPreSelect"
          ></pre-select>
          <!--id用于获取当作选择器-->
          <base-article
            id="article"
            :documentId="documentId"
            ref="myArticle"
            class="tag-main-article"
          />
        </section>
      </el-col>
      <el-col :span="9" :style="rightPartStyle">
        <div
          v-if="
            activeName === 'entity' ||
            activeName === 'attribute' ||
            activeName === 'entityList' ||
            activeName === 'discussion'
          "
          class="main-header"
        >
          <div class="main-left">
            <el-radio-group
              @input="changeAnnoSwitch"
              v-model="annoTypeSwitch"
              size="mini"
            >
              <el-radio-button label="实体标注">
                <template slot-scope="labelData">
                  <i style="margin-right: 4px" class="el-icon-collection"></i>
                </template>
              </el-radio-button>
              <el-radio-button label="属性标注">
                <template slot-scope="labelData">
                  <i style="margin-right: 4px" class="el-icon-postcard"></i>
                </template>
              </el-radio-button>
              <el-radio-button label="实体清单">
                <template slot-scope="labelData">
                  <i style="margin-right: 4px" class="el-icon-tickets"></i>
                </template>
              </el-radio-button>
              <el-radio-button label="讨论区">
                <template slot-scope="labelData">
                  <i
                    style="margin-right: 4px"
                    class="el-icon-chat-dot-round"
                  ></i>
                </template>
              </el-radio-button>
            </el-radio-group>
          </div>
          <div class="main-left batch-switch">
            <el-form :inline="true" label-width="80px">
              <el-form-item v-if="showQuestionBtn" label="">
                <el-button
                  @click="openQuestionDialog"
                  type="warning"
                  size="mini"
                  icon="el-icon-question"
                  round
                  >提问
                </el-button>
              </el-form-item>

              <el-form-item label="批量标注">
                <el-switch
                  @change="changeBatchSwitch"
                  v-model="batchSwitch"
                  :disabled="batchSwitchDisabled"
                  style="font-size: 13px"
                >
                </el-switch>
              </el-form-item>
            </el-form>
            <!--<el-tooltip :content="'批量标注'" placement="top" effect="light">
            </el-tooltip>-->
          </div>
        </div>
        <!--右侧页面的内容-->
        <div class="content">
          <anno-entity ref="refEntity" v-if="activeName === 'entity'" />
          <anno-attribute
            ref="refAttribute"
            :active-name="activeName"
            :is-original="isOriginal"
            v-if="activeName === 'attribute'"
          />
          <anno-relationship
            id="relationship"
            v-if="activeName === 'relationship'"
            :key="`relationship_vue_` + annotation.sourceChangeTimes"
          />
          <entity-list
            id="entityList"
            v-if="activeName === 'entityList'"
            :key="`entity_list_` + sourceChangeTimes"
          />
          <discussion-area
            id="discussionArea"
            v-if="activeName === 'discussion'"
            :key="`discussion_area_` + sourceChangeTimes"
          />
        </div>
      </el-col>
    </el-row>

    <!--划词弹框-->
    <div class="popover" ref="popover" v-show="styles.isShow">
      <DragPopover :styles="styles" ref="drag" v-click-outside="hide">
        <span
          slot="header"
          class="popover-header"
          @click="styles.isShow = false"
          ><i class="el-icon-close"></i
        ></span>
        <div class="popover-content tag-section">
          <!--<h3>批注</h3>-->
          <div
            class="section"
            v-for="(item, index) in entityInfos"
            :key="'pop-anno-' + item.selectedId"
            @click="popChangeEntity"
            :style="item.is_attr | popDivCls"
          >
            <span
              :draggable="item.is_attr | popCanDrag(that)"
              @dragstart="dragPop"
              @mouseover="mouseoverListener"
              @mouseleave="mouseleaveListener"
              style="
                overflow-y: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              "
              :class="item.popCls"
              :annoid="item.annoid"
              :uniqueid="item.selectedId"
              :labelid="item.labelId"
              :annotate="item.annotate"
              :is_attr="item.is_attr"
              :attr_label_info="item.attr_label_info"
              :attr_ids="item.attr_ids"
              :title="item.allContents | popoverFullContent"
              >{{ item.allContents | popoverPartContent }}</span
            >

            <!--<div
              style="margin-top: 8px; margin-left: 1px; margin-bottom: 2px"
              :key="`pop_at_div_${item.annoid}`"
            >
              <template v-if="item.attrList && item.attrList.length > 0">
                <span
                  class="pop_attr"
                  v-for="(attrItem, attrIndex) in item.attrList"
                  :title="attrItem.id"
                  :key="'attr_name' + attrIndex + 'id_' + attrItem.id"
                  v-text="`[ ${attrItem.name} ]`"
                ></span>
              </template>
            </div>-->
          </div>
        </div>
      </DragPopover>
    </div>
    <base-css :data="css" :id="annoLabel" />
    <repulse
      :note-id="noteId"
      @getNextArticle="getNextArticle"
      ref="repulseDialog"
    ></repulse>

    <el-dialog
      title="标注删除"
      :visible.sync="annoDeleteDialogVisible"
      width="30%"
    >
      <el-form
        ref="anno_delete_form"
        :model="annoDeleteForm"
        label-width="188px"
      >
        <el-form-item>
          <span slot="label">
            <i
              class="el-icon-warning"
              style="color: #f56c6c; margin-left: 8px"
            ></i>
            同时删除对应属性标注:
          </span>
          <el-switch v-model="annoDeleteForm.deleteAttrAsWell"></el-switch>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="annoDeleteDialogVisible = false">取消</el-button>
        <el-button type="danger" icon="el-icon-delete" @click="deleteEntity()"
          >删除</el-button
        >
      </span>
    </el-dialog>
    <Metadata :note-id="noteId" ref="metadataDialog"></Metadata>

    <el-dialog
      title="删除属性"
      :visible.sync="deletAttrDialogVisible"
      width="70%"
    >
      <div class="del-att-dialog">
        <div class="del-attr-title">
          <span>属性</span>
          <span :title="deleteAttrName" class="del-attr-name">
            “{{ deleteAttrName }}”
          </span>
          <span>正在使用中，确认要强制删除该属性吗？ </span>
        </div>
        <el-table
          :data="usedEntities"
          :stripe="true"
          border
          :max-height="560"
          style="width: 100%"
        >
          <el-table-column
            prop="name"
            label="使用中的实体名称"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
        </el-table>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="deletAttrDialogVisible = false">取消</el-button>
        <el-button
          type="danger"
          icon="el-icon-delete"
          @click="doForceDeleteAttr"
        >
          强制删除
        </el-button>
      </span>
    </el-dialog>

    <!--提问编辑弹窗-->
    <el-dialog
      :title="questionDialogTitle"
      :visible.sync="questionDialogVisible"
      width="80%"
      max-height="80vh"
      @close="onQuestionDialogClose"
    >
      <div>
        <!-- 提问实体/属性（包含上下文） -->
        <div style="margin-bottom: 15px">
          <div style="font-weight: bolder; margin-bottom: 8px">
            {{ isAttributeQuestion ? "提问属性" : "提问实体" }}
          </div>
          <div
            class="question-context-content"
            v-html="contextWithHighlight"
            style="
              background-color: #f8f9fa;
              padding: 10px;
              border-radius: 4px;
              border: 1px solid #dee2e6;
              max-height: 200px;
              overflow-y: auto;
              line-height: 1.6;
            "
          ></div>
        </div>

        <!-- 违背规则选择 -->
        <div
          style="margin-bottom: 15px"
          v-if="labelRules && labelRules.length > 0"
        >
          <div style="font-weight: bolder; margin-bottom: 8px">
            违背规则（可选）：
          </div>
          <el-radio-group v-model="selectedRule" style="width: 100%">
            <el-radio
              v-for="(rule, index) in labelRules"
              :key="index"
              :label="rule"
              style="display: block; margin-bottom: 8px; white-space: normal"
            >
              {{ rule }}
            </el-radio>
          </el-radio-group>
        </div>

        <!-- 提问内容 -->
        <div style="margin-bottom: 15px">
          <div style="font-weight: bolder; margin-bottom: 8px">提问内容：</div>
          <el-input
            type="textarea"
            :rows="4"
            :placeholder="`请输入${
              isAttributeQuestion ? '属性' : '实体'
            }提问内容`"
            v-model="questionAnnotateVal"
            maxlength="500"
            show-word-limit
          >
          </el-input>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeQuestionDialog">取消</el-button>
        <el-button type="primary" icon="el-icon-check" @click="doSaveQuestion">
          确认
        </el-button>
      </span>
    </el-dialog>
  </main>
</template>

<script>
import BaseArticle from "@/components/annotation/base-article";
import AnnoEntity from "@/views/modules/annotation/components/entity";
import AnnoAttribute from "@/views/modules/annotation/components/attribute";
import AnnoRelationship from "@/views/modules/annotation/components/relation/relationship";
import DragPopover from "@/views/modules/annotation/components/dragPopover";
import Repulse from "@/views/modules/annotation/components/repulse.vue";
import Metadata from "@/views/modules/annotation/components/metadata.vue";
import PreSelect from "@/components/annotation/pre-select";
import { AnnoEventsBus } from "@/utils/bus";
import ClickOutside from "vue-click-outside";
import { getCurrEvent, isAttrTag, joinArray, sleep, trimStr } from "@/utils";
import BaseCss from "@/components/annotation/base-css.vue";
import EntityList from "@/views/modules/annotation/components/entityList";
import DiscussionArea from "@/views/modules/annotation/components/discussionArea";

export default {
  name: "CommAnnotation",
  props: {
    noteId: {
      type: Number,
      required: true
    },
    taskId: {
      type: Number,
      required: false
    },
    annotatorId: {
      required: true
    }
  },
  data() {
    return {
      questionDialogVisible: false,
      questionAnnotateVal: "",
      questionEntityNames: "",
      questionDialogTitle: "提问实体",
      isAttributeQuestion: false,
      highlightedContent: "",
      contextContent: "",
      contextWithHighlight: "",
      labelRules: [],
      selectedRule: "",
      currentQuestionData: null,
      deletAttrDialogVisible: false,
      usedEntities: [],
      deleteAttrName: "",
      that: this,
      commAnnoLoading: false,
      annoDeleteDialogVisible: false,
      annoDeleteForm: {
        deleteAttrAsWell: false
      },
      uniqueid_to_delete: null,
      is_attr_to_delete: null,
      css: "",
      annoLabel: "",
      activeName: "entity",
      documentId: "",
      showPreSelect: true,
      articleFlag: false,
      // annotatorName: undefined,
      projectEnv: {},
      currentNote: {},
      styles: {},
      visible: false,
      activeFiled: "",
      annoTypeSwitch: "实体标注",
      batchSwitch: false,
      batchSwitchDisabled: false,
      sourceList: [],
      sourceType: "原文",
      // ctrl键是否按住
      isCtrlPressed: false,
      // 右侧面板禁用点击事件样式值
      rightPartStyle: "",
      // 当前键盘按键事件对象
      currKeyEvent: null,
      // 实体悬浮弹窗数据
      entityInfos: [],
      currAnnoId: null,
      mouseOverEvent: "",
      taskIdChangeTimes: 0,
      // 标记是否已经执行过首次自动跳转到讨论区
      hasAutoSwitchedToDiscussion: false
    };
  },
  filters: {
    popoverPartContent: function (val) {
      return joinArray(val, " / ", 35);
    },
    popoverFullContent: function (val) {
      return joinArray(val, " / ");
    },
    /* popItemCls: function (selected, labelId, is_attr, labelIdOfAttrAnno) {
      let cls = `tag `;
      if (isAttrTag(is_attr)) {
        if (labelIdOfAttrAnno) {
          cls += `tag-attr-${labelIdOfAttrAnno}`;
        } else {
          cls += `tag-attr`;
        }
      } else {
        cls += "tag-" + labelId;
      }
      if (selected) {
        cls += " select";
      }
      return cls;
    }, */
    popDivCls: function (is_attr) {
      return isAttrTag(is_attr)
        ? "margin: 3px; padding: 7px 3px;"
        : "margin: 3px; padding: 3px";
    },
    popCanDrag: function (is_attr, that) {
      return that.activeName === "attribute" && isAttrTag(is_attr);
    }
  },
  created() {
    /*
    // 显示/隐藏弹框
    AnnoEventsBus.$on("hied-popover", (value) => {
      this.styles.isShow = value;
    });
    // 实体标注弹框的位置信息
    AnnoEventsBus.$on("popoverStyles", (value) => {
      // 用户关闭弹框功能
      if (!this.annoTypeSwitch) {
        return;
      }
      this.$nextTick(() => {
        if (!this.$refs.drag) return;
        const dom = this.$refs.drag.$el;
        const popoverHeight = window
          .getComputedStyle(dom)
          .height.replace("px", "");
        const styles = { ...value };
        //  当底部距离不足以展示弹框，实体标注弹框位置 触发点的顶部
        if (popoverHeight >= styles.bottom) {
          styles.top = styles.top - styles.height - popoverHeight;
        }
        // 实体标注弹框位置 页面左下角
        this.$http({
          url: this.$http.adornUrl("/note/getEntity"),
          method: "post",
          data: this.$http.adornData({
            noteId: this.annotation.noteId,
            textId: this.annotation.textId,
            start: this.annotation.start,
            end: this.annotation.end,
            source: this.annotation.source
          })
        }).then(({ data }) => {
          if (data.code === 0) {
            const result = data.data;
            this.entityAnnotation = result.annotate || "";
            this.styles = styles;
          } else {
            this.$message({
              message: "获取批注信息失败！",
              type: "error"
            });
          }
        });
      });
    }); */
  },
  mounted() {
    // 多人标注切换taskId
    AnnoEventsBus.$on("taskIdChanged", () => {
      this.taskIdChangeTimes = this.taskIdChangeTimes + 1;
    });
    // 切换标注模式
    AnnoEventsBus.$on("switchAnnoType", (type, isEntityList) => {
      this.switchAnnoType(type, isEntityList);
    });
    // 提交标注信息
    AnnoEventsBus.$on("submitAnnoData", (value, event) => {
      this.judgeBatchSubmit(value, event);
    });
    // 显示标注注解弹窗
    AnnoEventsBus.$on("showAnnoTip", (event, id) => {
      this.showTip(event, id);
    });
    AnnoEventsBus.$on("hideAnnoPopover", () => {
      this.hidePopover();
    });
    AnnoEventsBus.$on(
      "deleteCurrEntity",
      (uniqueid, is_attr, attr_label_info) => {
        this.deleteCurrEntity(uniqueid, is_attr, attr_label_info);
      }
    );
    AnnoEventsBus.$on("doChangeEntity", (data) => {
      this.doChangeEntity(data);
    });
    // 监听跨页面实体点击事件
    AnnoEventsBus.$on("crossPageEntityClick", (data) => {
      this.handleCrossPageEntityClick(data);
    });
    AnnoEventsBus.$on("submitTask", (arg) => {
      this.submitTask(arg);
    });
    AnnoEventsBus.$on("invalidTask", (invalidStatus) => {
      this.invalidTask(invalidStatus);
    });
    AnnoEventsBus.$on("cancelTask", () => {
      this.cancelTask();
    });
    AnnoEventsBus.$on("repulseTask", () => {
      this.repulse();
    });
    AnnoEventsBus.$on("reworkTask", () => {
      this.rework();
    });
    AnnoEventsBus.$on("openMetadata", () => {
      this.metadata();
    });
    AnnoEventsBus.$on("changeMaster", (data) => {
      this.sourceList = data || [];
    });
    AnnoEventsBus.$on("doSaveAnnotate", (annotateStr, multiple) => {
      this.doSaveAnnotate(annotateStr, multiple);
    });
    // 监听实体标签加载完成事件
    AnnoEventsBus.$on("entityLabelsLoaded", () => {
      this.checkAndSwitchToDiscussion();
    });
    // 设置弹框开关状态
    this.$store.commit("anno/setAnnoTypeSwitch", this.annoTypeSwitch);
    this.$store.commit("anno/setBatchSwitch", this.batchSwitch);
    // 获取文书
    window.addEventListener("keydown", this.handleKeydown);
    window.addEventListener("keyup", this.handleKeyup);
  },
  destroyed() {
    window.removeEventListener("keydown", this.handleKeydown);
    window.removeEventListener("keyup", this.handleKeyup);
    AnnoEventsBus.$off("hied-popover");
    AnnoEventsBus.$off("popoverStyles");
    AnnoEventsBus.$off("taskIdChanged");
    AnnoEventsBus.$off("switchAnnoType");
    AnnoEventsBus.$off("submitAnnoData");
    AnnoEventsBus.$off("showAnnoTip");
    AnnoEventsBus.$off("hideAnnoPopover");
    AnnoEventsBus.$off("deleteCurrEntity");
    AnnoEventsBus.$off("doChangeEntity");
    AnnoEventsBus.$off("crossPageEntityClick");
    AnnoEventsBus.$off("submitTask");
    AnnoEventsBus.$off("invalidTask");
    AnnoEventsBus.$off("cancelTask");
    AnnoEventsBus.$off("repulseTask");
    AnnoEventsBus.$off("reworkTask");
    AnnoEventsBus.$off("openMetadata");
    AnnoEventsBus.$off("changeMaster");
    AnnoEventsBus.$off("doSaveAnnotate");
    AnnoEventsBus.$off("entityLabelsLoaded");
  },
  directives: {
    ClickOutside
  },
  updated() {
    const attribute = this.$refs.attribute;
    if (attribute && attribute.labels) {
      if (!attribute.activeName && attribute.labels.length > 0) {
        this.$refs.attribute.activeName = attribute.labels[0].field;
      }
    }
  },
  computed: {
    currAttrId: function () {
      return trimStr(this.$store.state.attr.currAttrId);
    },
    currAttrAnnoId() {
      return this.$store.state.attr.currAnnoId;
    },
    showQuestionBtn() {
      return (
        this.$store.state.anno.navbar.taskStatus === "已合格" &&
        !this.$store.state.anno.annotation.preEntity &&
        !!this.$store.state.anno.annotation.currAnnoUniId &&
        !this.isHistoryMode
        // 无论在实体标注页面还是属性标注页面，只要选中了实体或属性都可以提问
        // 历史版本模式下不显示提问按钮
      );
    },
    roleId() {
      return this.$store.state.user.roleId;
    },
    annotation() {
      return this.$store.state.anno.annotation;
    },
    editable() {
      return this.$store.state.anno.editable;
    },
    isHistoryMode() {
      return this.$store.state.anno.historyMode.enabled;
    },
    historyModeData() {
      return this.$store.state.anno.historyMode;
    },
    continuousAnnotations() {
      return this.$store.state.anno.continuousAnnotations;
    },
    source() {
      return this.$store.state.anno.annotation.source;
    },
    sourceChangeTimes() {
      return this.$store.state.anno.annotation.sourceChangeTimes;
    },
    isAttributeAnno() {
      // 是否为属性标注
      return this.activeName === "attribute";
    },
    isOriginal() {
      const s = this.$store.state.anno.annotation.source;
      return s + "" === "1";
    },
    currAnnoUniId() {
      // 当前选中的实体标注id
      return this.$store.state.anno.annotation.currAnnoUniId;
    },
    hasSelectTag() {
      const uid = this.$store.state.anno.annotation.currAnnoUniId;
      if (uid) {
        const s = this.$store.state.anno.annotation.source;
        if (s + "" === "1") {
          // 非预标注
          return true;
        } else {
          // 预标注判断是否已经被导入
          const domObj = document.querySelector(
            `.tag-section span.tag[uniqueid="${uid}"]`
          );
          if (domObj) {
            return domObj.className.includes("-gray");
          } else {
            return false;
          }
        }
      } else {
        return false;
      }
    }
  },
  methods: {
    // 检查是否需要切换到讨论区（仅首次加载时执行）
    checkAndSwitchToDiscussion() {
      // 检查URL参数中是否包含discussionId，并且还没有执行过自动跳转
      const discussionId = this.$route.query.discussionId;
      if (discussionId && !this.hasAutoSwitchedToDiscussion) {
        // 标记已经执行过自动跳转，避免后续重复执行
        this.hasAutoSwitchedToDiscussion = true;

        // 实体标注组件已经加载完成，可以安全切换到讨论区
        this.$nextTick(() => {
          this.annoTypeSwitch = "讨论区";
          this.changeAnnoSwitch();
        });
      }
    },

    openQuestionDialog() {
      let uniId = trimStr(this.currAnnoUniId);
      if (!uniId) {
        this.$message({
          message: "请选中一个实体或属性！",
          type: "error"
        });
        return false;
      }

      // 判断是否为属性提问
      this.isAttributeQuestion = this.$store.state.anno.annotation.isAttr;
      this.questionDialogTitle = this.isAttributeQuestion
        ? "提问属性"
        : "提问实体";

      const loading = this.$loading({
        lock: true
      });

      // 重置弹窗数据
      this.questionAnnotateVal = "";
      this.questionEntityNames = "";
      this.highlightedContent = "";
      this.contextContent = "";
      this.contextWithHighlight = "";
      this.labelRules = [];
      this.selectedRule = "";
      this.currentQuestionData = null;

      // 获取实体或属性的详细信息
      this.$http({
        url: this.$http.adornUrl(
          `/comment/getEntityOrAttributeForQuestion?uniqueId=${uniId}`
        ),
        method: "get"
      })
        .then(({ data }) => {
          if (data.code !== 0) {
            loading.close();
            this.$message({
              message: data.msg,
              type: "error",
              duration: 10000
            });
            return false;
          }

          let resultInfo = data.data;
          this.currentQuestionData = resultInfo;

          // 设置高亮内容
          if (
            resultInfo.allEntityNames &&
            resultInfo.allEntityNames.length > 0
          ) {
            this.highlightedContent = resultInfo.allEntityNames
              .map(
                (name) =>
                  `<span style="background-color: yellow; padding: 2px 4px; border-radius: 2px;">${name}</span>`
              )
              .join(" / ");
          }

          // 获取段落上下文（这里需要根据实际情况实现）
          this.getContextContent(resultInfo);

          // 获取标签规则
          this.getLabelRules(resultInfo);

          loading.close();
          this.questionDialogVisible = true;
        })
        .catch((error) => {
          loading.close();
          console.error("获取实体或属性信息失败:", error);
          this.$message({
            message: "获取信息失败，请重试",
            type: "error"
          });
        });
    },

    // 获取段落上下文内容
    getContextContent(entityData) {
      try {
        let contextText = "";
        let contextWithHighlight = "";

        // 尝试从DOM中获取段落上下文
        const currentUniqueId = this.currAnnoUniId;
        if (currentUniqueId) {
          // 查找当前选中的span元素
          const spanElements = document.querySelectorAll(
            `span[annoid="${entityData.id}"]`
          );
          spanElements.forEach((spanElement) => {
            if (spanElement) {
              // 找到包含该span的p标签
              const pElement = spanElement.closest("p");
              if (pElement) {
                // 获取段落的HTML内容
                const pElementHTML = pElement.innerHTML;
                contextText = pElement.innerText || pElement.textContent || "";

                // 获取当前选中实体的annoid
                const currentAnnoId = spanElement.getAttribute("annoid");

                if (currentAnnoId && pElementHTML) {
                  // 处理HTML片段，只高亮当前选中的实体
                  contextWithHighlight =
                    contextWithHighlight +
                    "<p>" +
                    this.highlightSpecificEntity(pElementHTML, currentAnnoId);
                } else {
                  contextWithHighlight = contextText;
                }
              }
            }
          });
        }

        // 如果没有找到上下文，使用默认文本
        if (!contextText) {
          contextText = "无法获取段落上下文";
          contextWithHighlight = contextText;
        }

        this.contextContent = contextText;
        this.contextWithHighlight = contextWithHighlight;
      } catch (error) {
        console.error("获取段落上下文失败:", error);
        this.contextWithHighlight = "获取段落上下文失败";
        this.contextContent = "";
      }
    },

    // 高亮特定实体的方法
    highlightSpecificEntity(htmlContent, targetAnnoId) {
      try {
        // 创建一个临时的DOM元素来解析HTML
        const tempDiv = document.createElement("div");
        tempDiv.innerHTML = htmlContent;

        // 查找所有的span标签
        const spanElements = tempDiv.querySelectorAll("span");

        spanElements.forEach((span) => {
          const annoId = span.getAttribute("annoid");

          if (annoId === targetAnnoId) {
            // 当前选中的实体，添加黄色背景高亮
            span.style.backgroundColor = "yellow";
            span.style.padding = "1px 2px";
            span.style.borderRadius = "2px";
          } else if (annoId) {
            // 其他实体，保留文本但移除span标签样式
            const textContent = span.textContent || span.innerText;
            span.outerHTML = textContent;
          }
        });

        return tempDiv.innerHTML;
      } catch (error) {
        console.error("高亮特定实体失败:", error);
        // 如果处理失败，返回原始文本
        const tempDiv = document.createElement("div");
        tempDiv.innerHTML = htmlContent;
        return tempDiv.textContent || tempDiv.innerText || htmlContent;
      }
    },

    // 获取标签规则
    getLabelRules(entityData) {
      if (!entityData.labelId && !entityData.attributeLabel) {
        return;
      }

      // 根据标签ID获取对应的标签名称，然后获取规则
      let labelName = undefined;
      if (entityData.labelId) {
        labelName = this.$store.state.anno.currLabelMap.get(
          entityData.labelId.toString()
        ).name;
      } else {
        labelName = entityData.attributeLabel.name;
      }

      if (!labelName) {
        return;
      }

      // 调用后端接口获取标签规则
      this.$http({
        url: this.$http.adornUrl("/label/entity/getLabelRuleExample"),
        method: "get",
        params: this.$http.adornParams({
          projectId: this.$store.state.anno.annotation.projectId,
          labelName: labelName
        })
      }).then(({ data }) => {
        if (data && data.code === 0 && data.data) {
          // 解析markdown内容，提取规则列表
          this.parseLabelRules(data.data);
        }
      });
    },

    // 解析标签规则markdown内容
    parseLabelRules(markdownContent) {
      if (!markdownContent) {
        return;
      }

      const lines = markdownContent.split("\n");

      const rules = [];

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        // 匹配规则标题（### 规则X）
        if (line.match(/^###\s+\d+/) || line.match(/^###\s+.+/)) {
          rules.push(line.replace(/^###\s+/, ""));
        }
      }
      this.labelRules = rules;
    },
    doSaveQuestion() {
      if (!this.questionAnnotateVal.trim()) {
        this.$message({
          message: "请输入提问内容！",
          type: "warning"
        });
        return;
      }

      this.$confirm(
        `确定要提交当前${this.isAttributeQuestion ? "属性" : "实体"}提问吗?`,
        "提示",
        {
          confirmButtonText: "提交",
          cancelButtonText: "取消",
          type: "warning"
        }
      ).then(() => {
        this.submitQuestion();
      });
    },

    // 提交提问到新的Comment接口
    submitQuestion() {
      const loading = this.$loading({
        lock: true
      });

      // 构建提问数据
      const questionData = {
        projectId: this.$store.state.anno.annotation.projectId,
        batchId: this.$store.state.anno.annotation.batchId,
        noteId: this.$store.state.anno.annotation.noteId,
        taskId: this.$store.state.anno.annotation.taskId,
        questionText: this.questionAnnotateVal,
        violateRule: this.selectedRule || null,
        entityId: this.currentQuestionData ? this.currentQuestionData.id : null,
        isAttributeQuestion: this.isAttributeQuestion,
        currentUniqueId: this.currAnnoUniId
      };

      // 设置相关的实体和属性ID
      if (this.currentQuestionData) {
        if (this.isAttributeQuestion) {
          // 属性提问
          questionData.attributeIds = [this.currentQuestionData.id];
          // 如果属性关联了实体，也添加实体ID
          if (this.currentQuestionData.entityId) {
            questionData.entityIds = [this.currentQuestionData.entityId];
          }
        } else {
          // 实体提问
          questionData.entityIds = [this.currentQuestionData.id];
        }
      }

      this.$http({
        url: this.$http.adornUrl("/comment/submitQuestion"),
        method: "post",
        data: this.$http.adornData(questionData)
      })
        .then(({ data }) => {
          loading.close();
          if (data && data.code === 0) {
            this.$message({
              message: "提问提交成功！",
              type: "success"
            });
            this.questionDialogVisible = false;

            // 触发事件通知讨论区刷新
            AnnoEventsBus.$emit("questionSubmitted");

            // 确保清理loading状态
            this.$nextTick(() => {
              this.onQuestionDialogClose();
            });
          } else {
            this.$message({
              message: data.msg || "提问提交失败！",
              type: "error"
            });
          }
        })
        .catch(() => {
          loading.close();
          this.$message({
            message: "提问提交失败！",
            type: "error"
          });
        });
    },

    // 关闭提问弹窗
    closeQuestionDialog() {
      this.questionDialogVisible = false;
      this.onQuestionDialogClose();
    },

    // 提问弹窗关闭事件处理
    onQuestionDialogClose() {
      // 确保清理所有loading状态
      this.$nextTick(() => {
        // 移除可能存在的loading遮罩
        const loadingElements = document.querySelectorAll(".el-loading-mask");
        loadingElements.forEach((el) => {
          if (el.parentNode) {
            el.parentNode.removeChild(el);
          }
        });
      });
    },

    doSaveAnnotate(annotateStr, multiple, questionFlag) {
      questionFlag = !!questionFlag;
      const loading = this.$loading({
        lock: true
      });
      const currAnnoUniId = this.currAnnoUniId;
      this.$http({
        url: this.$http.adornUrl("/note/updateEntityAnnotate"),
        method: "post",
        data: this.$http.adornData({
          entityId: currAnnoUniId,
          roleId: this.roleId,
          annotate: annotateStr,
          multiple: !!multiple,
          questionFlag: questionFlag
        })
      })
        .then(({ data }) => {
          loading.close();
          if (data && data.code === 0) {
            AnnoEventsBus.$emit(
              "updateCurrDoc",
              data.data,
              null,
              currAnnoUniId
            );
          }

          if (questionFlag) {
            this.questionDialogVisible = false;
          }
        })
        .catch(() => {
          loading.close();
          this.$message({
            message: "更新批注信息失败！",
            type: "error"
          });
        });
    },
    switchAnnoType(type) {
      // 首先从属性标注切换到实体标注
      this.annoTypeSwitch = "实体标注";
      this.changeAnnoSwitch();
      // 等待页面渲染完成后，修改activeName
      this.$nextTick(() => {
        // 此组件通过watch监听activeName字段变化
        if (type === "实体标注") {
          this.activeName = "relationship";
        } else {
          this.activeName = "entity";
        }
      });
    },
    initEntityDragInfo(target) {
      const entityId = target.getAttribute("annoid");
      const dragInfo = {};
      dragInfo.id = entityId;
      dragInfo.labelId = target.getAttribute("labelid");
      dragInfo.isAttr = target.getAttribute("is_attr");

      const spans = document.querySelectorAll(
        `span[annoid='${entityId}'][basetag="1"]`
      );
      const spanTexts = [];
      spans.forEach((span) => {
        const text = span.textContent;
        spanTexts.push(text);
      });
      dragInfo.text = joinArray(spanTexts, " / ", 35);
      return dragInfo;
    },
    popClickAddAttr(target) {
      const dragInfo = this.initEntityDragInfo(target);
      dragInfo.currAttrId = this.currAttrId;
      AnnoEventsBus.$emit("doAddAttrEv", dragInfo);
      const loading = this.$loading({
        lock: false
      });
      sleep(800).then(() => {
        loading.close();
      });
    },
    isEntityByUid() {
      let id = this.currAttrAnnoId;
      const target = document.querySelector(
        `span.tag[annoid='${id}'][basetag="1"]`
      );
      if (!target) {
        return false;
      }
      return target.getAttribute("is_attr") === "0";
    },
    popChangeEntity: function (event) {
      const target = event.target;
      if (!(target.tagName === "SPAN" && target.classList.contains("tag"))) {
        return;
      }
      const currAnnoid = trimStr(target.getAttribute("annoid"));
      let isPre = Number(this.source) === 2;
      let addAttrAction =
        !isPre &&
        this.isAttributeAnno &&
        this.currAttrAnnoId &&
        this.isEntityByUid() &&
        this.currAttrId &&
        target.getAttribute("is_attr") === "1";
      if (addAttrAction) {
        // 弹窗中点击属性标注，给对应实体添加属性
        this.popClickAddAttr(target);
      } else {
        // 标注切换
        if (this.activeName === "entityList") {
          // 点击实体同步实体清单树选中项
          AnnoEventsBus.$emit("doChangeEntityTree", currAnnoid);
        }
        this.doChangeEntity({
          annoid: currAnnoid,
          uniqueid: trimStr(target.getAttribute("uniqueid")),
          labelId: trimStr(target.getAttribute("labelid")),
          content: trimStr(target.getAttribute("title")),
          annotate: trimStr(target.getAttribute("annotate")),
          is_attr: trimStr(target.getAttribute("is_attr")),
          attr_ids: target.getAttribute("attr_ids"),
          isPre: target.classList.contains("black"),
          showTip: false
        });
      }
    },
    doChangeEntity(data) {
      const {
        annoid,
        uniqueid,
        labelId,
        content,
        annotate,
        is_attr,
        attr_ids,
        isPre,
        showTip
      } = data;
      this.$store.dispatch("attr/setCurrAnnoId", annoid).then(() => {
        if (is_attr !== "1" && !isPre) {
          AnnoEventsBus.$emit("entityClick4Attr", content);
        }
      });
      this.$store.commit("anno/setAnnotate", trimStr(annotate));
      this.currAnnoId = annoid;
      // 调用浏览器的复制命令
      this.$tools.copy(trimStr(content));
      // 添加选中样式
      const all = document.querySelectorAll(".tag-section span.select");
      for (let i = 0; i < all.length; i++) {
        all[i].classList.remove("select");
      }

      // 高亮选中实体对应的属性
      if (attr_ids) {
        const attrDomsAll = document.querySelectorAll(
          `.tag-section span[is_attr="1"]`
        );
        // 根据当前选中实体对应的属性id拼接字符串，高亮对应属性标注
        const attr_id_array = attr_ids.split(",");
        const attr_id_length = attr_id_array.length;
        for (let i = 0; i < attr_id_length; i++) {
          for (let j = 0; j < attrDomsAll.length; j++) {
            const item = attrDomsAll[j];
            if (attr_id_array[i] === item.getAttribute("annoid")) {
              item.classList.add("select");
            }
          }
        }
      }

      if (annoid) {
        const entityDoms = document.querySelectorAll(
          `.tag-section span[annoid="${annoid}"]`
        );
        const entity_id_array = [];
        for (let i = 0; i < entityDoms.length; i++) {
          let item = entityDoms[i];
          item.classList.add("select");
          let entity_ids = item.getAttribute("entity_ids");
          if (entity_ids) {
            entity_id_array.push(...entity_ids.split(","));
          }
        }
        let entity_id_length = entity_id_array.length;
        if (entity_id_length > 0) {
          for (let i = 0; i < entity_id_length; i++) {
            let entityAnnos = document.querySelectorAll(
              `.tag-section span[annoid="${entity_id_array[i]}"]`
            );
            for (let i = 0; i < entityAnnos.length; i++) {
              entityAnnos[i].classList.add("select");
            }
          }
        }
      }

      // 判断是否是预标注的实体
      this.$store.commit("anno/setPreEntity", isPre);

      // 更新Vuex中当前选中的实体数据
      const isAttrFlag = isAttrTag(is_attr);
      const anno = {
        uniqueid: uniqueid,
        labelId: labelId,
        isAttr: isAttrFlag
      };
      this.$store.commit("anno/setAnno", anno);
      if (this.isAttributeAnno) {
        // 属性标注
        if (isAttrFlag) {
          this.$refs.refAttribute.initAttrByLabel(null, null);
        } else {
          this.$refs.refAttribute.initAttrByLabel(labelId, annoid);
        }
      } else if (this.activeName === "entity") {
        // 通知UMLSConcept组件重置UMLSConcept
        // AnnoEventsBus.$emit("resetUMLSConcept");
        // 刷新右侧一级和二级标签
        AnnoEventsBus.$emit("refreshLabels", uniqueid);
      }

      // 滚动到选中的实体位置，确保用户能看到
      const selectedTarget = document.querySelector(
        `.tag-section span[uniqueid="${uniqueid}"]`
      );
      if (selectedTarget) {
        selectedTarget.scrollIntoView({
          behavior: "smooth",
          block: "center"
        });
      }

      if (showTip) {
        // 弹出提示框
        this.showTip(event, uniqueid);
      }
    },
    mouseoverListener: function (event) {
      this.mouseOverEvent = event;
    },
    mouseleaveListener: function (event) {
      this.mouseOverEvent = "";
    },
    dragPop(ev) {
      const target = ev.target;
      const annoid = target.getAttribute("annoid");
      ev.dataTransfer.setData("id", annoid);
      ev.dataTransfer.setData("text", trimStr(target.innerText));
      ev.dataTransfer.setData(
        "labelId",
        trimStr(target.getAttribute("labelid"))
      );
    },
    handleKeyup: function (event) {
      const e = getCurrEvent(event);
      if (!e) {
        return;
      }
      if (this.deletAttrDialogVisible === true && e.key === "Enter") {
        this.doForceDeleteAttr();
      } else {
        this.currKeyEvent = e;
        this.isCtrlPressed = this.getCtrlPressedOrNot(e);
        this.$store.commit("anno/setIsCtrlPressed", this.isCtrlPressed);
        if (e.keyCode === 27) {
          // Esc按键
          this.styles.isShow = false;
        } else if (e.keyCode === 46 || e.keyCode === 8) {
          // 46 = Delete  8 = BackSpace 点击按键删除标注
          // 判断弹窗中标注是否选中，若选中则删除该标注
          if (this.mouseOverEvent === "") {
            return;
          }
          const target = this.mouseOverEvent.target;
          const uniqueid = trimStr(target.getAttribute("uniqueid"));
          if (
            !(
              target.tagName === "SPAN" &&
              target.classList.contains("tag") &&
              !!uniqueid
            )
          ) {
            return;
          }
          const is_attr = trimStr(target.getAttribute("is_attr"));
          const attr_label_info = trimStr(
            target.getAttribute("attr_label_info")
          );
          this.deleteCurrEntity(uniqueid, is_attr, attr_label_info);
        } else {
          // 预标注选项卡中，点击i键将选中的实体预标注和关联的属性预标注导入原文
          if (Number(this.source) === 2 && e.keyCode === 73) {
            AnnoEventsBus.$emit("keyPressImportToOriginal");
          }
        }
      }
    },
    // 检查标注权限
    hasMarkPermission(allowFlag) {
      const availableTab =
        allowFlag ||
        this.activeName === "entity" ||
        this.activeName === "attribute";
      if (!this.editable || !availableTab) {
        if (availableTab) {
          this.$message({
            message: "当前状态无编辑权限！",
            type: "error"
          });
        }
        return false;
      }
      return true;
    },
    deleteCurrEntity(uniqueid, is_attr, attr_label_info) {
      if (!this.hasMarkPermission(this.activeName === "entityList")) {
        return;
      }
      if (!this.isOriginal) {
        this.$message({
          message: "无法删除非原文数据",
          type: "warning",
          duration: 1500
        });
        return;
      }
      if (this.batchSwitch) {
        this.$confirm("确认批量删除该实体？", "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            let hasAttr = false;
            if (!attr_label_info) {
              const target = document.querySelector(
                `.tag-section span[uniqueid='${uniqueid}'][basetag="1"]`
              );
              const batch_annotate_id =
                target.getAttribute("batch_annotate_id");
              if (batch_annotate_id) {
                const spans = document.querySelectorAll(
                  `.tag-section span[batch_annotate_id='${batch_annotate_id}'][basetag="1"]`
                );
                const l = spans.length;
                for (let i = 0; i < l; i++) {
                  if (spans[i].getAttribute("attr_label_info")) {
                    hasAttr = true;
                    break;
                  }
                }
              }
            } else {
              hasAttr = true;
            }
            this.deleteAttrWithDialog(uniqueid, is_attr, hasAttr);
          })
          .catch(() => {});
      } else {
        this.deleteAttrWithDialog(uniqueid, is_attr, attr_label_info);
      }
    },
    deleteAttrWithDialog(uniqueid, is_attr, attr_label_info) {
      this.uniqueid_to_delete = uniqueid;
      this.is_attr_to_delete = is_attr;
      this.annoDeleteForm.deleteAttrAsWell = false;
      if (!isAttrTag(is_attr) && attr_label_info) {
        // 在弹窗中删除（已废弃，使用$confirm替代）
        // this.annoDeleteDialogVisible = true;
        this.$confirm("是否同时删除对应的属性标注？", "删除标注", {
          type: "warning",
          distinguishCancelAndClose: true,
          confirmButtonText: "删除实体和属性",
          confirmButtonClass: "el-button--danger",
          cancelButtonText: "仅删除实体",
          cancelButtonClass: ""
        })
          .then(() => {
            this.annoDeleteForm.deleteAttrAsWell = true;
            this.deleteEntity();
          })
          .catch((action) => {
            //取消按钮
            if (action === "cancel") {
              this.deleteEntity();
            }
          });
      } else {
        this.deleteEntity();
      }
    },
    handleKeyDown(event) {
      // console.log(1, event);
    },
    doForceDeleteAttr() {
      this.deletAttrDialogVisible = false;
      this.deleteEntity(true);
    },
    deleteEntity(forceDelAttr) {
      this.annoDeleteDialogVisible = false;
      const uniqueid = this.uniqueid_to_delete;
      const is_attr = this.is_attr_to_delete;
      if (!uniqueid) {
        return;
      }
      const multiple = this.batchSwitch;
      this.commAnnoLoading = true;
      this.$http({
        url: this.$http.adornUrl("/note/deleteEntity"),
        method: "post",
        data: this.$http.adornData({
          entityId: uniqueid,
          roleId: this.roleId,
          deleteAttrAsWell: this.annoDeleteForm.deleteAttrAsWell,
          multiple: multiple,
          forceDelAttr: !!forceDelAttr
        })
      })
        .then(({ data }) => {
          this.commAnnoLoading = false;
          if (data && data.code !== 0) {
            // 存在使用中的属性，需要确定是否强制删除
            if (data.code === 2001) {
              let tagDom = document.querySelector(
                `span.tag[uniqueid="${uniqueid}"][basetag="1"]`
              );
              if (tagDom) {
                this.deleteAttrName = tagDom.textContent;
              }
              this.deletAttrDialogVisible = true;
              this.usedEntities = data.usedEntities || [];
            }
            return;
          }
          this.$store.commit("anno/addSourceChangeTimes");
          if (!isAttrTag(is_attr)) {
            AnnoEventsBus.$emit("clearAttrData");
          } else {
            AnnoEventsBus.$emit("doRefreshAttrByLabel");
          }

          AnnoEventsBus.$emit("hideAnnoPopover");
          this.$store.commit("anno/setCurrAnno", null);
          // 重新刷新页面
          if (multiple) {
            AnnoEventsBus.$emit("changeSource");
            // 如果不是批量删除，页面直接删除即可
          } else {
            // 刷新当前来源页面，回显灰色背景色
            if (!this.isOriginal) {
              AnnoEventsBus.$emit("changeSource");
            } else {
              AnnoEventsBus.$emit("updateCurrDoc", data.data);
            }
          }
          // 删除右侧二级标签选中状态
          // AnnoEventsBus.$emit("resetLabels");
          AnnoEventsBus.$emit("resetUMLSConcept");
          // 清空当前选中的标签
          this.$store.commit("anno/setCurrAnno", null);
        })
        .catch(() => {
          this.commAnnoLoading = false;
        });
    },
    handleKeydown: function (event) {
      const e = getCurrEvent(event);
      if (!e) {
        return;
      }
      // console.log(e.ctrlKey, e.altKey, e.shiftKey);
      this.currKeyEvent = e;
      this.isCtrlPressed = this.getCtrlPressedOrNot(e);
      this.$store.commit("anno/setIsCtrlPressed", this.isCtrlPressed);
    },
    getCtrlPressedOrNot(e) {
      return e.ctrlKey && !(e.altKey || e.shiftKey);
    },
    changeAnnoSwitch() {
      this.$store.commit("anno/setAnnoTypeSwitch", this.annoTypeSwitch);
      let val = "";
      if (this.annoTypeSwitch === "属性标注") {
        val = "attribute";
      } else if (this.annoTypeSwitch === "实体清单") {
        val = "entityList";
      } else if (this.annoTypeSwitch === "讨论区") {
        val = "discussion";
      } else {
        val = "entity";
      }
      this.activeName = val;
    },
    changeBatchSwitch() {
      this.$store.commit("anno/setBatchSwitch", this.batchSwitch);
    },
    /**
     * 点击空白区域隐藏弹框
     */
    hide(event) {
      const selection = window.getSelection();
      if (selection.focusNode && selection.focusNode.tagName === "P") return;
      const target = event.target;
      if (target.tagName === "SPAN" && target.classList.contains("tag")) return;

      this.styles.isShow = false;
    },
    hidePopover() {
      this.styles.isShow = false;
    },
    /**
     * 校验当前用户和文书状态下，是否有编辑权限
     */
    /* checkPermission() {
      console.log("checkPermission", this.roleId, this.currentNote);
      let tempEditable = true;
      // 只有 标注员是标注中 和 审核员是审核中 内容才能编辑
      if (
        !(
          (this.roleId === this.$RoleEnum.annotator &&
            this.currentNote.step === this.$NoteEnum.noting) ||
          (this.roleId === this.$RoleEnum.auditor &&
            this.currentNote.step === this.$NoteEnum.reviewing)
        )
      ) {
        tempEditable = false;
      }
      // 项目管理员、项目观察员只能看, 不能编辑
      if (
        this.roleId === this.$RoleEnum.projectAdmin ||
        this.roleId === this.$RoleEnum.projectWatcher
      ) {
        tempEditable = false;
      }
      this.$store.commit("anno/setEditable", tempEditable);
    }, */
    /**
     * 获取文书
     */
    getArticle() {
      this.clearVuex();
      this.articleFlag = false;

      // 如果是历史版本模式，先加载历史数据
      if (this.isHistoryMode) {
        this.loadHistoricalData();
        return;
      }

      const annotatorId = trimStr(this.$route.query.annotatorId);
      /* const currAuditAnnotatorId = sessionStorage.getItem(
        "currAuditAnnotatorId"
      );
      if (annotatorId && !currAuditAnnotatorId) {
        // 判断是否审核员从任务列表点击进入的此界面
        if (
          this.roleId === this.$RoleEnum.annotator ||
          this.roleId === this.$RoleEnum.auditor
        ) {
          this.$router.push({
            path: "/annotask-list"
          });
        } else {
          this.$router.go(-1);
        }
        return;
      } */
      const params = {
        // annotatorId: currAuditAnnotatorId ? currAuditAnnotatorId : null,
        annotatorId: annotatorId,
        taskId: this.taskId
      };
      this.$http({
        url: this.$http.adornUrl(
          `/note/getNoteById/${this.noteId}/${this.roleId}`
        ),
        method: "get",
        params: this.$http.adornParams(params)
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.currentNote = data.data.note;
          if (this.currentNote !== null) {
            this.projectEnv = {
              projectId: this.currentNote.projectId,
              batchId: this.currentNote.batchId
            };
            this.$store.commit("anno/setBatch", this.projectEnv);

            this.documentId = this.currentNote.documentId;
            this.$store.commit("anno/setNoteId", {
              noteId: this.noteId,
              taskId: this.taskId
            });

            this.$store.commit("anno/setArticleId", this.currentNote.articleId);
            this.$store.commit(
              "anno/setDocumentId",
              this.currentNote.documentId
            );

            // this.annotatorName = data.data.annotator;
            this.$store.commit("anno/setActiveTab", this.activeName);

            // 判断标记权限 校验权限
            this.sourceList = data.data.sources || [];
            this.articleFlag = true;
            // this.checkPermission();
          }
        }
      });
    },
    /**
     * 获取下一篇文书
     */
    getNextArticle() {
      this.$emit("nextArticle");
    },
    /**
     * 完成
     */
    submitTask(nextArticle) {
      let notUsedAttr = document.querySelectorAll(
        `.tag-main-article span[class="tag tag-attr"]`
      );
      const hasNotUsedAttr = notUsedAttr && notUsedAttr.length > 0;
      this.$confirm(
        hasNotUsedAttr ? "存在未使用的属性标注" : "确定完成当前文书？",
        "提示",
        {
          confirmButtonText: hasNotUsedAttr ? "忽略" : "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      )
        .then(() => {
          AnnoEventsBus.$emit("submitAttr");
          this.$http({
            url: this.$http.adornUrl(`/task/submitTask`),
            method: "get",
            params: this.$http.adornParams({
              roleId: this.roleId,
              taskId: this.taskId
            })
          }).then(({ data }) => {
            if (data && data.code === 0) {
              if (nextArticle) {
                // 获取下一篇文书
                const loading = this.$loading({
                  lock: true
                });
                this.getNextArticle();
              } else {
                this.$router.replace({
                  path: "/annotask/detail",
                  query: {
                    batchId: Number(this.$route.query.batchId),
                    noteId: Number(this.$route.query.noteId),
                    editable: false,
                    annotatorId: this.$route.query.annotatorId,
                    fromType: trimStr(this.$route.query.fromType)
                  }
                });
                window.location.reload();
              }
            }
          });
        })
        .catch(() => {});
    },
    /**
     * 废弃 / 恢复 文书
     */
    invalidTask(invalid) {
      this.$confirm(`确定${invalid ? "废弃" : "恢复"}当前文书？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.$http({
            url: this.$http.adornUrl(`/task/invalid`),
            method: "get",
            params: this.$http.adornParams({
              invalid: invalid,
              taskId: this.taskId
            })
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message.success({
                message: `${invalid ? "废弃" : "恢复"}成功`
              });
              this.$store.commit("anno/setNavbarInvalid", {
                invalid: invalid
              });
            }
          });
        })
        .catch(() => {});
    },
    /**
     * 打回重标（审核员）
     */
    repulse() {
      this.$refs.repulseDialog.getData();
    },
    /**
     * 查看元数据
     */
    metadata() {
      this.$refs.metadataDialog.init();
    },
    /**
     * 重新标注/审核（标注员/审核员）
     */
    rework() {
      this.$confirm(
        "确定重新" +
          (this.roleId === this.$RoleEnum.auditor ? "审核" : "标注") +
          "该文书？",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      )
        .then(() => {
          this.$http({
            url: this.$http.adornUrl(`/task/rework`),
            method: "get",
            params: this.$http.adornParams({
              roleId: this.roleId,
              taskId: this.taskId
            })
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$router.replace({
                path: "/annotask/detail",
                query: {
                  batchId: Number(this.$route.query.batchId),
                  noteId: Number(this.$route.query.noteId),
                  editable: true,
                  annotatorId: this.$route.query.annotatorId,
                  fromType: trimStr(this.$route.query.fromType)
                }
              });
              window.location.reload();
            }
          });
        })
        .catch(() => {});
    },
    /**
     * 取消标注/审核当前文书
     */
    cancelTask() {
      this.$confirm(
        "确定取消" +
          (this.roleId === this.$RoleEnum.auditor ? "审核" : "标注") +
          "当前文书？您对该文书的所有操作历史都将会被清空",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      )
        .then(() => {
          this.$http({
            url: this.$http.adornUrl(`/task/cancelTask`),
            method: "get",
            params: this.$http.adornParams({
              roleId: this.roleId,
              taskId: this.taskId
            })
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$router.push({
                path: "/annotask-list"
              });
            }
          });
        })
        .catch(() => {});
    },
    submitAttr() {
      AnnoEventsBus.$emit("submitAttr");

      // 保存是每个组件异步的，无法真提示，所有写假提醒，给用户心理安慰
      this.$message({
        message: "保存成功",
        type: "success",
        duration: 1500
      });
    },
    // 复制特殊符号
    copySymbol(data) {
      // 复制到剪贴板
      this.$tools.copy(data);
      this.$message.info({
        dangerouslyUseHTMLString: true,
        message: `${data}  <em>Copied!</em>`
      });
      this.visible = false;
    },
    /**
     * 清除Vuex中存在的过期缓存数据
     */
    clearVuex() {
      AnnoEventsBus.$emit("emptyLabel");

      this.$store.commit("anno/setLabelId", null);
      this.$store.commit("anno/setSource", { id: 1 });
      this.$store.commit("anno/setPreEntity", false);
      this.$store.commit("anno/setArticleId", "");
      this.$store.commit("anno/setDocumentId", "");
      this.$store.commit("anno/setNoteId", null);
      this.$store.commit("anno/setAnno", null);
      this.$store.commit("anno/setAnnotate", null);
      this.$store.commit("anno/setBatch", {});
      // this.annoTypeSwitch = false;
      // this.changeAnnoSwitch();
      // this.batchSwitch = false;
      // this.changeBatchSwitch();
      // this.styles.isShow = false;
    },
    judgeBatchSubmit(param, event) {
      if (this.batchSwitch) {
        const entityInfos = param.entityInfos;
        if (!entityInfos || entityInfos.length === 0) {
          this.$message({
            message: "划词数据不能为空！",
            type: "error"
          });
          return;
        }
        if (entityInfos.length > 1) {
          this.$message({
            message: "复合标注不能进行批量操作！",
            type: "error"
          });
          return;
        }
        //批量标注
        this.$confirm(
          "确认批量标注全文中的【" + entityInfos[0].content + "】？",
          "提示",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          }
        ).then(() => {
          param.multiple = true;
          this.submitContinuousAnnotate(param, event);
        });
      } else {
        this.submitContinuousAnnotate(param, event);
      }
    },
    // 提交标注信息
    submitContinuousAnnotate(param, event) {
      if (!this.hasMarkPermission()) {
        return;
      }
      const conAnno = this.$_.cloneDeep(param);
      // 设置当前角色id
      conAnno.roleId = this.roleId;
      conAnno.isAttr = this.isAttributeAnno ? 1 : 0;
      // this.commAnnoLoading = false;
      // const loadingInstance1 = Loading.service({ fullscreen: true });
      this.$http({
        url: this.$http.adornUrl("/note/addEntity"),
        method: "post",
        data: this.$http.adornData(conAnno)
      })
        .then(({ data }) => {
          // this.commAnnoLoading = false;
          if (data.msg === "success" && data.code === 0) {
            if (data.data) {
              // 设置当前选中的实体id
              conAnno.uniqueid = data.data.uniIds[data.data.uniIds.length - 1];
              AnnoEventsBus.$emit("updateCurrDoc", data.data.docAllInfo);
            }
          } else {
            AnnoEventsBus.$emit("updateCurrDoc", -1);
          }
          this.$store.commit("anno/setAnno", conAnno);
          // 通知标签组件重置标签
          // AnnoEventsBus.$emit("resetLabels");
          // 通知UMLSConcept组件重置UMLSConcept
          // AnnoEventsBus.$emit("resetUMLSConcept");
          const entityInfos = conAnno.entityInfos;
          AnnoEventsBus.$emit(
            "getUMLSConcept",
            entityInfos.map((x) => x.content).join(" ")
          );

          if (data && data.code === 0) {
            this.showTip(event, conAnno.uniqueid);
          }
        })
        .catch(() => {
          // this.commAnnoLoading = false;
          AnnoEventsBus.$emit("updateCurrDoc", -1);
        });
    },
    // 获取最终定位
    getPosAtPoint(event) {
      let range;
      let textNode;
      let offset;
      let pos = null;
      const scrollTop =
        document.documentElement.scrollTop || document.body.scrollTop;
      // standard
      if (document.caretPositionFromPoint) {
        range = document.caretPositionFromPoint(
          event.pageX,
          event.pageY - scrollTop
        );
        textNode = range.offsetNode;
        offset = range.offset;
      } else if (document.caretRangeFromPoint) {
        // WebKit
        range = document.caretRangeFromPoint(
          event.pageX,
          event.pageY - scrollTop
        );
        textNode = range.startContainer;
        offset = range.startOffset;
      }

      // only split TEXT_NODEs
      if (textNode.nodeType === 3) {
        const replacement = textNode.splitText(offset); // 截取鼠标位置之后的文本
        const spanElement = document.createElement("span"); // 创建标识
        spanElement.id = "flagDom"; // 设置标识类名
        spanElement.className = "tag";
        AnnoEventsBus.$emit("getScrollTop");
        textNode.parentNode.insertBefore(spanElement, replacement); // 插入标识
        const dom = document.getElementById("flagDom");
        pos = dom.getBoundingClientRect();
        pos.height = dom.offsetHeight; // 获取插入标识获得得一行高度
        dom.parentNode.removeChild(dom); // 获取到位置后移除掉标识
        textNode.parentNode.normalize(); // 合并文本节点
        AnnoEventsBus.$emit("setScrollTop");
      }
      return pos; // 返回位置
    },
    // 获得可视区域高度let
    getClientHeight() {
      let clientHeight = 0;
      if (document.body.clientHeight && document.documentElement.clientHeight) {
        clientHeight =
          document.body.clientHeight < document.documentElement.clientHeight
            ? document.body.clientHeight
            : document.documentElement.clientHeight;
      } else {
        clientHeight =
          document.body.clientHeight > document.documentElement.clientHeight
            ? document.body.clientHeight
            : document.documentElement.clientHeight;
      }
      return clientHeight;
    },
    popoverStyles(value, id) {
      // this.$nextTick(() => {});
      if (!this.$refs.drag) return;
      const dom = this.$refs.drag.$el;
      const popoverHeight = window
        .getComputedStyle(dom)
        .height.replace("px", "");
      const styles = { ...value };
      //  当底部距离不足以展示弹框，实体标注弹框位置 触发点的顶部
      if (popoverHeight >= styles.bottom) {
        styles.top = styles.top - styles.height - popoverHeight;
      }
      const allTag = document.querySelectorAll(`.tag-section span.tag`);
      const length = allTag.length;
      const tagsMap = new Map();
      const popoverAttrIdsMapp = new Map();
      const popoverAttrLabelInfoMap = new Map();
      for (let i = 0; i < length; i++) {
        const tagElement = allTag[i];
        const key = tagElement.getAttribute("annoid");
        if (key && !tagsMap.has(key)) {
          tagsMap.set(key, tagElement.getAttribute("class"));
          popoverAttrIdsMapp.set(key, tagElement.getAttribute("attr_ids"));
          popoverAttrLabelInfoMap.set(
            key,
            tagElement.getAttribute("attr_label_info")
          );
        }
      }
      // 实体标注弹框位置 页面左下角
      this.$http({
        url: this.$http.adornUrl("/note/getEntity"),
        method: "get",
        params: this.$http.adornParams({
          id: id
        })
      }).then(({ data }) => {
        if (data.code === 0) {
          const result = data.data;

          // 通知UMLSConcept组件重置UMLSConcept
          AnnoEventsBus.$emit("refreshLabels", id);

          let currAnnotate = "";
          let popoverData = result.allEntityInfos || [];
          // const popoverAttrData = result.attrInfo || {};
          // const popoverAttrLabelInfoMap = result.attrLabelInfoMap || {};
          // const popoverAttrIdsMapp = result.attrIdsMap || {};
          const popoverMap = new Map();
          for (let i = 0; i < popoverData.length; i++) {
            const annoid = popoverData[i].annoid;
            let item = popoverMap.get(annoid);
            if (!item) {
              item = {
                annoid: annoid,
                popCls: tagsMap.get(annoid),
                // attrList: popoverAttrData[annoid],
                attr_label_info: popoverAttrLabelInfoMap.get(annoid),
                attr_ids: popoverAttrIdsMapp.get(annoid),
                labelId: popoverData[i].labelId,
                is_attr: popoverData[i].isAttr,
                selected: false,
                selectedId: null,
                uniqueids: [],
                allContents: [],
                annotate: trimStr(popoverData[i].annotate)
              };
            }
            const currUniId = popoverData[i].uniqueid;
            item.uniqueids.push(currUniId);
            if (!item.selected) {
              item.selected = item.uniqueids.includes(id);
            }
            if (item.selected) {
              item.selectedId = id;
              currAnnotate = item.annotate;
            } else {
              item.selectedId = currUniId;
            }
            item.allContents.push(popoverData[i].content);
            popoverMap.set(annoid, item);
          }
          popoverData = [];
          for (let item of popoverMap.values()) {
            popoverData.push(item);
          }
          this.entityInfos = popoverData;
          this.$store.commit("anno/setAnnotate", currAnnotate);
          const popoverItemCount = popoverData.length;
          if (popoverItemCount > 0) {
            // 显示完全重复的或者连续标注
            styles.isShow =
              popoverItemCount > 1 || popoverData[0].uniqueids.length > 1;
          }

          this.styles = styles;
        }
      });
    },
    // 弹出提示框
    showTip(event, id) {
      this.$nextTick(() => {
        sleep(200).then(() => {
          // const target = event.target;
          const target = document.querySelector(
            `.tag-section span[uniqueid="${id}"]`
          );
          if (!target) {
            return;
          }
          let position = {};

          if (event.type === "click") {
            position = target.getBoundingClientRect();
          } else if (event.type === "mouseup") {
            position = this.getPosAtPoint(event);
          } else {
            return;
          }

          const { height, top, left, bottom } = position;
          const clientHeight = this.getClientHeight();

          // 实体标注弹框位置 触发点的底部
          const styles = {
            isShow: true,
            height: height,
            top: top + height + window.scrollY,
            left: left + window.scrollX,
            bottom: clientHeight - bottom,
            clientHeight
          };
          this.popoverStyles(styles, id);
        });
      });
    },

    // 恢复实体选中状态和样式
    restoreEntitySelection(uniqueId) {
      if (!uniqueId) return;

      // 查找对应的实体元素
      const target = document.querySelector(
        `.tag-section span[uniqueid="${uniqueId}"]`
      );
      if (!target) return;

      // 构建实体数据
      const changeData = {
        annoid: target.getAttribute("annoid"),
        uniqueid: target.getAttribute("uniqueid"),
        labelId: target.getAttribute("labelid"),
        content: target.innerText,
        annotate: target.getAttribute("annotate"),
        is_attr: target.getAttribute("is_attr"),
        attr_ids: target.getAttribute("attr_ids"),
        isPre: target.classList.contains("black"),
        showTip: false // 不显示提示框，只恢复选中状态
      };

      // 恢复选中样式
      this.doChangeEntity(changeData);
    },

    // 处理跨页面实体点击自动跳转
    handleCrossPageEntityClick(data) {
      // 如果当前不在实体标注页面，自动切换到实体标注页面
      if (this.activeName === "discussion") {
        // 从讨论区切换到实体标注页面
        this.annoTypeSwitch = "实体标注";
        this.changeAnnoSwitch();

        // 等待页面切换完成后，再处理实体选中
        this.$nextTick(() => {
          setTimeout(() => {
            this.doChangeEntity(data);
          }, 100);
        });
      } else {
        // 当前已在实体相关页面，直接处理
        this.doChangeEntity(data);
      }
    },

    // 加载历史版本数据
    async loadHistoricalData() {
      try {
        this.commAnnoLoading = true;

        // 继续正常的文书加载流程，历史模式过滤在后端进行
        const annotatorId = trimStr(this.$route.query.annotatorId);
        const params = {
          annotatorId: annotatorId,
          taskId: this.taskId
        };

        const noteResponse = await this.$http({
          url: this.$http.adornUrl(
            `/note/getNoteById/${this.noteId}/${this.roleId}`
          ),
          method: "get",
          params: this.$http.adornParams(params)
        });

        if (noteResponse.data && noteResponse.data.code === 0) {
          this.currentNote = noteResponse.data.data.note;
          if (this.currentNote !== null) {
            this.projectEnv = {
              projectId: this.currentNote.projectId,
              batchId: this.currentNote.batchId
            };
            this.$store.commit("anno/setBatch", this.projectEnv);

            this.documentId = this.currentNote.documentId;
            this.$store.commit("anno/setNoteId", {
              noteId: this.noteId,
              taskId: this.taskId
            });
            this.$store.commit("anno/setArticleId", this.currentNote.articleId);
            this.$store.commit(
              "anno/setDocumentId",
              this.currentNote.documentId
            );

            this.sourceList = noteResponse.data.data.sources || [];
            this.articleFlag = true;
          }
        } else {
          this.$message.error(noteResponse.data.msg || "加载文书数据失败");
        }
      } catch (error) {
        console.error("加载历史数据失败:", error);
        this.$message.error("加载历史数据失败");
      } finally {
        this.commAnnoLoading = false;
      }
    }
  },
  beforeDestroy() {
    this.clearVuex();
  },
  components: {
    EntityList,
    BaseCss,
    PreSelect,
    BaseArticle,
    AnnoEntity,
    DragPopover,
    AnnoRelationship,
    AnnoAttribute,
    Repulse,
    Metadata,
    DiscussionArea
  },
  watch: {
    isCtrlPressed: {
      handler(newValue, oldValue) {
        // Ctrl监听，按住不放时，实体不可拖动
        // console.log("watch Ctrl", newValue, this.continuousAnnotations);
        if (!newValue) {
          this.rightPartStyle = "";
          if (this.continuousAnnotations) {
            this.submitContinuousAnnotate(
              this.continuousAnnotations,
              this.currKeyEvent
            );
          }
        } else {
          this.rightPartStyle =
            "pointer-events: none;background-color: rgba(233,233,235,0.21)";
        }
        this.$store.commit("anno/setContinuousAnnotations", null);
        // ctrl按下，则取消draggable事件
        const allTag = document.querySelectorAll(".section span.tag");
        for (let i = 0; i < allTag.length; i++) {
          const tagElement = allTag[i];
          const draggableVal = tagElement.draggable;
          if (draggableVal === true || draggableVal === false) {
            tagElement.draggable = !newValue;
          }
        }
      },
      deep: true,
      immediate: false
    },
    /* isAttributeAnno: {
      handler(newValue, oldValue) {
        const allTag = document.querySelectorAll(`.tag-section span.tag[is_attr="0"]`);
        for (let i = 0; i < allTag.length; i++) {
          const tagElement = allTag[i];
          const draggableVal = tagElement.draggable;
          if (draggableVal === true || draggableVal === false) {
            tagElement.draggable = !newValue;
          }
        }
      },
      deep: true,
      immediate: true
    }, */
    activeName: {
      handler(newValue, oldValue) {
        // 保存当前选中的实体信息，以便页面切换后恢复
        const currentSelectedEntity = this.$store.state.anno.annotation;
        const currentAnnotate = this.$store.state.anno.annotate;

        this.$store.commit("attr/SET_CURR_ATTR", null);
        this.$store.commit("attr/SET_CURR_ANNO_ID", null);

        const isAttribute = newValue === "attribute";
        const allTag = document.querySelectorAll(`.tag-section span.tag`);
        if (allTag) {
          for (let i = 0; i < allTag.length; i++) {
            const tagElement = allTag[i];
            const isAttrFlag = isAttrTag(tagElement.getAttribute("is_attr"));
            if (isAttribute) {
              tagElement.draggable = isAttrFlag;
            } else {
              tagElement.draggable = !isAttrFlag;
            }
          }
        }

        // 右侧选项卡切换监听
        this.$store.commit("anno/setActiveTab", newValue);

        // 页面切换后恢复实体选中状态和样式
        this.$nextTick(() => {
          if (currentSelectedEntity && currentSelectedEntity.currAnnoUniId) {
            // 恢复实体选中状态
            this.$store.commit("anno/setAnno", currentSelectedEntity);
            this.$store.commit("anno/setAnnotate", currentAnnotate);

            // 恢复实体选中样式
            this.restoreEntitySelection(currentSelectedEntity.currAnnoUniId);
          }
        });

        /* if (newValue === "entityList") {
          this.batchSwitchDisabled = true;
        } else {
          this.batchSwitchDisabled = false;
        } */
        this.batchSwitch = false;
        if (
          newValue === "entity" ||
          isAttribute ||
          newValue === "entityList" ||
          newValue === "relationship"
        ) {
          this.showPreSelect = true;
        } else {
          this.showPreSelect = false;
          AnnoEventsBus.$emit("hideAnnotation");
          // 重置预标注的选择框
          if (this.$refs.refPreSelect) {
            this.$refs.refPreSelect.resetValue();
          }
        }
      },
      immediate: false
    },
    // 监听路由变化，如果discussionId参数发生变化，重置自动跳转标志
    "$route.query.discussionId": {
      handler(newDiscussionId, oldDiscussionId) {
        // 如果discussionId参数发生变化，重置自动跳转标志
        if (newDiscussionId !== oldDiscussionId) {
          this.hasAutoSwitchedToDiscussion = false;
          // 如果新的URL包含discussionId，触发检查
          if (newDiscussionId) {
            this.checkAndSwitchToDiscussion();
          }
        }
      },
      immediate: false
    }
  }
};
</script>

<style scoped lang="scss">
::v-deep .el-radio-button--mini .el-radio-button__inner {
  padding: 8px 12px 5px !important;
  font-size: 14px !important;
}

::v-deep .el-form-item__label {
  font-weight: normal;
}

.el-divider--horizontal {
  margin: 5px 0;
}

.el-switch {
  ::v-deep.el-switch__label * {
    line-height: 1.8;
    font-size: 12px;
    font-weight: 600;
  }
}

.annotation {
  margin-top: 10px;
  display: flex;
  width: 100%;
  height: 100%;

  & > * {
    min-width: 50%;
    box-sizing: border-box;
  }
}

.popover {
  background-color: #fff;
  overflow: hidden;

  &-root {
    position: fixed;
    width: 300px;
    height: 200px;
    max-height: 300px;
    background-color: #fff;
    border-radius: 6px;
    box-shadow: rgba(0, 0, 0, 0.8) 0 4px 23px -6px;
    overflow: hidden;
  }

  &-header {
    cursor: pointer;
  }

  &-content {
    margin-bottom: 10px;
  }

  .el-radio {
    display: block;
    line-height: 23px;
    margin-right: 0;
    margin-left: 0;
  }
}

.main {
  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
  }

  &-left,
  &-right {
    .iconfont {
      color: #909399;
      font-size: 28px;
      margin: 0 15px;

      &:hover {
        color: #17b3a3;
      }
    }

    .active {
      color: #17b3a3;
    }
  }
}

.symbol-row {
  margin-bottom: 10px;
}

.pop_attr {
  background-color: #ebb563;
  margin: 2px;
  cursor: default;
  border-radius: 3px;
  padding: 2px;
}

.pop_attr:hover {
  background-color: #ebb563;
}

.batch-switch {
  .el-form-item {
    margin-bottom: 0;
  }
}

.del-attr-title {
  margin-bottom: 6px;
  font-size: 16px;
  margin-left: 8px;
}

.del-attr-title span {
  display: inline-block;
}

.del-attr-name {
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  vertical-align: bottom;
  text-overflow: ellipsis;
}

.question-anno-name {
  max-width: 80%;
  white-space: nowrap;
  overflow: hidden;
  vertical-align: bottom;
  text-overflow: ellipsis;
}

.history-mode-banner {
  margin-bottom: 15px;

  ::v-deep .el-alert {
    border-radius: 6px;
  }

  ::v-deep .el-alert__title {
    font-weight: bold;
    font-size: 16px;
  }

  ::v-deep .el-alert__description {
    margin-top: 8px;
    line-height: 1.5;
  }
}

::v-deep .question-logo:before {
  content: "?";
  font-size: 16px;
  padding-right: 3px;
  padding-left: 3px;
  padding-top: 3px;
  color: red;
  background-color: yellow;
  font-weight: bolder;
  border: 1px solid #5353e0;
  border-radius: 8px;
}
</style>

<style>
.el-tooltip__popper {
  max-width: 700px;
}
</style>
