<template>
  <el-dialog title="打回" :visible.sync="dialogVisible" width="30%">
    <div v-for="(val, idx) in userList" :key="'repulse' + idx">
      <el-divider v-if="idx"></el-divider>
      <div class="textarea-content">
        <span class="repulse-title">{{ val.username }}</span>
        <el-input
          style="width: 230px"
          type="textarea"
          :rows="2"
          placeholder="打回原因"
          v-model="val.repulseMsg"
        >
        </el-input>
        <el-popconfirm
          icon="el-icon-info"
          icon-color="red"
          @confirm="confirmRepulse(val.taskId)"
          :title="`确定单独打回标注员${val.username}吗？`"
        >
          <el-button slot="reference" type="danger" size="small" plain
            >打回
          </el-button>
        </el-popconfirm>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="danger" @click="confirmRepulse(0)">全部打回</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script lang="js">

export default {
  name: "Repulse",
  props: {
    noteId: {
      type: Number,
      require: true
    }
  },
  data() {
    return {
      repulseMsg: [],
      userList: [],
      dialogVisible: false
    };
  },
  methods: {
    confirmRepulse(taskId) {
      let data = this.userList;
      if (taskId) {
        data = this.userList.filter((user) => {
          return user.taskId === taskId;
        });
      }
      this.$http({
        url: this.$http.adornUrl("/task/repulse"),
        method: "post",
        contentType: "application/json",
        data: this.$http.adornData({data }, false)
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.$emit("getNextArticle");
        }
      });
    },
    getData() {
      this.dialogVisible = true;
      this.$http({
        url: this.$http.adornUrl("/task/findAnnoByNoteId"),
        method: "get",
        params: this.$http.adornParams({
          noteId: this.noteId
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.userList = data.data;
        }
      });
    }
  }
};
</script>

<style scoped lang="scss">
.repulse-title {
  font-weight: bold;
  font-size: 15px;
}

.textarea-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
