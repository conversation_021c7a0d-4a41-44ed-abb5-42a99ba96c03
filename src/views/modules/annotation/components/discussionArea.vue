<template>
  <div class="discussion-area" :style="{ height: fullHeight + 'px' }">
    <div class="discussion-content" v-loading="loading">
      <div
        v-if="!discussions || discussions.length === 0"
        class="no-discussions"
      >
        <el-empty description="暂无讨论内容"></el-empty>
      </div>

      <div v-else class="discussion-list">
        <div
          v-for="discussion in discussions"
          :key="discussion.id"
          :ref="`discussion-${discussion.id}`"
          class="discussion-card"
          :class="{
            pending: discussion.status === 1,
            resolved: discussion.status === 2,
            highlighted: highlightedDiscussionId === discussion.id
          }"
        >
          <!-- 第一行：状态和实体/属性名称 -->
          <div class="card-header">
            <div class="status-and-entity">
              <el-tag
                :type="discussion.status === 1 ? 'warning' : 'success'"
                size="mini"
                class="status-tag"
              >
                {{ discussion.status === 1 ? "待解决" : "已解决" }}
              </el-tag>
              <span
                class="entity-name clickable"
                @click="locateToEntity(discussion)"
                :title="discussion.entityNames"
              >
                {{ discussion.entityNames }}
              </span>
            </div>
          </div>

          <!-- 第二行：提问人、时间和操作按钮 -->
          <div class="card-meta">
            <div class="questioner-info">
              <span class="questioner"
                >提问人：{{ discussion.questionerName }}</span
              >
              <span class="question-time">{{
                formatTime(discussion.createTime)
              }}</span>
            </div>
            <div class="action-buttons">
              <el-button
                type="text"
                size="mini"
                icon="el-icon-link"
                @click="copyLink(discussion)"
              >
                复制链接
              </el-button>
              <el-button
                v-if="discussion.status === 1 && isReviewer"
                type="text"
                size="mini"
                icon="el-icon-check"
                @click="resolveQuestion(discussion)"
              >
                解决并回复
              </el-button>
              <el-button
                v-if="discussion.status === 2"
                type="text"
                size="mini"
                icon="el-icon-view"
                @click="viewHistory(discussion)"
              >
                查看历史版本
              </el-button>
            </div>
          </div>

          <!-- 第三行：提问内容 -->
          <div class="question-content">
            <div class="content-label">问题：</div>
            <div class="content-text">{{ discussion.questionText }}</div>
          </div>

          <!-- 第四行：违背规则 -->
          <div v-if="discussion.violateRule" class="violate-rule">
            <div class="content-label">违背规则：</div>
            <div class="content-text">{{ discussion.violateRule }}</div>
          </div>

          <!-- 分隔线 -->
          <div v-if="discussion.replyText" class="reply-divider"></div>

          <!-- 第五行：回复人和回复时间 -->
          <div v-if="discussion.replyText" class="reply-meta">
            <span class="replier">回复人：{{ discussion.replierName }}</span>
            <span class="reply-time">{{
              formatTime(discussion.replyTime)
            }}</span>
          </div>

          <!-- 第六行：回复内容 -->
          <div v-if="discussion.replyText" class="reply-content">
            <div class="content-text">{{ discussion.replyText }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 解决问题弹窗 -->
    <el-dialog
      title="解决问题并回复"
      :visible.sync="resolveDialogVisible"
      width="60%"
    >
      <div>
        <div class="resolve-question">
          <div class="content-label">问题：</div>
          <div class="content-text">{{ currentQuestion.questionText }}</div>
        </div>
        <div class="resolve-reply">
          <div class="content-label">回复：</div>
          <el-input
            type="textarea"
            :rows="4"
            placeholder="请输入回复内容"
            v-model="replyText"
            maxlength="500"
            show-word-limit
          >
          </el-input>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="resolveDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitReply" :loading="submitting">
          确认回复
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { AnnoEventsBus } from "@/utils/bus";

export default {
  name: "DiscussionArea",
  data() {
    return {
      discussions: [],
      loading: false,
      resolveDialogVisible: false,
      currentQuestion: {},
      replyText: "",
      submitting: false,
      highlightedDiscussionId: null, // 用于高亮显示的讨论ID
      fullHeight: document.documentElement.clientHeight - 160 // 计算组件高度
    };
  },
  computed: {
    isReviewer() {
      // 判断当前用户是否为审核员
      return this.$store.state.user.roleId === this.$RoleEnum.auditor;
    },
    currentNoteId() {
      return this.$store.state.anno.annotation.noteId;
    },
    currentTaskId() {
      return this.$store.state.anno.annotation.taskId;
    },
    currentProjectId() {
      return this.$store.state.anno.annotation.projectId;
    },
    currentBatchId() {
      return this.$store.state.anno.annotation.batchId;
    },
    // 判断当前文书是否为"已合格"状态
    isNoteReviewed() {
      return this.$store.state.anno.navbar.status === this.$NoteEnum.reviewed;
    },
    // 判断是否显示重新审核按钮（仅审核员在文书已合格时显示）
    showReReviewButton() {
      return this.isReviewer && this.isNoteReviewed;
    }
  },

  watch: {
    // 监听任务ID变化，自动刷新讨论列表
    currentTaskId(newVal, oldVal) {
      if (newVal && newVal !== oldVal) {
        this.loadDiscussions();
      }
    }
  },
  mounted() {
    this.loadDiscussions();

    // 监听提问成功事件，自动刷新讨论列表
    AnnoEventsBus.$on("questionSubmitted", this.handleQuestionSubmitted);

    // 检查URL中是否包含discussionId参数，用于定位到特定讨论
    this.checkUrlForDiscussionId();

    // 监听窗口大小变化，动态调整高度
    window.onresize = () => {
      return (() => {
        this.fullHeight = document.documentElement.clientHeight - 105;
      })();
    };
  },

  beforeDestroy() {
    // 移除事件监听器
    AnnoEventsBus.$off("questionSubmitted", this.handleQuestionSubmitted);
  },
  methods: {
    // 加载讨论列表
    async loadDiscussions() {
      if (!this.currentTaskId) {
        return;
      }

      this.loading = true;
      try {
        const { data } = await this.$http({
          url: this.$http.adornUrl("/comment/getQuestionsByTaskId"),
          method: "get",
          params: this.$http.adornParams({
            taskId: this.currentTaskId
          })
        });
        if (data && data.code === 0) {
          this.discussions = data.data;
        } else {
          this.$message.error(data.msg || "加载讨论失败");
        }
      } catch (error) {
        console.error("加载讨论失败:", error);
        this.$message.error("加载讨论失败");
      } finally {
        this.loading = false;
      }
    },

    // 刷新讨论
    refreshDiscussions() {
      this.loadDiscussions();
    },

    // 处理提问成功事件
    handleQuestionSubmitted() {
      // 延迟一点时间再刷新，确保后端数据已保存
      setTimeout(() => {
        this.loadDiscussions();
      }, 500);
    },

    // 定位到实体
    locateToEntity(discussion) {
      try {
        // 如果有entityIds，尝试定位到第一个实体
        if (discussion?.entityIds && discussion?.entityIds?.length > 0) {
          const entityId = discussion.entityIds[0];
          const target = document.querySelector(`span[annoid="${entityId}"]`);

          if (target) {
            // 滚动到目标位置
            target.scrollIntoView({ behavior: "smooth", block: "center" });

            // 高亮显示
            target.style.backgroundColor = "#ffeb3b";
            setTimeout(() => {
              target.style.backgroundColor = "";
            }, 2000);

            // 触发选中事件
            const changeData = {
              annoid: target.getAttribute("annoid"),
              uniqueid: target.getAttribute("uniqueid"),
              labelId: target.getAttribute("labelid"),
              content: target.innerText,
              annotate: target.getAttribute("annotate"),
              is_attr: target.getAttribute("is_attr"),
              attr_ids: target.getAttribute("attr_ids"),
              isPre: target.classList.contains("black"),
              showTip: true
            };

            // 触发实体选中事件
            AnnoEventsBus.$emit("doChangeEntity", changeData);
          } else {
            this.$message.warning("未找到相关实体位置");
          }
        } else {
          this.$message.warning("无法定位：缺少实体信息");
        }
      } catch (error) {
        console.error("定位实体失败:", error);
        this.$message.error("定位失败");
      }
    },

    // 复制链接
    copyLink(discussion) {
      try {
        // 构建完整的标注任务链接，包含权限验证参数
        const linkUrl = this.generateDiscussionLink(discussion);

        // 复制到剪贴板
        if (navigator.clipboard && navigator.clipboard.writeText) {
          navigator.clipboard
            .writeText(linkUrl)
            .then(() => {
              this.$message.success("链接已复制到剪贴板");
            })
            .catch(() => {
              this.fallbackCopyText(linkUrl);
            });
        } else {
          this.fallbackCopyText(linkUrl);
        }
      } catch (error) {
        console.error("复制链接失败:", error);
        this.$message.error("复制链接失败");
      }
    },

    // 生成讨论链接
    generateDiscussionLink(discussion) {
      const baseUrl = window.location.origin;
      const basePath = `/${process.env.VUE_APP_BASE_URL || "bnlp-app"}`;

      // 构建标注任务详情页面的路径
      const taskDetailPath = `${basePath}/annotask/detail`;

      // 包含完整的权限验证和定位参数
      const params = new URLSearchParams({
        batchId: this.currentBatchId,
        noteId: this.currentNoteId,
        editable: "false",
        annotatorId: discussion.annotatorId,
        discussionId: discussion.id
      });

      return `${baseUrl}${taskDetailPath}?${params.toString()}`;
    },

    // 备用复制方法
    fallbackCopyText(text) {
      const textArea = document.createElement("textarea");
      textArea.value = text;
      textArea.style.position = "fixed";
      textArea.style.left = "-999999px";
      textArea.style.top = "-999999px";
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      try {
        document.execCommand("copy");
        this.$message.success("链接已复制到剪贴板");
      } catch (err) {
        this.$message.error("复制失败，请手动复制");
      } finally {
        document.body.removeChild(textArea);
      }
    },

    // 解决问题
    resolveQuestion(discussion) {
      this.currentQuestion = discussion;
      this.replyText = "";
      this.resolveDialogVisible = true;
    },

    // 提交回复
    async submitReply() {
      if (!this.replyText.trim()) {
        this.$message.warning("请输入回复内容");
        return;
      }

      this.submitting = true;
      try {
        const { data } = await this.$http({
          url: this.$http.adornUrl("/comment/replyQuestion"),
          method: "post",
          params: this.$http.adornParams({
            commentId: this.currentQuestion.id,
            replyText: this.replyText
          })
        });

        if (data && data.code === 0) {
          this.$message.success("回复成功");
          this.resolveDialogVisible = false;
          this.loadDiscussions(); // 重新加载讨论列表
        } else {
          this.$message.error(data.msg || "回复失败");
        }
      } catch (error) {
        console.error("回复失败:", error);
        this.$message.error("回复失败");
      } finally {
        this.submitting = false;
      }
    },

    // 查看历史版本
    async viewHistory(discussion) {
      try {
        // 构建历史版本查看链接
        const historyUrl = this.generateHistoryViewLink(discussion);

        // 在新窗口中打开历史版本
        const newWindow = window.open(historyUrl);

        if (!newWindow) {
          this.$message.error("无法打开新窗口，请检查浏览器弹窗设置");
          return;
        }
      } catch (error) {
        console.error("打开历史版本失败:", error);
        this.$message.error("打开历史版本失败");
      }
    },

    // 生成历史版本查看链接
    generateHistoryViewLink(discussion) {
      const baseUrl = window.location.origin;
      const basePath = `/${process.env.VUE_APP_BASE_URL || "bnlp-app"}`;

      // 构建标注任务详情页面的路径
      const taskDetailPath = `${basePath}/annotask/detail`;

      // 包含历史版本查看的特殊参数
      const params = new URLSearchParams({
        projectId: this.currentProjectId,
        batchId: this.currentBatchId,
        noteId: this.currentNoteId,
        taskId: this.currentTaskId,
        annotatorId: discussion.annotatorId,
        discussionId: discussion.id,
        editable: "false", // 强制只读模式
        historyMode: "true" // 标识为历史版本模式
      });

      return `${baseUrl}${taskDetailPath}?${params.toString()}`;
    },

    // 格式化时间
    formatTime(time) {
      if (!time) return "";
      const date = new Date(time);
      return date.toLocaleString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit"
      });
    },

    // 检查URL中的discussionId参数
    checkUrlForDiscussionId() {
      const urlParams = new URLSearchParams(window.location.search);
      const discussionId = urlParams.get("discussionId");

      if (discussionId) {
        this.highlightedDiscussionId = discussionId;
        // 延迟执行定位，确保讨论列表已加载
        this.$nextTick(() => {
          setTimeout(() => {
            this.scrollToDiscussion(discussionId);
          }, 1000);
        });
      }
    },

    // 滚动到指定讨论并高亮显示
    scrollToDiscussion(discussionId) {
      const discussionElement = this.$refs[`discussion-${discussionId}`];
      if (discussionElement && discussionElement[0]) {
        const element = discussionElement[0];

        // 滚动到目标位置
        element.scrollIntoView({
          behavior: "smooth",
          block: "center"
        });

        // 添加闪烁效果
        element.classList.add("flash-highlight");
        setTimeout(() => {
          element.classList.remove("flash-highlight");
        }, 3000);
      }
    }
  }
};
</script>

<style scoped>
.discussion-area {
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.discussion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 10px;
}

.discussion-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.discussion-content {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
  padding-right: 10px;
}

.no-discussions {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 500px;
}

.discussion-list {
  space-y: 10px;
}

.discussion-card {
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 12px;
  background: #fff;
  transition: all 0.2s;
  position: relative;
}

.discussion-card:hover {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  border-color: #c0c4cc;
}

/* 待解决状态的卡片样式 */
.discussion-card.pending {
  border-color: #f5dab1;
}

/* 已解决状态的卡片样式 */
.discussion-card.resolved {
  border-color: #b3d8ff;
}

.card-header {
  margin-bottom: 8px;
}

.status-and-entity {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-tag {
  flex-shrink: 0;
  font-weight: 500;
}

.entity-name {
  color: #409eff;
  font-weight: 500;
  font-size: 14px;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  background: rgba(64, 158, 255, 0.1);
  padding: 2px 8px;
  border-radius: 4px;
  border: 1px solid rgba(64, 158, 255, 0.2);
}

.clickable {
  cursor: pointer;
}

.clickable:hover {
  text-decoration: underline;
}

.card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  color: #909399;
}

.questioner-info {
  display: flex;
  gap: 10px;
}

.action-buttons {
  display: flex;
  gap: 5px;
}

.question-content,
.violate-rule,
.reply-content {
  margin-bottom: 8px;
}

.content-label {
  font-weight: 600;
  color: #606266;
  margin-bottom: 6px;
  font-size: 13px;
}

.content-text {
  color: #303133;
  line-height: 1.6;
  word-break: break-word;
  background: #fafafa;
  padding: 8px 12px;
  border-radius: 4px;
  border-left: 3px solid #e6e6e6;
}

.question-content .content-text {
  border-left-color: #409eff;
}

.violate-rule .content-text {
  border-left-color: #f56c6c;
  background: #fef0f0;
}

.reply-content .content-text {
  border-left-color: #67c23a;
  background: #f0f9ff;
}

.reply-divider {
  height: 2px;
  background: linear-gradient(to right, #e6e6e6, #f0f0f0, #e6e6e6);
  margin: 15px 0;
  border-radius: 1px;
  position: relative;
}

.reply-divider::before {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background: #fff;
  padding: 0 8px;
  font-size: 12px;
  color: #909399;
}

.reply-meta {
  display: flex;
  gap: 10px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #909399;
  font-weight: 500;
}

.resolve-question,
.resolve-reply {
  margin-bottom: 15px;
}

/* 高亮显示的讨论卡片 */
.discussion-card.highlighted {
  border-color: #409eff !important;
  box-shadow: 0 4px 20px rgba(64, 158, 255, 0.3) !important;
  background: linear-gradient(135deg, #f0f8ff 0%, #ffffff 100%);
}

/* 闪烁高亮效果 */
.flash-highlight {
  animation: flashHighlight 3s ease-in-out;
}

@keyframes flashHighlight {
  0%,
  100% {
    background-color: transparent;
  }
  25%,
  75% {
    background-color: rgba(64, 158, 255, 0.1);
  }
  50% {
    background-color: rgba(64, 158, 255, 0.2);
  }
}
</style>
