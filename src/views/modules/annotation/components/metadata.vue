<template>
  <el-dialog
    :visible.sync="dialogVisible"
    width="60%"
    :modal="false"
    class="dialog"
  >
    <div v-for="(item, i) in dataList" class="norm" :key="'list' + i">
      <div class="title">{{ item.title.content }}</div>
      <div v-for="(it, j) in item.items" :key="'list1' + i + j">
        - {{ it.content }}
      </div>
    </div>
  </el-dialog>
</template>

<script lang="js">

export default {
  name: "Metadata",
  props: {
    noteId: {
      type: Number,
      require: true
    }
  },
  data() {
    return {
      dataList: [],
      dialogVisible: false
    };
  },
  methods: {
    init() {
      this.$http({
        url: this.$http.adornUrl("/task/findMetadataByNoteId"),
        method: "get",
        params: this.$http.adornParams({
          noteId: this.noteId
        })
      }).then(({data}) => {
        if (data && data.code === 0 && data.data) {
          this.dataList = data.data;
          this.dialogVisible = true;
        } else {
          this.$message.info("暂无数据");
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog {
  ::v-deep .el-dialog {
    max-height: 600px !important;
    overflow-y: auto !important;
    margin-top: 50px !important;
  }
}
.site-navbar__body {
  ::v-deep .el-descriptions-item__label {
    color: #292725;
  }
}

::v-deep .el-dialog__header {
  padding: 0;
  padding-bottom: 0;
}

::v-deep .el-dialog__body {
  -webkit-column-count: 2;
  -moz-column-count: 2;
  column-count: 2;

  .norm {
    break-inside: avoid;
    //width: 50%;
    margin: 10px 0 20px;
    &:first-child {
      margin-top: 0;
    }
    div {
      text-align: justify;
    }
    div:not(:first-child):not(:last-child) {
      margin-bottom: 8px;
    }
  }
}
.title {
  font-weight: 600;
  font-size: 15px;
  margin-bottom: 8px;
  margin-top: 20px;
  &:before {
    content: "\2022"; /* 使用unicode字符作为内容，这里是小圆点 */
    margin-right: 0.5em; /* 在小圆点和p元素之间添加一些间距 */
    color: #409eff;
  }
}
::v-deep .el-dialog__footer {
  padding: 10px;
}
</style>
