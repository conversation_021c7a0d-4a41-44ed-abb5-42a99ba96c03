<template>
  <div v-loading="loadFlag">
    <div
      v-for="(item, idx) in entityData"
      :key="idx"
      :class="idx + 1 !== entityData.length ? 'br' : ''"
    >
      <div class="relation-card-header">
        <span class="tag-first" @click="toEntityPosition(item.textId)"
          >{{ idx + 1 }}.{{ item.text }}</span
        >
        <br />
        <div v-for="(attr, i) in item.attr" :key="'rela-pop-' + i">
          <span class="tag-attr-relation" v-html="attr"></span>
        </div>
        <!--        <span>-->
        <!--          <icon-svg-->
        <!--            :name="'onelabel'"-->
        <!--            class="icon-inner onelabel"-->
        <!--            height="20"-->
        <!--            width="20"-->
        <!--          ></icon-svg>-->
        <!--        </span>-->
        <!--        <span class="tag-first">{{ item.labelCode }}</span>-->
      </div>
      <!--      <div v-if="item.metaMapDTO" class="relation-card-consept">-->
      <!--        <div>-->
      <!--          <img class="consept-icon" src="../../../../../assets/img/umls.png" />-->
      <!--          <span class="tag-first" v-text="item.metaMapDTO.preferredName"></span>-->
      <!--        </div>-->
      <!--        <div-->
      <!--          v-for="it in item.metaMapDTO.semanticTypes"-->
      <!--          :key="it.conceptId"-->
      <!--          class="tag-second tag"-->
      <!--        >-->
      <!--          <span class="marker"></span> {{ it }}-->
      <!--        </div>-->
      <!--      </div>-->
    </div>
  </div>
</template>

<script>
export default {
  props: {
    noteId: Number,
    entity: Array
  },
  watch: {
    entity: {
      handler() {
        this.entityData = undefined;
        this.umls = undefined;
        if (this.entity) {
          this.getEntity();
        }
      }
    }
  },
  data() {
    return {
      loadFlag: true,
      entityData: undefined,
      umls: undefined
    };
  },
  created() {
    if (this.entity) {
      this.getEntity();
    }
  },
  methods: {
    toEntityPosition(id) {
      // 取消原有样式
      const all = document.querySelectorAll("span.select");
      for (let i = 0; i < all.length; i++) {
        all[i].classList.remove("select");
      }
      // 添加样式，并且定位到第一个实体的位置
      const hitEntity = document.querySelectorAll(`span[annoid='${id}']`);
      if (hitEntity) {
        for (let j = 0; j < hitEntity.length; j++) {
          hitEntity[j].classList.add("select");
        }
        hitEntity[0].classList.add("select");
        hitEntity[0].scrollIntoView();
      }
    },
    getEntity() {
      this.loadFlag = true;
      this.$http({
        url: this.$http.adornUrl("/note/getEntityAndUMlS"),
        method: "post",
        data: this.$http.adornData(this.entity, false)
      }).then(({ data }) => {
        if (data.code === 0) {
          this.entityData = data.data;
        }
        this.loadFlag = false;
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.br {
  border-bottom: 2px solid #eeeedd;
}

.onelabel {
  margin-left: 2px;
}

::v-deep .relation-card {
  &-consept {
    /*border-bottom: 1px solid #eeeedd;*/

    .consept-icon {
      width: 24px;
    }
  }

  &-header,
  &-consept {
    padding: 5px;

    .marker {
      display: inline-block;
      width: 8px;
      height: 2px;
      background-color: #17b3a3;
      margin-right: 6px;
      vertical-align: middle;
    }

    .tag {
      display: flex;
      align-items: center;
      padding-left: 25px;
    }

    .tag-first {
      font-size: 14px;
      font-weight: bold;
      cursor: pointer;
      letter-spacing: 0;
      margin: 0;
    }

    .tag-attr-relation {
      font-size: 13px;
      color: #828491;
      letter-spacing: 0;
      margin: 0 0 0 10px;
    }

    .tag-second {
      font-size: 14px;
      // color: #808080;
      line-height: 20px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>
