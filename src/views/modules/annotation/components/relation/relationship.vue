<template>
  <div class="relation-card">
    <el-tabs
      tab-position="bottom"
      style="height: 200px"
      @tab-click="handleClick"
    >
      <el-tab-pane
        v-if="patterns.length > 0"
        :key="patterns.length + 1"
        label="总列表"
      >
        <relationship-list :key="'all-list' + listMapKey"></relationship-list>
      </el-tab-pane>
      <el-tab-pane
        v-if="patterns.length > 0"
        :key="patterns.length"
        label="总预览"
      >
        <relationship-map
          :key="'all-map' + listMapKey"
          :patternId="-1"
        ></relationship-map>
      </el-tab-pane>
      <el-tab-pane
        v-for="(item, index) in patterns"
        :key="index"
        :label="item.name"
      >
        <comm-pattern
          :key="'comm-pattern' + index + time"
          :patternData="item"
        ></comm-pattern>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import CommPattern from "@/views/modules/annotation/components/relation/comm-pattern";
import RelationshipMap from "@/views/modules/annotation/components/relation/relationship-map";
import RelationshipList from "@/views/modules/annotation/components/relation/relationship-list";

export default {
  data() {
    return {
      fullHeight: document.documentElement.clientHeight - 75,
      map: true,
      list: true,
      workEnv:
        JSON.parse(sessionStorage.getItem("workEnv")) ||
        this.$store.state.anno.annotation,
      patterns: [],
      time: 0,
      listMapKey: 0
    };
  },
  components: {
    CommPattern,
    RelationshipMap,
    RelationshipList
  },
  computed: {},
  created() {},
  mounted() {
    this.getPatterns();
    window.onresize = () => {
      return (() => {
        this.fullHeight = document.documentElement.clientHeight - 105;
      })();
    };
  },
  methods: {
    handleClick(tab, event) {
      this.time = new Date().getTime();
      if (tab.label === "总预览" || tab.label === "总列表") {
        this.listMapKey = new Date().getTime();
      }
    },
    getPatterns() {
      if (this.workEnv) {
        this.$http({
          url: this.$http.adornUrl("/labels/relation/pattern/list"),
          method: "post",
          params: this.$http.adornParams({
            projectId: this.workEnv.projectId,
            status: 1,
            limit: 500
          })
        }).then(({ data }) => {
          if (data.code === 0) {
            this.patterns = data.page.list || [];
          }
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.el-tabs {
  ::v-deep .el-tabs__item.is-active {
    color: #409eff !important;
  }
}

.relation-card {
  padding-right: 0;
}
</style>
