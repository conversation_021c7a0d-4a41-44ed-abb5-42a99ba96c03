<template>
  <el-card
    :style="'height: ' + fullHeight + 'px'"
    class="relation-card-list"
    shadow="never"
  >
    <el-button
      style="float: right; margin-bottom: 10px"
      v-if="showImportBtn"
      @click="importAllToOriginal"
      icon="el-icon-copy-document"
      type="primary"
      size="mini"
      round
      plain
      >导入全部关系到原文
    </el-button>
    <el-alert
      :closable="!showImportBtn"
      v-for="item in relationShipsData"
      :key="item.id"
      type="info"
      @close="removeRelationships(item.id)"
    >
      <div slot="title" style="width: 100%">
        <el-row>
          <el-col :span="23">
            <div
              v-for="(it, key) in item.item"
              :key="'cop-item' + key"
              class="patter-list"
            >
              <span class="radio-number">
                {{ it.order }}
              </span>
              <!-- 主语 -->
              <span v-if="it.subList.isEntity">
                <el-popover
                  style="cursor: pointer"
                  placement="left"
                  trigger="click"
                  width="360"
                  @hide="setShowPopId(undefined)"
                  @after-enter="setShowPopId(it.subList.entityIds)"
                >
                  <span slot="reference">
                    <span style="vertical-align: middle">{{
                      $tools.truncateText(it.subList.text, 18)
                    }}</span>
                    <span v-if="it.subList.number" class="plus-color"
                      >+{{ it.subList.number }}</span
                    >
                  </span>
                  <relationPopover
                    v-if="showPopId === it.subList.entityIds"
                    :entity="it.subList.entityIds"
                    :note-id="annotation.noteId"
                  />
                </el-popover>
              </span>
              <span v-else class="statement-number">
                {{ it.subList.index }}
              </span>
              <!-- 否定 -->
              <span v-if="it.negation" style="margin-left: 5px">[否定]</span>
              <!-- 程度副词 -->
              <span v-if="it.annotations" class="degree"
                >({{ it.annotations.join(",") }})</span
              >
              <!-- 关系 -->
              <span class="item-predicate">{{
                relationLabelMap.get(it.relation)
              }}</span>
              <!-- 宾语 -->
              <span v-if="it.objList.isEntity">
                <el-popover
                  style="cursor: pointer"
                  placement="left"
                  trigger="click"
                  width="360"
                  @hide="setShowPopId(undefined)"
                  @after-enter="setShowPopId(it.objList.entityIds)"
                >
                  <span slot="reference">
                    <span>{{ $tools.truncateText(it.objList.text, 18) }}</span>
                    <span v-if="it.objList.number" class="plus-color"
                      >+{{ it.objList.number }}</span
                    >
                  </span>
                  <relationPopover
                    v-if="showPopId === it.objList.entityIds"
                    :entity="it.objList.entityIds"
                    :note-id="annotation.noteId"
                  />
                </el-popover>
              </span>
              <span v-else class="statement-number degree">
                {{ it.objList.index }}
              </span>
              <br />
            </div>
          </el-col>
          <!--编辑-->
          <el-col :span="2" class="operate">
            <!--            <el-tooltip
              v-if="roleId === $RoleEnum.auditor && !item.item[0].correct"
              effect="light"
              content="当前关系状态是错误，点击改为标注正确"
              placement="top"
              :enterable="false"
            >
              <i
                class="el-icon-circle-close icon-red"
                @click="auditStatus(item.id, true)"
              ></i>
            </el-tooltip>
            <el-tooltip
              v-if="roleId === $RoleEnum.auditor && item.item[0].correct"
              effect="light"
              content="当前关系状态是正确，点击改为标注错误"
              placement="top"
              :enterable="false"
            >
              <i
                class="el-icon-circle-check icon-primary"
                @click="auditStatus(item.id, false)"
              ></i>
            </el-tooltip>-->

            <el-tooltip
              v-if="showImportBtn && isAuth('task:detail:import')"
              effect="light"
              content="导入原文"
              placement="top"
              :enterable="false"
            >
              <i
                class="el-icon-s-promotion icon-primary"
                @click="relationImportToOriginal(item.id)"
              ></i>
            </el-tooltip>

            <el-tooltip
              effect="light"
              content="查看实体"
              placement="top"
              :enterable="false"
            >
              <i
                class="el-icon-view icon-primary"
                @click="seeEntity(item.id)"
              ></i>
            </el-tooltip>
          </el-col>
        </el-row>
      </div>
    </el-alert>
    <el-empty
      v-show="listIsEmpty"
      style="height: calc(100vh - 205px)"
      description="暂无数据，请在自定义模板中添加"
    ></el-empty>
  </el-card>
</template>

<script>
import relationPopover from "@/views/modules/annotation/components/relation/relationPopover";
import { AnnoEventsBus } from "@/utils/bus";

let preLoadingInstance = null;

export default {
  name: "relationship-list",
  components: {
    relationPopover
  },
  data() {
    return {
      relationShipsData: [],
      patternId: -1,
      fullHeight: document.documentElement.clientHeight - 159,
      relationOptions: [],
      preLoading: false,
      showPopId: "",
      listIsEmpty: false
    };
  },
  created() {},
  activated() {},
  mounted() {
    // 获取关系标签数据
    this.getRelationships();
    this.getRelationLabels();
    // 获取关系标注
    window.onresize = () => {
      return (() => {
        this.fullHeight = document.documentElement.clientHeight - 159;
      })();
    };
  },
  watch: {
    preLoading: {
      handler(newValue, oldValue) {
        if (newValue) {
          preLoadingInstance = this.$loading({
            text: "Program running...",
            spinner: "el-icon-loading",
            background: "rgba(204,204,204,0.82)"
          });
        } else if (preLoadingInstance != null) {
          preLoadingInstance.close();
        }
      },
      deep: false,
      immediate: true
    }
  },
  computed: {
    masterTaskId() {
      return this.$store.state.anno.masterTaskId;
    },
    showImportBtn() {
      return this.$store.state.anno.showImportBtn;
    },
    noteId() {
      return this.$store.state.anno.annotation.noteId;
    },
    taskId() {
      return this.$store.state.anno.annotation.taskId;
    },
    roleId() {
      return this.$store.state.user.roleId;
    },
    userId() {
      return this.$store.state.user.id;
    },
    importLogId() {
      return this.$store.state.anno.annotation.importLogId;
    },
    relationLabelMap: function () {
      const tempMap = new Map();
      for (const v of this.relationOptions) {
        tempMap.set(v.id, v.name);
      }
      return tempMap;
    },
    entityLabelMap: function () {
      const tempMap = new Map();
      for (const v of this.entityLabelData) {
        tempMap.set(v.id, v.name);
      }
      return tempMap;
    },
    annotation: function () {
      return this.$store.state.anno.annotation;
    },
    editable() {
      return this.$store.state.anno.editable;
    }
  },
  methods: {
    cleanInput(index, place) {
      if (place === "subject" && !this.templateData[index].subOptions[0].text) {
        this.templateData[index].subject = [];
        this.templateData[index].subOptions = [{ id: null, text: null }];
      }
      if (place === "objects" && !this.templateData[index].objOptions[0].text) {
        this.templateData[index].objects = [];
        this.templateData[index].objOptions = [{ id: null, text: null }];
      }
    },
    // 获取关系标签数据
    getRelationLabels() {
      this.$http({
        url: this.$http.adornUrl("/labels/relation/getRelationLabels"),
        method: "get",
        params: this.$http.adornParams({
          projectId: this.annotation.projectId
        })
      }).then(({ data }) => {
        if (data.code === 0) {
          this.relationOptions = data.data || [];
        } else {
          this.$message({
            message: "获取关系标签失败！",
            type: "error"
          });
        }
      });
    },
    // 获取关系标注
    getRelationships() {
      this.$http({
        url: this.$http.adornUrl("/note/relationship/list"),
        method: "get",
        params: this.$http.adornParams({
          taskId: this.annotation.taskId,
          noteId: this.annotation.noteId,
          patternId: this.patternId,
          source: this.annotation.source,
          importLogId: this.importLogId
        })
      }).then(({ data }) => {
        this.refreshMap = true;
        if (data.code === 0) {
          this.relationShipsData = data.data || [];
          this.listIsEmpty = !(this.relationShipsData.length > 0);
        } else {
          this.$message({
            message: "获取关系标注信息失败！",
            type: "error"
          });
        }
      });
    },
    relationImportToOriginal(id) {
      this.$http({
        url: this.$http.adornUrl("/note/relationship/importToOriginal"),
        method: "post",
        data: this.$http.adornData({
          relationId: id,
          masterTaskId: this.masterTaskId,
          noteId: this.noteId,
          source: this.annotation.source,
          roleId: this.roleId,
          taskId: this.taskId,
          importLogId: this.importLogId
        })
      }).then(({ data }) => {
        if (data.code === 0) {
          this.$message({
            message: "导入完毕！",
            type: "success"
          });
          AnnoEventsBus.$emit("refreshRequest");
        }
      });
    },
    importAllToOriginal() {
      this.$confirm("确认要执行一键导入到原文吗？", "警告", {
        confirmButtonText: "导入",
        cancelButtonText: "取消"
      }).then(() => {
        this.preLoading = true;
        this.$http({
          url: this.$http.adornUrl(`/note/relationship/importAllToOriginal`),
          method: "post",
          data: this.$http.adornData({
            masterTaskId: this.masterTaskId,
            noteId: this.noteId,
            source: this.annotation.source,
            roleId: this.roleId,
            taskId: this.taskId,
            importLogId: this.importLogId
          })
        })
          .then(({ data }) => {
            if (data.code === 0) {
              this.$message({
                message: "导入完毕！",
                type: "success"
              });
              AnnoEventsBus.$emit("refreshRequest");
            }
          })
          .finally(() => {
            this.preLoading = false;
          });
      });
    },
    // 删除关系标注组
    removeRelationships(id) {
      if (!this.hasMarkPermission()) return;

      this.refreshMap = false;
      this.$http({
        url: this.$http.adornUrl("/note/relationship/removeById"),
        method: "get",
        params: this.$http.adornParams({
          id: id,
          taskId: this.annotation.taskId,
          roleId: this.roleId
        })
      }).then(({ data }) => {
        if (data.code === 0) {
          this.$message({
            message: "删除成功！",
            type: "success"
          });
          // 刷新数据
          this.getRelationships();
        } else {
          this.$message({
            message: "删除失败！",
            type: "error"
          });
          // 刷新数据
          this.getRelationships();
        }
        this.refreshMap = true;
      });
    },
    // 审核关系标注，修改状态
    auditStatus(id, status) {
      if (!this.hasMarkPermission()) return;

      this.refreshMap = false;
      this.$http({
        url: this.$http.adornUrl("/note/relationship/auditStatus"),
        method: "get",
        params: this.$http.adornParams({
          id: id,
          taskId: this.annotation.taskId,
          status: status
        })
      }).then(({ data }) => {
        if (data.code === 0) {
          this.$message({
            message: "操作成功！",
            type: "success"
          });
          // 刷新数据
          this.getRelationships();
        } else {
          this.$message({
            message: "操作失败！",
            type: "error"
          });
          // 刷新数据
          this.getRelationships();
        }
        this.refreshMap = true;
      });
    },
    seeEntity(id) {
      this.$http({
        url: this.$http.adornUrl("/note/relationship/getEntitiesById"),
        method: "get",
        params: this.$http.adornParams({
          id: id
        })
      }).then(({ data }) => {
        if (data.code === 0) {
          const entityIds = data.data;
          // 删除选中样式
          const all = document.querySelectorAll("span.select");
          for (let i = 0; i < all.length; i++) {
            all[i].classList.remove("select");
          }
          // 添加样式，并且定位到第一个实体的位置
          for (let i = 0; i < entityIds.length; i++) {
            const hitEntity = document.querySelectorAll(
              `span[annoid='${entityIds[i]}']`
            );
            // hitEntity.classList.add("select");
            for (let j = 0; j < hitEntity.length; j++) {
              hitEntity[j].classList.add("select");
            }
            if (i === 0 && hitEntity) {
              hitEntity[0].scrollIntoView();
            }
          }
        } else {
          this.$message({
            message: "查询实体ID出错",
            type: "error"
          });
        }
      });
    },
    setShowPopId(id) {
      this.showPopId = id;
    },
    allowDrop(ev) {
      ev.preventDefault();
    },
    // 检查标注权限
    hasMarkPermission() {
      if (!this.editable) {
        this.$message({
          message: "当前状态无编辑权限！",
          type: "error"
        });
        return false;
      }
      return true;
    },
    stopProp(e) {
      e.stopPropagation();
    }
  }
};
</script>

<style scoped lang="scss">
.patter-list {
  &:not(:last-child) {
    margin-bottom: 8px;
  }
}

.el-alert.is-light {
  ::v-deep.el-alert__closebtn {
    color: #f56c6c;
    font-weight: 600;
  }
}
</style>
