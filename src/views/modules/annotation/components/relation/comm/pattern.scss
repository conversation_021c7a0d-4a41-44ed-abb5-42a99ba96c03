.multiple-choice {
  border-radius: 4px;
  width: 100%;
  //border: 1px solid #e6a23c;
}

.single-choice {
  border-radius: 4px;
  border: 1px solid #67c23a;
}

.radio-number {
  color: #fff;
  line-height: 21px;
  margin-top: 0;
  margin-right: 3px;
  border-radius: 50%;
  height: 20px;
  width: 20px;
  display: inline-block;
  background: #2B82B9;
  vertical-align: middle;
  text-align: center;

  span {
    display: block;
    color: #FFFFFF;
    height: 20px;
    line-height: 20px;
    text-align: center
  }
}

.statement-number {
  //margin-top: 3px;
  line-height: 15px;
  color: #fff;
  border-radius: 50%;
  height: 15px;
  width: 15px;
  display: inline-block;
  background: #EB6709;
  vertical-align: middle;
  text-align: center;

  span {
    display: block;
    color: #FFFFFF;
    height: 15px;
    line-height: 15px;
    text-align: center
  }
}

.required {
  vertical-align: middle;
  text-align: center;
  display: inline-block;
  margin-top: 10px;
  color: red;
  font-size: 20px;
}

.relation-card {
  padding-right: 10px;
  // overflow-y: auto;
  &-wrapper {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  &-input {
    margin-bottom: 5px;
  }

  .el-card {
    //margin-bottom: 14px;

    /*::v-deep .el-card__body {
      padding: 12px;
    }*/
  }

  .el-row {
    display: flex;
    align-items: center;
  }

  .el-button {
    padding: 5px;
    border: 1px dashed #dcdfe6;
  }

  .el-alert {
    margin: 4px 0 0;

    &:first-child {
      margin: 0;
    }
  }

  &-list {
    flex: 1;
    //  height: 100px;
    overflow-y: scroll;

    .item-predicate {
      margin: 0 4px;
      color: #5eb7ff;
      padding-top: 1px;
    }
  }

  &-bottom {
    margin-bottom: 0 !important;

    img {
      height: 200px;
      text-align: center;
    }
  }

  .icon {
    color: gray;
    margin: 0 5px 0 1px;
    font-size: 13px;
  }
}

.plus-color {
  color: #85ce61;
}
