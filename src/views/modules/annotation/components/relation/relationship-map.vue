<template>
  <div
    v-loading="loading"
    style="height: 100%"
    ref="graphShrink"
    class="relation-card"
  >
    <el-row
      :gutter="5"
      align="center"
      class="relation-card-input"
      justify="end"
    >
      <el-col :span="4" :offset="21">
        <el-button
          type="primary"
          plain
          icon="el-icon-rank"
          size="mini"
          @click="fullscreen"
        ></el-button>
      </el-col>
    </el-row>
    <div :id="'graph-shrink-' + patternId" style="z-index: 10" />
    <el-dialog :modal="loading" fullscreen :visible.sync="dialogVisible">
      <div :id="'graph-full-' + patternId" />
    </el-dialog>
  </div>
</template>

<script>
import G6 from "@antv/g6";

export default {
  name: "relationship-map",
  data() {
    return {
      comboTitle: "",
      item: null,
      loading: true,
      graph: null,
      dialogVisible: false,
      mapData: null
    };
  },
  props: {
    patternId: {
      type: Number,
      required: true
    }
  },
  created() {},
  mounted() {
    // 设置article高度
    const el1 = document.getElementById("article");
    const height = el1.clientHeight - 39;
    document.getElementById("graph-shrink--1").style.height = height + "px";
    this.loading = true;
    this.$nextTick(() => {
      this.getData();
    });
  },
  computed: {
    annotation: function () {
      return this.$store.state.anno.annotation;
    }
  },
  methods: {
    toEntityPosition(id) {
      // 取消原有样式的数据
      // 删除选中样式
      const all = document.querySelectorAll("span.select");
      for (let i = 0; i < all.length; i++) {
        all[i].classList.remove("select");
      }
      const hitEntity = document.querySelectorAll("span[annoid='" + id + "']");
      if (hitEntity) {
        for (let j = 0; j < hitEntity.length; j++) {
          hitEntity[j].classList.add("select");
        }
        hitEntity[0].classList.add("select");
        hitEntity[0].scrollIntoView();
      }
    },
    getData(type) {
      if (!type) {
        type = "graph-shrink-" + this.patternId;
      }
      this.$http({
        url: this.$http.adornUrl("/note/relationship/getMapData"),
        method: "get",
        params: this.$http.adornParams({
          taskId: this.annotation.taskId,
          noteId: this.annotation.noteId,
          patternId: this.patternId,
          source: this.annotation.source
        })
      }).then(({ data }) => {
        if (data.code === 0) {
          this.mapData = data.data;
          // 设置暂停时间,等待组件加载完毕
          let stopTime = 2000;
          if (type === "graph-full-" + this.patternId) {
            stopTime = 1;
          }
          setTimeout(() => {
            this.getInit(type);
            this.loading = false;
          }, stopTime);
        } else {
          this.$message({
            message: "获取关系数据失败！",
            type: "error"
          });
          this.loading = false;
        }
      });
    },
    fullscreen() {
      this.dialogVisible = !this.dialogVisible;
      this.$nextTick(() => {
        this.getData("graph-full-" + this.patternId);
      });
    },
    getInit: function (container) {
      const el1 = document.getElementById("article");
      const el2 = document.getElementById("relationship");
      const el1Height = el1.clientHeight;
      const el2Width = el2.clientWidth;
      const el2Height = el2.clientHeight;
      let width = el2Width;
      let height = el1Height - el2Height + 50;
      if (this.patternId === -1) {
        height = el1Height - 50;
      }

      if (container === "graph-full-" + this.patternId) {
        width = window.innerWidth;
        height = window.innerHeight;
      }

      // 鼠标悬浮节点提示框
      const tooltip = new G6.Tooltip({
        // offsetX 与 offsetY 需要加上父容器的 padding
        offsetX: 30,
        offsetY: -15,
        // 允许出现 tooltip 的 item 类型
        itemTypes: ["node"],
        // 允许出现的时机
        shouldBegin: (e) => {
          const model = e.item.getModel();
          // 虚拟节点不允许出现
          return !model.id.toString().startsWith("R");
        },
        // 自定义 tooltip 内容
        getContent: (e) => {
          const outDiv = document.createElement("div");
          outDiv.style.width = "fit-content";
          outDiv.style.height = "fit-content";
          const model = e.item.getModel();
          if (e.item.getType() === "node") {
            if (!model.name) {
              return outDiv;
            }
            // 实体名
            let html = `<strong>${model.name
              .replace(/\s/g, "")
              .replace(/(.{15})/g, "$1<br>")}</strong>`;
            if (model.value) {
              html += "<br>CUI:" + model.value;
            }
            // 属性
            if (model.attr) {
              html += "<br>";
              model.attr.forEach((it) => {
                html += "<br>" + it;
              });
            }
            outDiv.innerHTML = html;
          } else {
            return false;
          }
          return outDiv;
        }
      });

      const graph = new G6.Graph({
        container: container,
        width: width,
        height: height,
        plugins: [tooltip],
        modes: {
          default: [
            "drag-canvas",
            "zoom-canvas",
            "drag-node",
            {
              type: "activate-relations",
              // trigger: 'mouseenter',
              shouldUpdate: (e) => {
                const model = e.getModel();
                // 虚拟节点不允许出现
                return !model.id.toString().startsWith("R");
              },
              resetSelected: true
            }
          ]
        },
        nodeStateStyles: {
          hover: {
            fill: "#BDD2FD"
          },
          active: {
            opacity: 1
          },
          inactive: {
            opacity: 0.5
          }
        },
        edgeStateStyles: {
          active: {
            stroke: "#999"
          }
        },
        defaultNode: {
          size: 50,
          labelCfg: {
            positions: "center"
          }
        },
        defaultEdge: {
          style: {
            opacity: 0.6,
            stroke: "grey",
            endArrow: true
          },
          type: "loop",
          labelCfg: {
            autoRotate: true,
            style: {
              fontSize: 13,
              background: {
                fill: "#ffffff",
                padding: [2, 2, 2, 2]
              }
            }
          }
        }
      });

      if (!this.mapData) {
        graph.render();
        return;
      }
      const virtualNodes = new Set();

      function linkVNode(id) {
        const vNode = graph.findById(id);
        const edge = graph.findById("link_" + id);
        const bbox = edge.getBBox();
        const x0 = bbox.centerX;
        const y0 = bbox.centerY;

        vNode.update({
          x: x0,
          y: y0
        });
      }

      graph.destroyLayout();

      graph.setAutoPaint(true);

      // 设置布局
      graph.updateLayout({
        type: "force",
        preventOverlap: true,
        linkDistance: (d) => {
          if (d.target.id.startsWith("R")) {
            return 100;
          }
          return 170;
        },
        edgeStrength: 1,
        onTick: () => {
          virtualNodes.forEach((n) => {
            linkVNode(n);
          });
        }
      });

      // 固定节点被拖拽的位置

      function refreshDragedNodePosition(e) {
        const model = e.item.get("model");

        model.fx = e.x;
        model.fy = e.y;
      }

      // 所有行为的监听
      graph.on("node:dragstart", function (e) {
        graph.layout();
        refreshDragedNodePosition(e);
      });
      graph.on("node:drag", function (e) {
        refreshDragedNodePosition(e);
      });
      graph.on("node:dragend", function (e) {
        e.item.get("model").fx = null;
        e.item.get("model").fy = null;
      });
      graph.on("node:mouseenter", (evt) => {
        const { item } = evt;
        const model = item.getModel();
        // 虚拟节点不生效
        if (!model.id.toString().startsWith("R")) {
          graph.setItemState(item, "hover", true);
        }
      });
      graph.on("node:mouseleave", (evt) => {
        const { item } = evt;
        graph.setItemState(item, "hover", false);
      });
      graph.on("click", (ev) => {});
      graph.on("node:click", (ev) => {
        const { item } = ev;
        const model = item.getModel();
        const id = model.entityId;
        if (id && id.length === 32) {
          this.toEntityPosition(id);
        }
      });

      const data = { nodes: [], edges: [] };

      data.nodes = this.mapData.nodes.map((node) => {
        const newNode = node;

        // newNode.label = node.name.replace(/ /g, "\n");
        newNode.label = node.name;
        if (newNode.label.length > 15) {
          newNode.label = this.$tools.truncateText(newNode.label, 20) + "...";
        }
        return newNode;
      });

      function addVNode(id) {
        if (id.startsWith("R") && !virtualNodes.has(id)) {
          virtualNodes.add(id);
          data.nodes.push({
            id,
            size: 20,
            style: {
              fill: "transparent",
              stroke: "transparent"
            },
            stateStyles: {
              active: {
                opacity: 0
              },
              inactive: {
                opacity: 0
              }
            }
          });
        }
      }

      data.edges = this.mapData.edges.map((link) => {
        const newLink = link;

        // newLink.label = newLink.name.toUpperCase();
        newLink.label = newLink.name;
        addVNode(link.target);
        addVNode(link.source);
        newLink.id = "link_" + link.id;

        // 特殊关系用红色标注
        if (link.special) {
          newLink.labelCfg = {
            style: {
              stroke: "#fff",
              fill: "#FF0000"
            }
          };
        }
        return newLink;
      });
      G6.Util.processParallelEdges(data.edges, 30);

      data.nodes.forEach((i) => {
        if (i.color) {
          i.style = Object.assign(i.style || {}, {
            fill: i.color,
            opacity: 1
          });
          i.stateStyles = Object.assign(i.stateStyles || {}, {
            active: {
              opacity: 1,
              fill: i.color,
              stroke: "transparent"
            },
            inactive: {
              opacity: 0.5
            }
          });
        }
      });
      graph.data(data);

      graph.render();
      this.graph = graph;

      if (typeof window !== "undefined") {
        window.onresize = () => {
          if (!this.graph || this.graph.get("destroyed")) return;
          if (!container || !container.scrollWidth || !container.scrollHeight)
            return;
          this.graph.changeSize(container.scrollWidth, container.scrollHeight);
        };
      }
    }
  }
};
</script>

<style lang="scss" scoped></style>
