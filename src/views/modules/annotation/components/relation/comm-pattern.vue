<template>
  <div :style="{ height: fullHeight + 'px' }" class="relation-card">
    <div class="relation-card-wrapper">
      <el-collapse
        accordion
        v-for="(it, index) in schemaData"
        :key="'cop-sch' + index"
        :name="index"
      >
        <el-collapse-item>
          <template slot="title">
            <div
              @click="stopProp"
              :gutter="5"
              align="center"
              class="relation-card-top"
            >
              <div class="number-top">
                <span v-if="it.order" class="radio-number">{{ it.order }}</span>
                <span v-if="it.required" class="required">*</span>
              </div>
              <!--主语-->
              <div
                class="item"
                @drop="dropData($event, 'sub', index)"
                @dragover="allowDrop($event)"
                :accept-entity-id="it.subject.entityTypes"
              >
                <el-input
                  size="mini"
                  v-if="it.subject && it.subject.foreign"
                  :placeholder="holderName(it.subject.foreign)"
                  disabled
                  readonly
                ></el-input>
                <el-select
                  style="width: 100%"
                  v-model="templateData[index].subject"
                  v-else-if="it.subject && it.subject.multiple"
                  size="mini"
                  multiple
                  collapse-tags
                  default-first-option
                  :placeholder="it.subject.name"
                >
                  <el-option
                    v-for="item in templateData[index].subOptions"
                    :key="item.id"
                    :label="item.text"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
                <el-input
                  size="mini"
                  v-else-if="it.subject && !it.subject.multiple"
                  v-model="templateData[index].subOptions[0].text"
                  :placeholder="it.subject.name"
                  @change="cleanInput(index, 'subject')"
                  clearable
                  readonly
                >
                </el-input>
              </div>
              <!--关系-->
              <div class="item">
                <el-select
                  size="mini"
                  style="width: 100%"
                  v-model="templateData[index].relation"
                  clearable
                  filterable
                  placeholder="关系"
                >
                  <el-option
                    v-for="item in relationOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
              </div>
              <!--宾语-->
              <div class="item">
                <div
                  @drop="dropData($event, 'obj', index, it.objects.entityTypes)"
                  @dragover="allowDrop($event)"
                  :access-entity-type="it.objects.entityTypes"
                >
                  <el-input
                    size="mini"
                    v-if="it.objects && it.objects.foreign"
                    :placeholder="holderName(it.objects.foreign)"
                    disabled
                    readonly
                  ></el-input>
                  <el-select
                    v-model="templateData[index].objects"
                    v-else-if="it.objects && it.objects.multiple"
                    size="mini"
                    class="multiple-choice"
                    multiple
                    collapse-tags
                    default-first-option
                    :placeholder="it.objects.name"
                  >
                    <el-option
                      v-for="item in templateData[index].objOptions"
                      :key="item.id"
                      :label="item.text"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>
                  <el-input
                    size="mini"
                    v-else-if="it.objects && !it.objects.multiple"
                    v-model="templateData[index].objOptions[0].text"
                    :placeholder="it.objects.name"
                    @change="cleanInput(index, 'objects')"
                    clearable
                    readonly
                  >
                  </el-input>
                </div>
              </div>
            </div>
          </template>
          <el-row>
            <!--否定-->
            <el-col class="center" :span="3" :offset="2">
              <el-checkbox
                v-model="templateData[index].negation"
                label="否定"
              ></el-checkbox>
            </el-col>
            <!--关系批注-->
            <el-col :span="18">
              <el-select
                size="mini"
                style="width: 100%"
                @dragover.prevent
                v-model="templateData[index].annotations"
                multiple
                filterable
                default-first-option
                placeholder="批注信息"
              >
                <el-option
                  size="mini"
                  v-for="item in templateData[index].annotationOptions"
                  :key="item"
                  :label="item"
                  :value="item"
                >
                </el-option>
              </el-select>
            </el-col>
          </el-row>
        </el-collapse-item>
      </el-collapse>
      <el-row
        :gutter="5"
        align="center"
        style="margin-top: 10px"
        class="relation-card-input"
        justify="end"
      >
        <el-col :span="4">
          <el-button type="success" plain size="mini" @click="activeMap = true"
            >关系预览
          </el-button>
        </el-col>
        <el-col :span="4">
          <el-button type="primary" plain size="mini" @click="activeMap = false"
            >关系列表
          </el-button>
        </el-col>
        <!--模板样例图片-->
        <el-col :span="4" :offset="3">
          <el-popover placement="left" width="500" trigger="click">
            <img :src="image" />
            <el-button type="info" plain size="mini" slot="reference"
              >Pattern
            </el-button>
          </el-popover>
        </el-col>
        <el-col :span="3" :offset="3">
          <el-button
            type="success"
            plain
            size="mini"
            @click="saveRelationShip()"
            >新增关系
          </el-button>
        </el-col>
        <el-col :span="3">
          <el-button
            type="warning"
            plain
            size="mini"
            @click="generateDefaultData(schemaData)"
            >重置表单
          </el-button>
        </el-col>
      </el-row>
      <!--关系预览-->
      <relationship-map
        v-if="refreshMap"
        v-show="activeMap"
        :patternId="patternId"
        ref="refMap"
      ></relationship-map>
      <!--关系组数据列表-->
      <el-card class="relation-card-list" v-show="!activeMap" shadow="never">
        <el-alert
          v-for="item in relationShipsData"
          :key="item.id"
          type="info"
          :closable="!showImportBtn"
          @close="removeRelationships(item.id)"
        >
          <div slot="title" style="width: 100%">
            <el-row>
              <el-col :span="23">
                <div
                  v-for="(it, key) in item.item"
                  :key="'cop-item' + key"
                  class="patter-list"
                >
                  <span class="radio-number">
                    {{ it.order }}
                  </span>
                  <!-- 主语 -->
                  <span v-if="it.subList.isEntity">
                    <el-popover
                      style="cursor: pointer"
                      placement="left"
                      trigger="click"
                      width="360"
                      @hide="setShowPopId(undefined)"
                      @after-enter="setShowPopId(it.subList.entityIds)"
                    >
                      <span slot="reference">
                        <span style="vertical-align: middle">{{
                          $tools.truncateText(it.subList.text, 18)
                        }}</span>
                        <span v-if="it.subList.number" class="plus-color"
                          >+{{ it.subList.number }}</span
                        >
                      </span>
                      <relationPopover
                        v-if="showPopId === it.subList.entityIds"
                        :entity="it.subList.entityIds"
                        :note-id="annotation.noteId"
                      />
                    </el-popover>
                  </span>
                  <span v-else class="statement-number">
                    {{ it.subList.index }}
                  </span>
                  <!-- 否定 -->
                  <span v-if="it.negation" style="margin-left: 5px"
                    >[否定]</span
                  >
                  <!-- 程度副词 -->
                  <span v-if="it.annotations" class="degree"
                    >({{ it.annotations.join(",") }})</span
                  >
                  <!-- 关系 -->
                  <span class="item-predicate">{{
                    relationLabelMap.get(it.relation)
                  }}</span>
                  <!-- 宾语 -->
                  <span v-if="it.objList.isEntity">
                    <el-popover
                      style="cursor: pointer"
                      placement="left"
                      trigger="click"
                      width="360"
                      @hide="setShowPopId(undefined)"
                      @after-enter="setShowPopId(it.objList.entityIds)"
                    >
                      <span slot="reference">
                        <span>{{
                          $tools.truncateText(it.objList.text, 18)
                        }}</span>
                        <span v-if="it.objList.number" class="plus-color"
                          >+{{ it.objList.number }}</span
                        >
                      </span>
                      <relationPopover
                        v-if="showPopId === it.objList.entityIds"
                        :entity="it.objList.entityIds"
                        :note-id="annotation.noteId"
                      />
                    </el-popover>
                  </span>
                  <span v-else class="statement-number degree">
                    {{ it.objList.index }}
                  </span>
                  <br />
                </div>
              </el-col>
              <!--编辑-->
              <el-col :span="2" class="operate">
                <!--                <el-tooltip
                  v-if="roleId === $RoleEnum.auditor && !item.item[0].correct"
                  effect="light"
                  content="当前关系状态是错误，点击改为标注正确"
                  placement="top"
                  :enterable="false"
                >
                  <i
                    class="el-icon-circle-close icon-red"
                    @click="auditStatus(item.id, true)"
                  ></i>
                </el-tooltip>
                <el-tooltip
                  v-if="roleId === $RoleEnum.auditor && item.item[0].correct"
                  effect="light"
                  content="当前关系状态是正确，点击改为标注错误"
                  placement="top"
                  :enterable="false"
                >
                  <i
                    class="el-icon-circle-check icon-success"
                    @click="auditStatus(item.id, false)"
                  ></i>
                </el-tooltip>-->
                <el-tooltip
                  v-if="showImportBtn && isAuth('task:detail:import')"
                  effect="light"
                  content="导入原文"
                  placement="top"
                  :enterable="false"
                >
                  <i
                    class="el-icon-s-promotion icon-primary"
                    @click="relationImportToOriginal(item.id)"
                  ></i>
                </el-tooltip>
                <el-tooltip
                  effect="light"
                  content="查看实体"
                  placement="top"
                  :enterable="false"
                >
                  <i
                    class="el-icon-view icon-primary"
                    @click="seeEntity(item.id)"
                  ></i>
                </el-tooltip>
                <el-tooltip
                  v-if="!showImportBtn"
                  effect="light"
                  content="编辑"
                  placement="top"
                  :enterable="false"
                >
                  <i
                    class="el-icon-edit icon-primary"
                    @click="editRelationship(item.id)"
                  ></i>
                </el-tooltip>
              </el-col>
            </el-row>
          </div>
        </el-alert>
      </el-card>
    </div>
  </div>
</template>
<script>
import relationPopover from "@/views/modules/annotation/components/relation/relationPopover";
import relationshipMap from "@/views/modules/annotation/components/relation/relationship-map";

export default {
  data() {
    return {
      fullHeight: document.documentElement.clientHeight - 160,
      templateData: [],
      entityLabelData: [],
      relationShipsData: [],
      relationOptions: [],
      tempData: "",
      activeMap: true,
      refreshMap: true,
      showPopId: "",
      schemaData: JSON.parse(this.patternData.schemaData),
      image: this.patternData.image,
      patternId: JSON.parse(this.patternData.id)
    };
  },
  props: ["patternData"],
  components: {
    relationPopover,
    relationshipMap
  },
  created() {
    this.generateDefaultData(this.schemaData);
    this.getRelationships();
  },
  mounted() {
    // 获取关系标签数据
    this.getRelationLabels();
    this.getEntityLabels();
    // 获取关系标注
    window.onresize = () => {
      return (() => {
        this.fullHeight = document.documentElement.clientHeight - 180;
      })();
    };
  },
  computed: {
    roleId() {
      return this.$store.state.user.roleId;
    },
    userId() {
      return this.$store.state.user.id;
    },
    relationLabelMap: function () {
      const tempMap = new Map();
      for (const v of this.relationOptions) {
        tempMap.set(v.id, v.name);
      }
      return tempMap;
    },
    masterTaskId() {
      return this.$store.state.anno.masterTaskId;
    },
    showImportBtn() {
      return this.$store.state.anno.showImportBtn;
    },
    entityLabelMap: function () {
      const tempMap = new Map();
      for (const v of this.entityLabelData) {
        tempMap.set(v.id, v.name);
      }
      return tempMap;
    },
    annotation: function () {
      return this.$store.state.anno.annotation;
    },
    editable() {
      return this.$store.state.anno.editable;
    }
  },
  methods: {
    cleanInput(index, place) {
      if (place === "subject" && !this.templateData[index].subOptions[0].text) {
        this.templateData[index].subject = [];
        this.templateData[index].subOptions = [{ id: null, text: null }];
      }
      if (place === "objects" && !this.templateData[index].objOptions[0].text) {
        this.templateData[index].objects = [];
        this.templateData[index].objOptions = [{ id: null, text: null }];
      }
    },
    // 获取关系标签数据
    getRelationLabels() {
      this.$http({
        url: this.$http.adornUrl("/labels/relation/getRelationLabels"),
        method: "get",
        params: this.$http.adornParams({
          projectId: this.annotation.projectId
        })
      }).then(({ data }) => {
        if (data.code === 0) {
          this.relationOptions = data.data || [];
        } else {
          this.$message({
            message: "获取关系标签失败！",
            type: "error"
          });
        }
      });
    },
    // 获取关系标注
    getRelationships() {
      this.$http({
        url: this.$http.adornUrl("/note/relationship/list"),
        method: "get",
        params: this.$http.adornParams({
          taskId: this.annotation.taskId,
          noteId: this.annotation.noteId,
          patternId: this.patternId,
          source: this.annotation.source
        })
      }).then(({ data }) => {
        this.refreshMap = true;
        if (data.code === 0) {
          this.relationShipsData = data.data || [];
          this.refreshMap = true;
        } else {
          this.$message({
            message: "获取关系标注信息失败！",
            type: "error"
          });
        }
      });
    },
    // 根据schema生成对应提交的模板数据
    generateDefaultData(schemaData, oldId) {
      if (schemaData) {
        this.templateData = [];
        let id = this.$tools.getUUID();
        if (oldId) {
          id = oldId;
        }
        for (let i = 0; i < schemaData.length; i++) {
          const v = schemaData[i];
          const example = {
            id: "",
            subject: [],
            subOptions: [{ id: null, text: null }],
            annotationOptions: v.annotationOptions || [],
            annotations: [],
            negation: false,
            relation: v.defaultRelation ? v.defaultRelation : "",
            objects: [],
            objOptions: [{ id: null, text: null }],
            group: "",
            order: "",
            required: ""
          };
          // 根据JSON生成特定的模板数据
          // 生成order
          example.order = v.order;
          // 生成id
          if (v.required) {
            example.required = v.required;
          }
          example.id = id;
          this.templateData.push(example);
        }
        for (let i = 0; i < schemaData.length; i++) {
          const v = schemaData[i];
          const example = this.templateData[i];
          // 生成subject
          if (v.subject.foreign !== null && v.subject.foreign !== undefined) {
            if ((v.subject.foreign + "").indexOf("#") !== -1) {
              const split = v.subject.foreign.split("#");
              if (split[1] === "subject") {
                example.subject.push(
                  this.templateData[parseInt(split[0]) - 1].order + "#subject"
                );
              } else {
                example.subject.push(
                  this.templateData[parseInt(split[0]) - 1].order + "#objects"
                );
              }
            } else {
              example.subject.push(
                this.templateData[parseInt(v.subject.foreign) - 1].order + ""
              );
            }
          }
          // 生成objects
          if (v.objects.foreign !== null && v.objects.foreign !== undefined) {
            if ((v.objects.foreign + "").indexOf("#") !== -1) {
              const split = v.objects.foreign.split("#");
              if (split[1] === "subject") {
                example.objects.push(
                  this.templateData[parseInt(split[0]) - 1].order + "#subject"
                );
              } else {
                example.objects.push(
                  this.templateData[parseInt(split[0]) - 1].order + "#objects"
                );
              }
            } else {
              example.objects.push(
                this.templateData[parseInt(v.objects.foreign) - 1].order + ""
              );
            }
          }
          this.templateData[i] = example;
        }
      }
    },
    // 向指定位置拖入数据
    dropData(ev, po, i, entityTypes) {
      // ev 拖动的数据，po 位置 是subject还是objects， i 索引，代表第几条 关系
      const id = ev.dataTransfer.getData("id");
      let isAttr = ev.dataTransfer.getData("isAttr");
      if (isAttr === "1") {
        return;
      }
      if (isAttr === null || isAttr === undefined || isAttr === "") {
        return;
      }
      const text = ev.dataTransfer.getData("text");
      const labelId = ev.dataTransfer.getData("labelId");
      const entity = { id, text };
      if (
        !this.$_.isEmpty(entityTypes) &&
        entityTypes.indexOf(Number.parseInt(labelId)) === -1
      ) {
        this.$message({
          message: `不支持此${this.entityLabelMap.get(
            Number.parseInt(labelId)
          )}标签的实体拖入,仅支持${entityTypes
            .map((x) => this.entityLabelMap.get(x))
            .join(",")}标签实体拖入`,
          type: "warning"
        });
        return;
      }
      // 向subject里面拖
      if (po === "sub") {
        // subject支持多选
        if (this.schemaData[i].subject.multiple) {
          for (const v of this.templateData[i].subOptions) {
            if (v.id === id) {
              this.$message({
                message: "该实体已存在",
                type: "warning"
              });
              return;
            }
          }
        } else if (!this.schemaData[i].subject.multiple) {
          if (this.templateData[i].subOptions.length > 1) {
            this.$message({
              message: "只能存在一个实体",
              type: "warning"
            });
            return;
          }
          this.templateData[i].subOptions = [];
          this.templateData[i].subject = [];
        }
        this.templateData[i].subOptions.push(entity);
        this.templateData[i].subject.push(id);
        this.templateData[i].subOptions = this.templateData[
          i
        ].subOptions.filter((it) => it.id !== null);
      }
      if (po === "obj") {
        // objects支持多选
        if (this.schemaData[i].objects.multiple) {
          for (const v of this.templateData[i].objOptions) {
            if (v.id === id) {
              this.$message({
                message: "该实体已存在",
                type: "warning"
              });
              return;
            }
          }
        } else if (!this.schemaData[i].objects.multiple) {
          if (this.templateData[i].objOptions.length > 1) {
            this.$message({
              message: "只能存在一个实体",
              type: "warning"
            });
            return;
          }
          this.templateData[i].objOptions = [];
          this.templateData[i].objects = [];
        }
        this.templateData[i].objOptions.push(entity);
        this.templateData[i].objects.push(id);
        this.templateData[i].objOptions = this.templateData[
          i
        ].objOptions.filter((it) => it.id !== null);
      }
    },
    getEntityLabels() {
      this.$http({
        url: this.$http.adornUrl(
          `/note/getFirstLabels/${this.annotation.projectId}`
        ),
        method: "get"
      }).then((data) => {
        const labels = data.data.data;
        this.entityLabelData = labels;
      });
    },
    holderName(value) {
      if ((value + "").indexOf("#") !== -1) {
        const split = value.split("#");
        let name;
        if (split[1] === "subject") {
          name =
            "(" + split[0] + ")" + this.schemaData[split[0] - 1].subject.name;
        } else {
          name =
            "(" + split[0] + ")" + this.schemaData[split[0] - 1].objects.name;
        }
        return name;
      }
      return "(" + value + ")";
    },
    // 保存关系标注
    saveRelationShip() {
      if (!this.hasMarkPermission()) return;

      // 检查主语宾语是否重复
      const set = new Set();
      for (let x of this.templateData) {
        if (
          x.subject.length === 1 &&
          x.objects.length === 1 &&
          x.subject[0] === x.objects[0]
        ) {
          let entity = x.subject[0];
          if (entity.length === 32) {
            if (set.has(entity)) {
              this.$message({
                message: "选择的实体不能重复！",
                type: "warning"
              });
              return;
            }
            set.add(entity);
          }
        } else {
          let entities = x.subject.concat(x.objects);
          for (let entity of entities) {
            if (entity.length === 32) {
              if (set.has(entity)) {
                this.$message({
                  message: "选择的实体不能重复！",
                  type: "warning"
                });
                return;
              }
              set.add(entity);
            }
          }
        }
      }
      // 检查实体是否填写完整
      let flag = true;
      for (let i = 0; i < this.schemaData.length; i++) {
        // 该条数据必须填写
        if (
          this.schemaData[i].required &&
          this.schemaData[i].required === true
        ) {
          // subject 不是外键
          if (
            this.schemaData[i].subject.foreign === undefined ||
            this.schemaData[i].subject.foreign === null
          ) {
            if (!(this.templateData[i].subject.length > 0)) {
              flag = false;
            }
          }
          if (!this.templateData[i].relation) {
            flag = false;
          }
          // object 不是外键
          if (
            this.schemaData[i].objects.foreign === undefined ||
            this.schemaData[i].objects.foreign === null
          ) {
            if (!(this.templateData[i].objects.length > 0)) {
              flag = false;
            }
          }
        } else {
          // subject 不是外键
          if (
            this.schemaData[i].subject.foreign === undefined ||
            this.schemaData[i].subject.foreign === null
          ) {
            if (
              this.templateData[i].subject.length > 0 &&
              !this.templateData[i].relation
            ) {
              flag = false;
            }
          }
          // object 不是外键
          if (
            this.schemaData[i].objects.foreign === undefined ||
            this.schemaData[i].objects.foreign === null
          ) {
            if (
              this.templateData[i].objects.length > 0 &&
              !this.templateData[i].relation
            ) {
              flag = false;
            }
          }
          if (this.templateData[i].relation) {
            if (
              this.templateData[i].subject.length === 0 ||
              this.templateData[i].objects.length === 0
            ) {
              flag = false;
            }
          }
        }
      }
      if (!flag) {
        this.$message({
          message: "请补充完整的关系信息",
          type: "warning"
        });
        return;
      }
      // 过滤一遍数据
      const tempData = this.templateData.filter((x) => x.relation);
      let totalSet = [];
      let idSet = [];
      tempData.forEach((x) => {
        totalSet = totalSet.concat(x.subject).concat(x.objects);
      });
      // 将是外键（S_开头）过滤出来
      totalSet = totalSet.filter((x) => x.indexOf("S_") >= 0);
      // 将是外键且不是S，O（包含#） 过滤出去
      totalSet = totalSet.filter((x) => x.indexOf("#") < 0);
      tempData.forEach((x) => {
        idSet = idSet.concat(x.id);
      });
      for (const v of totalSet) {
        if (idSet.indexOf(v) < 0) {
          this.$message({
            message: "请补充完整的关系信息",
            type: "warning"
          });
          return;
        }
      }
      let data = {};
      // 构成提交的数据
      for (let i = 0; i < this.templateData.length; i++) {
        const v = this.templateData[i];
        data.id = v.id;
        data.noteId = this.annotation.noteId;
        data.patternId = this.patternId;
        data.taskId = this.annotation.taskId;
        if (this.roleId === this.$RoleEnum.annotator) {
          data.annotatorId = this.userId;
          data.auditorId = v.auditorId;
        }
        if (this.roleId === this.$RoleEnum.auditor) {
          data.annotatorId = v.annotatorId;
          data.auditorId = this.userId;
        }
        const temp = {
          order: v.order,
          subject: v.subject,
          negation: v.negation,
          // annotations: v.annotations,
          relation: v.relation,
          objects: v.objects
        };
        if (v.annotations && v.annotations.length > 0) {
          temp.annotations = v.annotations;
        }
        if (!data.items) {
          data.items = [];
        }
        if (v.relation) {
          data.items.push(temp);
        }
      }
      this.refreshMap = false;
      // 添加数据
      this.$http({
        url: this.$http.adornUrl("/note/relationship/add"),
        method: "post",
        data: this.$http.adornData(data, false)
      }).then(({ data }) => {
        if (data.code !== 0) {
          this.$message({
            message: data.msg,
            type: "error"
          });
          // 清空表单
          this.generateDefaultData(this.schemaData);
          // 刷新数据
          this.getRelationships();
        } else {
          this.data = [];
          // 清空表单
          this.generateDefaultData(this.schemaData);
          // 刷新数据
          this.getRelationships();
          this.$message({
            message: "保存成功！",
            type: "success"
          });
        }
      });
    },
    // 编辑关系标注
    editRelationship(id) {
      this.$http({
        url: this.$http.adornUrl("/note/relationship/getById"),
        method: "get",
        params: this.$http.adornParams({
          id: id
        })
      }).then(({ data }) => {
        if (data.code === 0) {
          const entityMap = new Map();
          for (const k of Object.keys(data.entityMap)) {
            entityMap.set(k, data.entityMap[k]);
          }

          // 处理firstTemp的数据
          const ship = data.data.items;
          let annotatorId = data.data.annotatorId || null;
          let auditorId = data.data.auditorId || null;
          const ids = new Map();
          ship.forEach((x) => {
            ids.set("" + x.order, x.id);
          });
          this.generateDefaultData(this.schemaData, id, ids);

          for (let i = 0; i < this.templateData.length; i++) {
            for (let j = 0; j < ship.length; j++) {
              if (ship[j].order === this.templateData[i].order) {
                this.templateData[i].subject = ship[j].subject;
                for (const v of this.templateData[i].subject) {
                  const option = {
                    id: v,
                    text: entityMap.get(v)
                  };
                  this.templateData[i].subOptions.push(option);
                }
                this.templateData[i].subOptions = this.templateData[
                  i
                ].subOptions.filter((it) => it.id !== null);

                this.templateData[i].relation = ship[j].relation;
                this.templateData[i].negation = ship[j].negation;
                this.templateData[i].annotations = ship[j].annotations || [];

                this.templateData[i].objects = ship[j].objects;
                for (const v of this.templateData[i].objects) {
                  const option = {
                    id: v,
                    text: entityMap.get(v)
                  };
                  this.templateData[i].objOptions.push(option);
                }
                this.templateData[i].objOptions = this.templateData[
                  i
                ].objOptions.filter((it) => it.id !== null);
                this.templateData[i].annotatorId = annotatorId;
                this.templateData[i].auditorId = auditorId;
              }
            }
          }
        } else {
          this.$message({
            message: "发生错误！",
            type: "error"
          });
        }
      });
    },
    relationImportToOriginal(id) {
      this.$http({
        url: this.$http.adornUrl("/note/relationship/importToOriginal"),
        method: "post",
        data: this.$http.adornData({
          relationId: id,
          masterTaskId: this.masterTaskId,
          noteId: this.annotation.noteId,
          source: this.annotation.source,
          roleId: this.roleId,
          taskId: this.annotation.taskId
        })
      }).then(({ data }) => {
        if (data.code === 0) {
          this.$message({
            message: "导入完毕！",
            type: "success"
          });
        }
      });
    },
    // 删除关系标注组
    removeRelationships(id) {
      if (!this.hasMarkPermission()) return;

      this.refreshMap = false;
      this.$http({
        url: this.$http.adornUrl("/note/relationship/removeById"),
        method: "get",
        params: this.$http.adornParams({
          id: id,
          taskId: this.annotation.taskId,
          roleId: this.roleId
        })
      }).then(({ data }) => {
        if (data.code === 0) {
          this.$message({
            message: "删除成功！",
            type: "success"
          });
          // 刷新数据
          this.getRelationships();
        } else {
          this.$message({
            message: "删除失败！",
            type: "error"
          });
          // 刷新数据
          this.getRelationships();
        }
        this.refreshMap = true;
      });
    },
    // 审核关系标注，修改状态
    auditStatus(id, status) {
      if (!this.hasMarkPermission()) return;

      this.refreshMap = false;
      this.$http({
        url: this.$http.adornUrl("/note/relationship/auditStatus"),
        method: "get",
        params: this.$http.adornParams({
          id: id,
          taskId: this.annotation.taskId,
          status: status
        })
      }).then(({ data }) => {
        if (data.code === 0) {
          this.$message({
            message: "操作成功！",
            type: "success"
          });
          // 刷新数据
          this.getRelationships();
        } else {
          this.$message({
            message: "操作失败！",
            type: "error"
          });
          // 刷新数据
          this.getRelationships();
        }
        this.refreshMap = true;
      });
    },
    seeEntity(id) {
      this.$http({
        url: this.$http.adornUrl("/note/relationship/getEntitiesById"),
        method: "get",
        params: this.$http.adornParams({
          id: id
        })
      }).then(({ data }) => {
        if (data.code === 0) {
          const entityIds = data.data;
          // 删除选中样式
          const all = document.querySelectorAll("span.select");
          for (let i = 0; i < all.length; i++) {
            all[i].classList.remove("select");
          }
          // 添加样式，并且定位到第一个实体的位置
          for (let i = 0; i < entityIds.length; i++) {
            const hitEntity = document.querySelectorAll(
              `span[annoid='${entityIds[i]}']`
            );
            // hitEntity.classList.add("select");
            for (let j = 0; j < hitEntity.length; j++) {
              hitEntity[j].classList.add("select");
            }
            if (i === 0 && hitEntity) {
              hitEntity[0].scrollIntoView();
            }
          }
        } else {
          this.$message({
            message: "查询实体ID出错",
            type: "error"
          });
        }
      });
    },
    setShowPopId(id) {
      this.showPopId = id;
    },
    allowDrop(ev) {
      ev.preventDefault();
    },
    // 检查标注权限
    hasMarkPermission() {
      if (!this.editable || this.showImportBtn) {
        this.$message({
          message: "当前状态无编辑权限！",
          type: "error"
        });
        return false;
      }
      return true;
    },
    stopProp(e) {
      e.stopPropagation();
    }
  }
};
</script>

<style lang="scss">
@import "./comm/pattern";

.jsoneditor-vue {
  height: 100%;
}
</style>
<style lang="scss" scoped>
@import "./comm/pattern.scss";
.el-collapse {
  border: none !important;
  ::v-deep.el-collapse-item__header {
    height: 40px;
  }
  ::v-deep.el-collapse-item__content {
    padding-bottom: 8px;
  }
  .relation-card-top {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    .number-top {
      width: 40px;
      text-align: left;
    }
    .item {
      flex: 1;
      margin-right: 5px;
    }
  }
  .el-collapse-item__arrow {
    margin: 0 8px 0 8px;
    width: 13px;
  }
}
.el-select {
  ::v-deep.el-select__tags-text {
    max-width: 120px !important;
  }
}
.relation-card {
  ::v-deep.el-checkbox__label {
    color: gray;
    font-size: 12px;
    padding-left: 5px;
  }
}

.degree {
  margin: 0 3px;
}
.patter-list {
  display: flex;
  align-items: center;
}
.patter-list:not(:last-child) {
  margin-bottom: 8px;
}
.el-alert.is-light {
  ::v-deep.el-alert__closebtn {
    color: #f56c6c;
    font-weight: 600;
  }
}
.relation-card-list {
  overflow-y: auto;
}
</style>
<style>
@import "./comm/relation.css";
</style>
