<template>
  <div :style="{ height: fullHeight + 'px' }" class="rightBox">
    <!-- 标签选择区域 - 自适应高度 -->
    <el-card
      shadow="never"
      v-if="labelsArray && labelsArray.length !== 0"
      class="box-card box-card-label"
      :body-style="{ padding: '10px' }"
    >
      <el-radio-group
        v-model="selectedLabel"
        @change="checkAnnoLabel"
        style="width: 100%"
      >
        <el-row
          class="button-area"
          v-for="(valA, a) in labelsArray"
          :key="`label-arr-${a}`"
        >
          <el-col :span="12" v-for="(val, b) in valA" :key="`label1-${b}`">
            <el-row>
              <el-col :span="19">
                <span
                  class="entity-colors"
                  :style="`color:${val.color}`"
                ></span>
                <el-radio
                  class="entity-radio"
                  :border="false"
                  :label="val.id + ''"
                >
                  {{ val.name }}
                </el-radio>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </el-radio-group>
    </el-card>
    <el-card
      shadow="never"
      v-else
      class="box-card"
      :body-style="{ padding: '10px' }"
    >
      <div class="el-tree__empty-block">
        <span class="el-tree__empty-text">暂无数据</span>
      </div>
    </el-card>
    <!-- 实体批注信息区域 - 固定2行高度 -->
    <div
      v-if="currAnnoUniId && currAnnoUniId.length > 0 && !annotation.isAttr"
      class="annotation-section"
    >
      <h3 class="entity">实体批注信息</h3>
      <el-card class="box-card" shadow="never" :body-style="{ padding: '0' }">
        <el-input
          type="textarea"
          :rows="2"
          placeholder="输入实体批注信息"
          v-model="annotate"
          maxlength="100"
          show-word-limit
          :readonly="this.$store.state.anno.navbar.taskStatus === '已合格'"
          @change="addAnnotate"
        >
        </el-input>
      </el-card>
    </div>

    <!-- 当前标签标注示例区域 - 占用剩余高度并可滚动 -->
    <div v-if="currentLabelExample" class="example-section">
      <div class="example-header">
        <h3 class="entity" style="margin: 0">当前标签标注示例</h3>
        <el-button
          v-if="showAddToExampleBtn"
          @click="openAddToExampleDialog"
          type="primary"
          size="mini"
          plain
          icon="el-icon-plus"
          :disabled="!canAddToExample"
        >
          添加到示例
        </el-button>
      </div>
      <div class="example-content">
        <mavon-editor
          v-model="currentLabelExample"
          :subfield="false"
          :defaultOpen="'preview'"
          :toolbarsFlag="false"
          :editable="false"
          :scrollStyle="true"
          :ishljs="true"
          :boxShadow="false"
          :toolbars="{
            fullscreen: true
          }"
          style="z-index: 1; height: 100%"
        />
      </div>
    </div>

    <template v-if="hasUmls">
      <el-row type="flex" align="middle" justify="space-between">
        <el-col :span="8"><h3>UMLS Concept</h3></el-col>
        <el-col :span="10" style="margin: 5px 0">
          <el-input
            placeholder="UMLS Concept ID"
            v-model="conceptId"
            size="mini"
            class="search"
            clearable
            @focus="removeListener"
            @blur="addListener"
            :disabled="
              this.annotation == null ||
              !this.currAnnoUniId ||
              (this.conceptRadio != null && this.conceptRadio !== '')
            "
          >
            <el-button
              slot="append"
              size="mini"
              icon="el-icon-search"
              @click="searchConcept"
            ></el-button>
          </el-input>
        </el-col>
      </el-row>
      <el-card
        shadow="never"
        v-loading="umlsLoading"
        v-if="umls && umls.length !== 0"
        :body-style="{ padding: '10px' }"
        class="box-card"
      >
        <el-row
          v-for="(val, idx) in umls"
          :key="idx"
          :gutter="20"
          justify="space-between"
        >
          <el-col :span="22">
            <el-radio-group
              v-model="conceptRadio"
              class="el-radio"
              type="vertical"
            >
              <el-radio
                :label="val.conceptId"
                @change="conceptChange"
                @click.native.prevent="
                  conceptClick(
                    val.conceptId,
                    val.preferredName,
                    val.conceptType
                  )
                "
              >
                <el-tooltip
                  :content="
                    val.conceptId +
                    (val.conceptName ? ' - ' + val.conceptName : '')
                  "
                  :enterable="false"
                  class="item"
                  effect="dark"
                  placement="top-start"
                >
                  <span style="font-weight: bold"
                    >{{ val.preferredName }}
                    <br />
                    <span
                      v-for="(item, index) in val.semanticTypes"
                      :key="index"
                      :label="idx"
                    >
                      <span
                        style="margin-left: 2em; font-weight: normal"
                        v-text="`${item}`"
                      ></span>
                      <br />
                    </span>
                  </span>
                </el-tooltip>
              </el-radio>
            </el-radio-group>
          </el-col>
          <el-col :span="2">
            <a
              class="go-back"
              href="javascript:0;"
              @click="gotoByConceptId(val)"
              ><i style="font-size: x-small" class="el-icon-link"></i
            ></a>
          </el-col>
        </el-row>
      </el-card>
      <el-card
        shadow="never"
        v-loading="umlsLoading"
        v-else
        class="box-card card-bottom"
        :body-style="{ padding: '10px', height: '100%' }"
      >
        <div class="el-tree__empty-block">
          <span class="el-tree__empty-text">暂无数据</span>
        </div>
      </el-card>
    </template>
    <base-css :data="css" id="labels" />

    <!-- 添加到示例弹窗 -->
    <el-dialog
      title="添加到示例"
      :visible.sync="addToExampleDialogVisible"
      width="80%"
      max-height="80vh"
      @close="onAddToExampleDialogClose"
    >
      <div>
        <!-- 预览示例 -->
        <div style="margin-bottom: 15px">
          <div style="margin-bottom: 8px">
            <strong>预览示例：</strong>
            <span
              >(如果需要强调被标注的实体，请用==包裹它的两端，比如：==高亮的实体==)</span
            >
          </div>
          <el-input
            type="textarea"
            :rows="10"
            placeholder="请编辑示例内容"
            v-model="examplePreview"
            show-word-limit
          >
          </el-input>
        </div>

        <!-- 选择规则 -->
        <div style="margin-bottom: 15px">
          <div style="font-weight: bolder; margin-bottom: 8px">
            选择规则：<span style="color: red">*</span>
          </div>
          <el-radio-group v-model="selectedRuleForExample">
            <el-radio
              v-for="rule in availableRules"
              :key="rule.value"
              :label="rule.value"
              style="display: block; margin-bottom: 8px"
            >
              {{ rule.label }}
            </el-radio>
          </el-radio-group>
          <div
            v-if="availableRules.length === 0"
            style="color: #999; font-style: italic"
          >
            当前标签暂无可用规则
          </div>
        </div>

        <!-- 示例类型 -->
        <div style="margin-bottom: 15px">
          <div style="font-weight: bolder; margin-bottom: 8px">示例类型：</div>
          <el-radio-group v-model="exampleType">
            <el-radio label="positive">正例</el-radio>
            <el-radio label="negative">反例</el-radio>
          </el-radio-group>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeAddToExampleDialog">取消</el-button>
        <el-button type="primary" icon="el-icon-check" @click="doSaveExample">
          确认
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script lang="js">
import BaseCss from "@/components/annotation/base-css";
import {AnnoEventsBus} from "@/utils/bus";
import {isAttrTag, trimStr} from "@/utils";
import { mavonEditor } from "mavon-editor";
import "mavon-editor/dist/css/index.css";

const Color = require("color");

export default {
  name: "AnnoEntity",
  components: {
    mavonEditor,
    BaseCss
  },
  data() {
    return {
      selectedLabel: null,
      //实体批注
      annotate: null,
      labelRadio: 0,
      lineNumber: 2,
      conceptRadio: "",
      currPreferredName: null,
      conceptType: null,
      fullHeight: document.documentElement.clientHeight - 160,
      labels: [],
      labelsArray: [],
      check: -1,
      umlsLoading: false,
      css: "",
      umls: [],
      defaultProps: {
        children: "children",
        label: "name"
      },
      conceptId: "",
      currentLabelExample: "", // 当前标签的规则示例
      // 添加到示例相关数据
      addToExampleDialogVisible: false,
      examplePreview: "",
      selectedRuleForExample: "",
      exampleType: "positive",
      availableRules: [],
      currentSelectedEntity: null // 当前选中的实体信息
    };
  },
  computed: {
    annotation: function () {
      return this.$store.state.anno.annotation;
    },
    editable() {
      return this.$store.state.anno.editable;
    },
    batchSwitch() {
      return this.$store.state.anno.batchSwitch;
    },
    currAnnoUniId() {
      return this.$store.state.anno.annotation.currAnnoUniId;
    },
    currAnnotate() {
      return this.$store.state.anno.annotate;
    },
    roleId() {
      return this.$store.state.user.roleId;
    },
    currLabelId() {
      return this.$store.state.anno.annotation.labelId;
    },
    isOriginal() {
      const s = this.$store.state.anno.annotation.source;
      return s + "" === "1";
    },
    hasUmls() {
      let disambiguate = false;
      if (this.labels) {
        const labels = this.labels;
        const length = labels.length;
        for (let i = 0; i < length; i++) {
          if ((labels[i].id + "") === (this.selectedLabel + "")) {
            disambiguate = labels[i].disambiguate;
            break;
          }
        }
      }
      return disambiguate;
    },
    // 是否显示"添加到示例"按钮
    showAddToExampleBtn() {
      return (
        this.roleId === this.$RoleEnum.auditor ||
        this.roleId === this.$RoleEnum.projectAdmin
      );
    },
    // 是否可以添加到示例（需要选中实体）
    canAddToExample() {
      return !!this.currAnnoUniId && !this.annotation.isAttr;
    }
  },
  watch: {
    currAnnotate: {
      handler(newValue, oldValue) {
        this.annotate = newValue;
      },
      immediate: true,
      deep: true
    },
    currLabelId: {
      handler(newValue, oldValue) {
        this.selectedLabel = newValue;
        if (newValue) {
          this.loadLabelExample(newValue);
        }
      },
      immediate: false
    }
  },
  mounted() {
    this.annotate = this.currAnnotate;
    this.selectedLabel = (this.currLabelId) ? (this.currLabelId + "") : null;
    // 监听数字案件选择一级标签
    this.addListener();
    window.onresize = () => {
      return (() => {
        this.fullHeight = document.documentElement.clientHeight - 105;
      })();
    };
  },
  destroyed() {
    this.removeListener();
    AnnoEventsBus.$off("refreshLabels");
    AnnoEventsBus.$off("getUMLSConcept");
    AnnoEventsBus.$off("resetUMLSConcept");
    AnnoEventsBus.$off("emptyLabel");
    this.currentLabelExample = "";
  },
  created() {
    this.getLabels();
    // 刷新一级和二级标签
    AnnoEventsBus.$on("refreshLabels", (id) => {
      this.$http({
        url: this.$http.adornUrl("/note/getEntity"),
        method: "get",
        params: this.$http.adornParams({
          id: id
        })
      }).then(({data}) => {
        const val = data.data;
        this.conceptId = val.conceptId;
        this.getUMLSConcept(val.entityInfos.map((x) => x.content).join(" "), val.conceptId);

        // 如果有标签ID，加载对应的标签示例
        if (val.labelId) {
          this.loadLabelExample(val.labelId);
        }

        /* // 如果有ID，说明是从人工标注的返回的值
        if (val.id) {
          if (this.labels != null && this.labels.length !== 0) {
            for (const [index, label] of this.labels.entries()) {
              if (label.id === val.labelId) {
                this.changeLabel(label.id, false);
                // 回显二级标签状态
                /!* if (val && val.subLabels != null) {
                  this.labelRadio = val.subLabels;
                } else {
                  this.labelRadio = 0;
                } *!/
                break;
              }
            }
          }
          // 回显UMLS
          if (val.conceptId) {
            this.$nextTick(() => {
              this.conceptRadio = val.conceptId;
              this.conceptId = "";
            });
          }
          // 没有ID说明是预标注数据, 清空右侧标签数据
        } else {
          this.emptyLabel();
        } */
      });
    });
    // 获取当前划词的UMLS
    AnnoEventsBus.$on("getUMLSConcept", (arg) => {
      this.getUMLSConcept(arg);
    });
    // 重置二级标签选择框
    /* AnnoEventsBus.$on("resetLabels", () => {
      this.$nextTick(() => {
        this.labelRadio = 0;
      });
    }); */
    // 重置UMLSConcept标签选择框
    AnnoEventsBus.$on("resetUMLSConcept", () => {
      this.resetUMLSConcept();
    });
    // 切换预标注来源，重置一级二级标签和UMLSConcept
    AnnoEventsBus.$on("emptyLabel", () => {
      this.$nextTick(() => {
        this.emptyLabel();
      });
    });
    // 监听实体选中状态变化
    AnnoEventsBus.$on("doChangeEntity", (data) => {
      // 当实体选中状态变化时，按钮状态会通过computed属性自动更新
      // 确保当前标签的示例内容被加载
      if (data && data.labelId) {
        this.loadLabelExample(data.labelId);
      }
    });
  },
  methods: {
    resetUMLSConcept() {
      this.conceptId = null;
      this.conceptRadio = null;
      this.umls = [];
    },
    getUMLSConcept(arg, inputConceptId) {
      this.resetUMLSConcept();
      if (!this.hasUmls) {
        return;
      }
      // this.umlsLoading = false;
      this.$http({
        url: this.$http.adornUrl("/note/getUMLS"),
        method: "get",
        params: this.$http.adornParams({
          conceptName: arg,
          conceptId: this.conceptId,
          projectId: this.annotation.projectId
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.umls = data.data;
          this.conceptRadio = inputConceptId;
        }
        /* else {
          this.$message({
            message: "UMLS Concept 获取超时，请联系管理员",
            type: "error",
            duration: 1500
          });
        } */
        // this.umlsLoading = false;
      }).catch(() => {
        // this.umlsLoading = false;
      });
    },
    addListener() {
      // window.addEventListener("keyup", this.handleLabelKeyUp);
    },
    removeListener() {
      // window.removeEventListener("keyup", this.handleLabelKeyUp);
    },
    handleLabelKeyUp(event) {
      // 如果弹框打开的，则不允许执行
      /* const switchStatus = sessionStorage.getItem('popoverSwitch');
      if (switchStatus === 'on') {
        return;
      } */

      // eslint-disable-next-line no-caller
      /* const e = event || window.event;
      if (!e) {
        return;
      }
      const { keyCode } = e;
      if (keyCode >= 49 && keyCode <= 58) {
        const index = keyCode - 49;
        this.changeLabel(this.labels[index].id, index, true);
      } */
    },
    searchConcept() {
      if (this.conceptRadio != null && this.conceptRadio !== "") {
        return;
      }
      if (this.conceptId != null && this.conceptId !== "") {
        this.umls = [];
        this.umlsLoading = true;
        this.$http({
          url: this.$http.adornUrl("/note/getUMLSById"),
          method: "get",
          params: this.$http.adornParams({
            conceptId: this.conceptId,
            projectId: this.annotation.projectId
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.umls = data.data;
          }
          /* else {
            this.$message({
              message: "UMLS Concept ID 查询超时，请联系管理员",
              type: "error",
              duration: 1500
            });
          } */
          this.umlsLoading = false;
        }).catch(() => {
          this.umlsLoading = false;
        });
      }
    },
    /**
     * 切换一级标签
     * @param labelId 一级标签ID
     * @param clear   是否清除Vuex中当前实体数据，新增实体用true，切换实体用false
     */
    changeLabel(labelId, clear) {
      //todo sw 标签改变
      if (clear && !this.hasMarkPermission()) return;

      // 判断是否是预标注页面的操作(如果是预标注)
      if (!this.isOriginal) {
        if (!this.currAnnoUniId) {
          this.$message({
            message: "请先选择预标注实体！",
            type: "warning"
          });
          // 重置用户的选择状态为未选择
          return false;
        }

        if (clear && !this.annotation.preEntity) {
          this.$message({
            message: "该实体已人工标注，请删除后重试！",
            type: "warning"
          });
          return false;
        }
      } else {
        this.conceptRadio = null;
        this.umls = [];
      }

      // 回显一级标签选中样式
      this.$store.commit("anno/setLabelId", labelId);

      // 如果是预标注页面，则不清空
      if (clear && this.isOriginal) {
        this.$store.commit("anno/setCurrAnno", null);
      }

      // 预标注页面，则需要存储选择的结果。并且刷新标签样式
      /* if (this.annotation.source !== "" && clear) {
        this.$http({
          url: this.$http.adornUrl("/note/addPreEntity"),
          method: "post",
          data: this.$http.adornData({
            noteId: this.annotation.noteId,
            textId: this.annotation.textId,
            source: this.annotation.source,
            labelId: labelId,
            start: this.annotation.start,
            end: this.annotation.end
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            // 显示标注颜色
            const anno = {
              start: this.annotation.start,
              end: this.annotation.end,
              label: labelId,
              textId: this.annotation.textId
            };
            AnnoEventsBus.$emit("addAnnotations_" + this.annotation.textId, anno);
          } else {
            this.$message({
              message: data.msg,
              type: "warning",
              duration: 1500
            });
          }
        });
      } */
    },
    emptyLabel() {
      this.labelRadio = null;
      this.conceptRadio = null;
      this.umls = [];
      this.currentLabelExample = "";
    },
    // 检查标注权限
    hasMarkPermission() {
      if (!this.editable) {
        this.$message({
          message: "当前状态无编辑权限！",
          type: "error"
        });
        return false;
      }
      return true;
    },
    // 获取文书的所有标签
    getLabels() {
      this.$http({
        url: this.$http.adornUrl(`/note/getFirstLabels/${this.annotation.projectId}`),
        method: "get"
      }).then(data => {
        const labels = data.data.data;
        this.labels = labels;
        // 注入样式
        let css = "";
        // 复合标注特有样式
        css += `.combo-anno::after {
          content: '';
          display: inline-block;
          width: 10px;
          height: 12px;
          background-repeat: no-repeat;
          background-image: url('data:image/png;base64,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');
          background-size: 100% 100%;
        }`;
        // 属性标注颜色
        const attrColor = Color("rgb(63,60,60)");
        if (attrColor) {
          // hexa()带透明度， hex()不带透明度
          const darker = attrColor.alpha(0.5).darken(0.2).hexa();
          const lighter = attrColor.alpha(0.5).lighten(0.3).hexa();
          const lighter2 = attrColor.alpha(0.5).darken(0.6).hexa();
          // 未使用的实体属性标注颜色
          css += ` .tag-attr {
                  border-left: 1px solid ${lighter2};
                  border-top: 1px solid ${lighter2};
                  border-right: 1px solid ${lighter2};
                  border-bottom: 3px solid ${lighter2};
                }
                .tag-attr:hover {
                  background-color: ${darker};
                }
                .tag-attr.select {
                  box-shadow: 1px 3px 1px 0px rgba(0,0,0,0.60);
                  background-color: ${lighter};
                }`;

          // 预标注中属性标注样式
          css += ` .tag-attr-gray {
                  border-left: 1px dashed ${lighter2};
                  border-top: 1px dashed ${lighter2};
                  border-right: 1px dashed ${lighter2};
                  border-bottom: 3px dashed ${lighter2};
                }
                .tag-attr-gray:hover {
                  background-color: ${darker};
                }
                .tag-attr-gray.select {
                  box-shadow: 1px 3px 1px 0px rgba(0,0,0,0.60);
                  background-color: ${lighter};
                }`;
          const usedAttrColor = Color("rgba(224,184,126,0.81)");
          const lighter3 = usedAttrColor.alpha(0.8).lighten(0.2).hexa();
          // 已被使用预标注中属性标注样式
          css += ` .tag-attr-gray-0 {
                  border-left: 1px dashed ${lighter3};
                  border-top: 1px dashed ${lighter3};
                  border-right: 1px dashed ${lighter3};
                  border-bottom: 3px dashed ${lighter3};
                }
                .tag-attr-gray-0:hover {
                  background-color: ${lighter3};
                }
                .tag-attr-gray-0.select {
                  box-shadow: 1px 3px 1px 0px rgba(0,0,0,0.60);
                  background-color: ${usedAttrColor.hex()};
                }`;
        }

        const labelMap = new Map();
        // 生成人工选择的实体标签样式
        if (this.labels != null && this.labels.length !== 0) {
          for (const [index, label] of labels.entries()) {
            const labelId = label.id;
            labelMap.set(labelId + "", label);
            const color = Color(label.color);
            // 常态下的颜色
            const normalColor = color.hex();
            const darker = color.darken(0.2).hex();
            const lighter = color.alpha(0.6).lighten(0.3).hexa();
            // 隐藏content（左下角属性）
            //预标注颜色
            css += ` .tag-gray-${labelId} {
                  border-style: dashed;
                  border-color: ${normalColor};
                  border-width: 2px
                }
                .tag-gray-${labelId}:hover {
                  background-color: ${darker};
                }
                .tag-gray-${labelId}.select {
                  box-shadow: 1px 3px 1px 0px rgba(0,0,0,0.60);
                  background-color: ${lighter};
                  border-width: 6px;
                }
                `;

            //实体标注颜色
            css += ` .tag-${label.id}  {
              background: ${normalColor};
            }
            .tag-${label.id}:hover {
              background-color: ${darker};
            }
            .tag-${label.id}.select {
              box-shadow: 1px 3px 1px 0px rgba(0,0,0,0.60);
              border-style: dashed;
              border-width: 6px;
              border-color: ${darker}
            }
            `;
            // background:  ${lighter};
            const lighter2 = color.lighten(0.05).alpha(0.2).hexa();
            const lighter3 = color.lighten(0.05).hex();
            //选中属性标注颜色
            css += ` .tag-attr-${label.id} {
                  border-left: 1px solid ${normalColor};
                  border-top: 1px solid ${normalColor};
                  border-right: 1px solid ${normalColor};
                  border-bottom: 3px solid ${normalColor};
                }
                .tag-attr-${label.id}:hover {
                  background-color: ${lighter3};
                }
                .tag-attr-${label.id}.select {
                  box-shadow: 1px 3px 1px 0px rgba(0,0,0,0.60);
                  border-left: 6px solid ${darker};
                  border-top: 6px solid ${darker};
                  border-right: 6px solid ${darker};
                  border-bottom: 8px solid ${darker};
                  background-color: ${lighter2};
                }
                `;
          }
        }

        // 生成多人标注时相同实体标注样式
        /* css += ` .multi-same-entity {
              font-size: 19px;
              box-shadow: 2px -3px 1px 0px rgba(95, 95, 243);
              border-right: 5px double rgba(245, 30, 30, 1);
          } `; */

        css += ` .multi-same-entity::before {
              content: '';
              display: inline-block;
              width: 16px;
              height: 18px;
              margin-bottom: -3px;
              background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAAA8xJREFUeF7tm11y2yAQgHeV3KPOLVpP68Qnafxk36LJLeynpCeJ7HTc3qLqQWw6C5Kif4FYBHKiGU88DgL2Y9mFZUFw/GxePs/g+noG5/MdNSUQPgGIGQDOAIA+ieqCSABQfkeAPf3dLo7PjrtHbfE/UugouhcobgFQCj7wISAxAXEFgxXAZj9/YBC6i1WCAn5ub48PA4HWXmMBoASHH1yd0qiHDYQVgM3r1zshzk/pXNboN3uRBAEebabHIAA0x8UVPlnOb04aCZ7Oy+3yT2pQ9as2BpCO+ot+E6OVTBCj1fbbr9ikRSMAm8P8XgCQyof6GNsGbQAeDN1gyCjgUddTaAFYH768BDTfdcE87xbHVV/hXgBTGvmqsDqa0AkgYIPXN7D5//sgtAK4BOFTCp3eoRGA8vPRX23M4RdsXSc0Apio0esZBhHvFr+XNTtR/eGCVL++8cFoWV0o1TTgMkc/Z5HsFsebIpkSgAms9KytDQKsipunEoD1YU6Gj6I0l/yUtCAH8B5GPxvVohbkAC587lc0+s0jFADMxUT0Xu74bENvmHoECWAq6l81YDZam9UlAawPc9rj34esAVXh7QdOTYMMQNDq3yQ8w8BJb4Chq3+b8BwrVrIDQQNwKTxpEG2VMdT571p4VgDUWTidY7iK7gTIAxKr1eQYwiuDL2K0cSUZxWIAMo0lUNh8EITxhM8B2K3/G92TCqgYQxhXeKkCCdkASxfYHGgw1QQPwnMBkCQbQ9C6EDwJLztOGsC1BR4Ewafw6RRgPfQwguBZ+MwLsO8DtCD4F15NXXR08tMJoe1Mn2N5a7ihcwag1TC2ddCD8JQgtULHDWsdUDruQ6tSKADuT4E6IfgSnqjsFkdM4wGsnqCJeLNNUDlGvrJNZJ/GDImVIPgcebkASs8HFAD30yDTijSgSdmifkNweDrfUFLVOw2Lvy3dPw5GMt3U3bgYLjRCLN58NGYfZg5R1nqfOg9HA8wAZaZaj13U8gN8uydmiUvVZcdhxR8/UmSaiF+gQTRLkhp5ceRS61XYqyE3KGu0O1Ey/OToXnhdwqdL4u46HAVMejvOUaAvS1QPQH4BatQrMQzyN4frqxX3Jktn9kDdApsGBJ2R17IBVVpTmA5twdY2ldLSgOLLAbtI91dmyhunsC5NVTNAdY2IsQaUtEG6SfHd422SQaPeuxTWpZcbSKa8AIN2jS9HsdmAtooY7wt3cWATfJAX0B0hCUNpxa197E/EKHCvewtMt49OAVS9BsGg31Ig9K31+jwK+Cffj6LY9BKkqfBU/j9ylHXjaOkeCAAAAABJRU5ErkJggg==");
              background-size: 100% 100%;
              background-repeat: no-repeat;
              background-color: yellow;
              border-radius: 2px;
              border: 1px solid rgba(255, 255, 255, 0.06);
          } `;

        this.$store.commit("anno/setCurrLabelMap", labelMap);
        this.labelsArray = this.$_.chunk(this.labels, this.lineNumber);
        this.css = css;

        this.$nextTick(() => {
          const radios = document.querySelectorAll(".entity-radio"); // 获取所有 el-radio 组件
          const labelColors = document.querySelectorAll(".entity-colors");
          const colors = [];
          labelColors.forEach(item => {
            // 获取 span 标签的计算后的样式值
            colors.push(window.getComputedStyle(item).color); // 获取颜色并将颜色值添加到数组中
          });
          radios.forEach((radio, index) => {
            radio.querySelector(".el-radio__inner")
                .style.setProperty("background-color", colors[index]); // 设置 el-radio__inner 元素的颜色
          });
        });

        // 显示子标签,默认显示第一个
        // if (!this.$_.isEmpty(this.labels)) {
        //   let firstLabel = this.labels[0]
        //   this.changeLabel(firstLabel.id, 0, true)
        // }

        // 通知标签和样式加载完成
        AnnoEventsBus.$emit("entityLabelsLoaded");
      });
    },
    checkAnnoLabel(val) {
      this.changeLabel(val, false);
      this.loadLabelExample(val);
    },
    addAnnotate() {
      // 批注值改变就更新到数据库
      const currAnnoUniId = this.currAnnoUniId;
      if (!this.isOriginal || !currAnnoUniId) {
        // 非原文以及未选中实体时，不能修改注释信息
        this.annotate = null;
        return;
      }
      const domObj = document.querySelector(`.tag-section span.tag[uniqueid="${currAnnoUniId}"]`);
      const is_attr = domObj.getAttribute("is_attr");
      if (isAttrTag(is_attr)) {
        //属性标注不能添加注释信息
        this.annotate = null;
        return;
      }
      let val = this.annotate;
      let annotateStr = null;
      if (val) {
        annotateStr = trimStr(val);
      } else {
        annotateStr = val;
      }
      const multiple = this.batchSwitch;
      if (multiple) {
        this.$confirm("确认要批量更新批注信息吗？", "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
            .then(() => {
              this.doAddAnnotate(annotateStr, multiple);
            })
            .catch(() => {
            });
      } else {
        this.doAddAnnotate(annotateStr, multiple);
      }
    },
    doAddAnnotate(annotateStr, multiple) {
      AnnoEventsBus.$emit("doSaveAnnotate", annotateStr, multiple);
    },
    conceptClick(e, preferredName, conceptType) {
      if (this.conceptRadio) {
        this.conceptRadio = "";
        this.currPreferredName = null;
        this.conceptType = null;
      } else {
        this.conceptRadio = e;
        this.currPreferredName = preferredName;
        this.conceptType = conceptType;
      }
      // e === this.conceptRadio ? this.conceptRadio = "" : this.conceptRadio = e;
      // e === this.conceptRadio ? this.currPreferredName = null : this.currPreferredName = preferredName;
      this.conceptChange();
    },
    // 点击UMLS
    conceptChange() {
      AnnoEventsBus.$emit("hied-popover");

      if (!this.hasMarkPermission()) return;

      if (!this.currAnnoUniId) {
        this.$message({
          message: "请先标注文本",
          type: "warning"
        });
        this.conceptRadio = null;
        return;
      }
      if (this.currLabelId === 0 || this.currLabelId === "" || this.currLabelId.toString().indexOf("black") !== -1) {
        this.$message({
          message: "请先选择一级标签",
          type: "warning"
        });
        this.conceptRadio = null;
        return;
      }
      // 批量标注
      const multiple = this.batchSwitch;
      if (multiple) {
        this.$confirm("确认批量编辑该实体同批次的UMLS Concept？", "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          this.updateEntityUmls(multiple);
        }).catch(() => {
        });
      } else {
        this.updateEntityUmls(multiple);
      }
    },
    updateEntityUmls(multiple) {
      const conceptId = this.conceptRadio;
      // 保存
      this.$http({
        url: this.$http.adornUrl("/note/updateEntityUmls"),
        method: "post",
        data: this.$http.adornData({
          entityId: this.currAnnoUniId,
          roleId: this.roleId,
          conceptId: conceptId,
          conceptText: this.currPreferredName,
          conceptType: this.conceptType,
          multiple: multiple
        })
      }).then(({data}) => {
        /* if (data && data.code !== 0) {
          this.$message({
            message: "操作失败",
            type: "error",
            duration: 1500
          });
        } */
      });
    },
    gotoByConceptId(val) {
      window.open(`https://uts.nlm.nih.gov/uts/umls/concept/${val.conceptId}`);
    },
    // 加载标签规则示例
    loadLabelExample(labelId) {
      if (!labelId) {
        this.currentLabelExample = "";
        return;
      }

      // 根据标签ID找到标签名称
      const selectedLabel = this.labels.find(label => label.id + "" === labelId + "");
      if (!selectedLabel) {
        this.currentLabelExample = "";
        return;
      }

      // 调用后端接口获取该标签的规则示例
      this.$http({
        url: this.$http.adornUrl("/label/entity/getLabelRuleExample"),
        method: "get",
        params: this.$http.adornParams({
          projectId: this.annotation.projectId,
          labelName: selectedLabel.name
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.currentLabelExample = data.data || "";
        } else {
          this.currentLabelExample = "";
        }
      }).catch(() => {
        this.currentLabelExample = "";
      });
    },

    // 打开添加到示例弹窗
    openAddToExampleDialog() {
      if (!this.canAddToExample) {
        this.$message({
          message: "请先选中一个已标注的实体！",
          type: "warning"
        });
        return;
      }

      const loading = this.$loading({
        lock: true
      });

      // 重置弹窗数据
      this.examplePreview = "";
      this.selectedRuleForExample = "";
      this.exampleType = "positive";
      this.availableRules = [];
      this.currentSelectedEntity = null;

      // 获取当前选中实体的信息
      this.getCurrentSelectedEntityInfo()
        .then(() => {
          // 获取当前标签的规则列表
          return this.getAvailableRules();
        })
        .then(() => {
          // 生成预览示例
          this.generateExamplePreview();
          loading.close();
          this.addToExampleDialogVisible = true;
        })
        .catch((error) => {
          loading.close();
          this.$message({
            message: error.message || "获取数据失败！",
            type: "error"
          });
        });
    },

    // 获取当前选中实体的信息
    getCurrentSelectedEntityInfo() {
      return new Promise((resolve, reject) => {
        const uniId = this.currAnnoUniId;
        if (!uniId) {
          reject(new Error("未选中实体"));
          return;
        }

        // 从DOM中获取实体信息
        const entityElement = document.querySelector(`span.tag[uniqueid="${uniId}"]`);
        if (!entityElement) {
          reject(new Error("找不到选中的实体"));
          return;
        }

        // 获取实体所在的段落
        const paragraphElement = entityElement.closest("p");
        if (!paragraphElement) {
          reject(new Error("找不到实体所在的段落"));
          return;
        }

        this.currentSelectedEntity = {
          content: entityElement.innerText,
          paragraphId: paragraphElement.getAttribute("data-id"),
          paragraphContent: paragraphElement.innerText,
          start: parseInt(entityElement.getAttribute("start")),
          end: parseInt(entityElement.getAttribute("end"))
        };

        resolve();
      });
    },

    // 获取当前标签的可用规则
    getAvailableRules() {
      return new Promise((resolve, reject) => {
        const selectedLabel = this.labels.find(label => label.id + "" === this.selectedLabel + "");
        if (!selectedLabel) {
          reject(new Error("未找到当前标签"));
          return;
        }

        this.$http({
          url: this.$http.adornUrl("/label/entity/getLabelRulesList"),
          method: "get",
          params: this.$http.adornParams({
            projectId: this.annotation.projectId,
            labelName: selectedLabel.name
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.availableRules = data.data || [];
            resolve();
          } else {
            reject(new Error(data.msg || "获取规则失败"));
          }
        }).catch(() => {
          reject(new Error("获取规则失败"));
        });
      });
    },

    // 生成预览示例
    generateExamplePreview() {
      if (!this.currentSelectedEntity) {
        return;
      }

      const articleId = this.annotation.articleId;
      const paragraphContext = this.currentSelectedEntity.paragraphContent;
      const entityContent = this.currentSelectedEntity.content;

      // 在段落上下文中高亮实体
      const highlightedContext = paragraphContext.replace(
        new RegExp(entityContent.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"), "g"),
        `==${entityContent}==`
      );

      this.examplePreview = `（${articleId}）${highlightedContext}`;
    },

    // 保存示例
    doSaveExample() {
      // 验证必填项
      if (!this.selectedRuleForExample) {
        this.$message({
          message: "请选择规则！",
          type: "warning"
        });
        return;
      }

      if (!this.examplePreview.trim()) {
        this.$message({
          message: "示例内容不能为空！",
          type: "warning"
        });
        return;
      }

      this.$confirm(
        `确定要添加当前示例到规则中吗？`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      ).then(() => {
        this.submitExample();
      });
    },

    // 提交示例
    submitExample() {
      const loading = this.$loading({
        lock: true
      });

      // 获取当前选中标签的名称
      const selectedLabel = this.labels.find(label => label.id + "" === this.selectedLabel + "");
      const labelName = selectedLabel ? selectedLabel.name : "";

      const exampleData = {
        projectId: this.annotation.projectId,
        ruleId: this.selectedRuleForExample,
        exampleContent: this.examplePreview,
        exampleType: this.exampleType,
        entityId: this.currAnnoUniId,
        labelName: labelName
      };

      this.$http({
        url: this.$http.adornUrl("/label/entity/addRuleExample"),
        method: "post",
        data: this.$http.adornData(exampleData)
      })
        .then(({ data }) => {
          loading.close();
          if (data && data.code === 0) {
            this.$message({
              message: "示例添加成功！",
              type: "success"
            });
            this.addToExampleDialogVisible = false;
            // 刷新当前标签的示例显示
            this.loadLabelExample(this.selectedLabel);
          } else {
            this.$message({
              message: data.msg || "示例添加失败！",
              type: "error"
            });
          }
        })
        .catch(() => {
          loading.close();
          this.$message({
            message: "示例添加失败！",
            type: "error"
          });
        });
    },

    // 关闭添加到示例弹窗
    closeAddToExampleDialog() {
      this.addToExampleDialogVisible = false;
      this.onAddToExampleDialogClose();
    },

    // 添加到示例弹窗关闭事件处理
    onAddToExampleDialogClose() {
      // 清理数据
      this.examplePreview = "";
      this.selectedRuleForExample = "";
      this.exampleType = "positive";
      this.availableRules = [];
      this.currentSelectedEntity = null;
    }
  }
};
</script>
<style>
.single-show {
  border-radius: 10px;
}
</style>
<style scoped lang="scss">
.rightBox {
  display: flex;
  flex-direction: column;
  padding-right: 10px;
  overflow: hidden; // 改为hidden，让子元素控制滚动

  h3 {
    font-weight: 600;
    padding-left: 10px;
    border-left: 3px solid #409eff;
    font-size: 14px;
    margin: 12px 0;
  }

  ::v-deep .el-input-group__append {
    background-color: #409eff;
    color: #fff;

    i {
      font-weight: 900;
    }
  }

  .card-bottom {
    flex-grow: 1;
    padding-right: 10px;
  }

  .el-tree__empty-block {
    .el-tree__empty-text {
      font-size: 12px;
    }
  }
}

.el-input {
  ::v-deep .el-input__inner,
  ::v-deep .el-textarea__inner {
    &::placeholder {
      font-size: 12px;
    }
  }
}

::v-deep .el-textarea__inner {
  &::placeholder {
    font-size: 12px;
  }
}

//::v-deep .el-textarea__inner::placeholder {
//  font-size: 12px;
//}

h3:last-child {
  margin: 0;
}

.button-area {
  text-align: left;

  & > * {
    margin-bottom: 10px;
  }
}

.entity-radio {
  ::v-deep.el-radio__label {
    font-size: 14px;
  }
}

.empty-text {
  margin: 20% 50%;
  color: #6f7180;
}

.el-radio {
  display: block;
  line-height: 23px;
  white-space: normal;
  margin-right: 0;

  ::v-deep.el-radio__input.is-checked {
    .el-radio__inner {
      border-color: transparent;
    }
  }
}

.el-radio + .el-radio {
  margin-left: 0;
}

.box-card-label {
  // 右侧实体标签卡片 - 自适应高度
  flex-shrink: 0; // 不压缩
  overflow: visible; // 显示所有内容
}

// 实体批注信息区域样式
.annotation-section {
  flex-shrink: 0; // 不压缩
  margin-bottom: 10px;
}

// 当前标签标注示例区域样式
.example-section {
  flex: 1; // 占用剩余空间
  display: flex;
  flex-direction: column;
  min-height: 0; // 允许flex子元素缩小

  .example-header {
    flex-shrink: 0; // 头部不压缩
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    margin-top: 10px;
  }

  .example-content {
    flex: 1; // 内容区域占用剩余空间
    min-height: 0; // 允许缩小
    overflow: hidden; // 隐藏溢出，让mavon-editor处理滚动

    .box-card {
      height: 100%;
    }
  }
}

// 标签规则示例样式
::v-deep .v-note-wrapper {
  border: none !important;
  box-shadow: none !important;
  height: 100% !important; // 占满容器高度
}

::v-deep .v-note-wrapper .v-note-panel {
  border: none !important;
}

::v-deep .v-note-wrapper .v-note-panel .v-note-show {
  background-color: #ffffff !important;
  padding: 15px;
  font-size: 13px;
  line-height: 1.6;
  border: 1px solid #e8e8e8;
  min-height: 200px;
  overflow-y: auto !important; // 确保内容可以滚动
  height: calc(100% - 40px) !important; // 减去工具栏高度
}

::v-deep .v-note-wrapper .v-note-op {
  border-bottom: 1px solid #e8e8e8;
  background-color: #f8f8f8;
}

::v-deep .v-note-wrapper .v-note-op .v-left-item,
::v-deep .v-note-wrapper .v-note-op .v-right-item {
  flex: none;
}

::v-deep .v-note-wrapper .v-note-op .v-left-item .op-icon,
::v-deep .v-note-wrapper .v-note-op .v-right-item .op-icon {
  display: none;
}

::v-deep .v-note-wrapper .v-note-op .v-right-item .op-icon.fa-arrows-alt {
  display: inline-block;
}
</style>
