<template>
  <el-card
    shadow="never"
    :style="`height: ${fullHeight}px`"
    class="attribute-container"
  >
    <div v-if="showTipFlag">
      <el-alert
        :closable="false"
        type="info"
        show-icon
        center
        title="划词进行属性标注 或 点击实体进行属性关联"
      >
      </el-alert>
    </div>
    <div v-else>
      <el-descriptions title="" :column="1" border style="font-size: 13px">
        <el-descriptions-item
          label="实体名称"
          content-class-name="my-entity-content"
        >
          <span :title="annoContents | attrFullContent">{{
            annoContents | attrFullContent
          }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="实体标签"
          >{{ annoLabelName }}
        </el-descriptions-item>
      </el-descriptions>
      <div v-if="attrList && attrList.length > 0">
        <div class="att">属性列表</div>
        <div class="attributes">
          <div
            v-for="(att, index) in attrList"
            :key="index"
            style="margin: 10px 8px"
          >
            <div
              class="att-list"
              style="
                padding: 6px;
                width: 15%;
                display: inline-block;
                color: #909399;
                font-size: 13px;
              "
              v-text="att.name"
            ></div>
            <div
              @drop="dropAttrData($event, index)"
              @dragover="allowAttrDrop($event)"
              style="width: 75%; display: inline-block"
            >
              <el-select
                :key="'i_' + index + 'attr-select' + att.id"
                style="width: 100%"
                v-model="att.values"
                size="mini"
                multiple
                filterable
                allow-create
                default-first-option
                placeholder=""
                value-key="value"
                @change="saveAttr($event, index)"
                @focus="attrFocus(index)"
              >
                <el-option
                  v-for="(item, index2) in att.options"
                  :key="'i_' + index + 'i2_' + index2 + 'attr-opt' + item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </div>
            <el-tooltip
              style="margin-left: 5px"
              class="item"
              effect="dark"
              content="清空"
              placement="top-start"
            >
              <el-button
                type="danger"
                icon="el-icon-delete"
                size="mini"
                circle
                :disabled="!editable"
                @click="clearAttr(index)"
              ></el-button>
            </el-tooltip>
          </div>
        </div>
      </div>

      <!-- 当前属性标注规范 -->
      <div
        v-if="currentAttrExample"
        style="
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 10px;
          margin-top: 10px;
        "
      >
        <h3 class="att" style="margin: 0">当前属性标注规范</h3>
        <el-button
          v-if="showAddToExampleBtn"
          @click="openAddToExampleDialog"
          type="primary"
          size="mini"
          plain
          icon="el-icon-plus"
          :disabled="!canAddToExample"
        >
          添加到示例
        </el-button>
      </div>
      <div v-if="currentAttrExample" class="box-card">
        <mavon-editor
          v-model="currentAttrExample"
          :subfield="false"
          :defaultOpen="'preview'"
          :toolbarsFlag="false"
          :editable="false"
          :scrollStyle="true"
          :ishljs="true"
          :boxShadow="false"
          :toolbars="{
            fullscreen: true
          }"
          style="z-index: 1"
        />
      </div>
    </div>

    <!-- 添加到示例弹窗 -->
    <el-dialog
      title="添加到示例"
      :visible.sync="addToExampleDialogVisible"
      width="80%"
      max-height="80vh"
      @close="onAddToExampleDialogClose"
    >
      <div>
        <!-- 预览示例 -->
        <div style="margin-bottom: 15px">
          <div style="margin-bottom: 8px">
            <strong>预览示例：</strong>
            <span
              >(如果需要强调被标注的属性，请用==包裹它的两端，比如：==高亮的属性==)</span
            >
          </div>
          <el-input
            type="textarea"
            :rows="10"
            placeholder="请编辑示例内容"
            v-model="examplePreview"
            show-word-limit
          >
          </el-input>
        </div>

        <!-- 选择规则 -->
        <div style="margin-bottom: 15px">
          <div style="font-weight: bolder; margin-bottom: 8px">
            选择规则：<span style="color: red">*</span>
          </div>
          <el-radio-group v-model="selectedRuleForExample">
            <el-radio
              v-for="rule in availableRules"
              :key="rule.value"
              :label="rule.value"
              style="display: block; margin-bottom: 8px"
            >
              {{ rule.label }}
            </el-radio>
          </el-radio-group>
          <div
            v-if="availableRules.length === 0"
            style="color: #999; font-style: italic"
          >
            当前属性暂无可用规则
          </div>
        </div>

        <!-- 示例类型 -->
        <div style="margin-bottom: 15px">
          <div style="font-weight: bolder; margin-bottom: 8px">示例类型：</div>
          <el-radio-group v-model="exampleType">
            <el-radio label="positive">正例</el-radio>
            <el-radio label="negative">反例</el-radio>
          </el-radio-group>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeAddToExampleDialog">取消</el-button>
        <el-button type="primary" icon="el-icon-check" @click="doSaveExample">
          确认
        </el-button>
      </span>
    </el-dialog>
  </el-card>
</template>

<script>
import { isBlank, joinArray, sleep, trimStr } from "@/utils";
import { AnnoEventsBus } from "@/utils/bus";
import { mavonEditor } from "mavon-editor";
import "mavon-editor/dist/css/index.css";

const attrEntityPrefix = "_##_";
export default {
  name: "attribute",
  components: {
    mavonEditor
  },
  data() {
    return {
      fullHeight: document.documentElement.clientHeight - 160,
      attrList: [],
      annoContents: [],
      annoLabelName: "",
      annoid: null,
      currLabelId: null,
      currentAttrExample: "", // 当前属性的规则示例
      // 添加到示例相关数据
      addToExampleDialogVisible: false,
      examplePreview: "",
      selectedRuleForExample: "",
      exampleType: "positive",
      availableRules: [],
      currentSelectedAttribute: null, // 当前选中的属性信息
      currentAttrName: "" // 当前属性名称
    };
  },
  props: {
    activeName: {
      require: true,
      type: String
    },
    isOriginal: {
      require: true,
      type: Boolean
    }
  },
  mounted() {
    this.fullHeight = document.documentElement.clientHeight - 160;
    window.onresize = () => {
      return (() => {
        this.fullHeight = document.documentElement.clientHeight - 160;
      })();
    };
    AnnoEventsBus.$on("clearAttrData", () => {
      this.clearAttrData();
    });

    AnnoEventsBus.$on("doRefreshAttrByLabel", () => {
      this.refreshAttrByLabel();
    });

    AnnoEventsBus.$on("doAddAttrEv", (entityInfo) => {
      this.doAddAttrEvent(entityInfo);
    });
  },
  computed: {
    roleId() {
      return this.$store.state.user.roleId;
    },
    currLabelMap: function () {
      return this.$store.state.anno.currLabelMap;
    },
    annotation: function () {
      return this.$store.state.anno.annotation;
    },
    editable: function () {
      return this.$store.state.anno.editable;
    },
    showTipFlag: function () {
      return !this.annoLabelName && !this.annoContents.length > 0;
    },
    // 是否显示"添加到示例"按钮
    showAddToExampleBtn() {
      return (
        this.roleId === this.$RoleEnum.auditor ||
        this.roleId === this.$RoleEnum.projectAdmin
      );
    },
    // 是否可以添加到示例（需要选中实体和属性）
    canAddToExample() {
      return !!this.annoid && !!this.currentAttrName && this.currentAttrExample;
    }
  },
  watch: {
    activeName: {
      handler(newVal, oldVal) {
        if (newVal === "attribute") {
          this.initAttrByLabel();
        }
      },
      immediate: false
    }
  },
  filters: {
    attrPartContent: function (val) {
      return joinArray(val, " / ", 70);
    },
    attrFullContent: function (val) {
      return joinArray(val, " / ");
    }
  },
  methods: {
    doAddAttrEvent(entityInfo) {
      let attrList = this.attrList;
      if (entityInfo && entityInfo.isAttr === "1") {
        let index = -1;
        let len = attrList.length;
        for (let i = 0; i < len; i++) {
          if (trimStr(attrList[i].id) === trimStr(entityInfo.currAttrId)) {
            index = i;
            break;
          }
        }
        if (index > -1) {
          this.doAddAttrSelect(entityInfo.id, entityInfo.text, index);
        }
      }
    },
    clearAttrData() {
      this.initAttrByLabel(null, null);
      this.currentAttrExample = "";
      this.currentAttrName = "";
    },
    refreshAttrByLabel() {
      this.initAttrByLabel(this.currLabelId, this.annoid);
    },
    initAttrByLabel(labelId, annoid) {
      this.annoid = annoid;
      this.currLabelId = labelId;

      this.attrList = [];
      this.annoContents = [];
      this.annoLabelName = "";
      if (!labelId || !annoid) {
        /* this.$message({
          message: "请先选中标注实体",
          type: "warning",
          duration: 1500
        }); */
        return;
      }
      /* const anno = {
        uniqueid: uniqueid,
        labelId: labelId
      };
      this.$store.commit("anno/setAnno", anno); */
      const entityDoms = document.querySelectorAll(
        `span[annoid="${annoid}"][basetag="1"]`
      );
      let annoContents = [];
      entityDoms.forEach((item) => {
        annoContents.push(trimStr(item.innerText));
      });
      this.annoContents = annoContents;
      const labelInfo = this.currLabelMap.get(labelId);
      if (labelInfo) {
        this.annoLabelName = labelInfo.name;
      }
      this.$http({
        url: this.$http.adornUrl("/attr/findAllAttrByEntityLabelId"),
        method: "get",
        params: this.$http.adornParams({
          entityLabelId: labelId,
          entityId: this.annoid
        })
      }).then(({ data }) => {
        if (data && data.code !== 0) {
          this.$message({
            message: data.msg,
            type: "warning",
            duration: 1500
          });
        } else {
          let result = data.data;
          if (!this.isEmptyArray(result)) {
            result = result.map((item) => {
              if (this.isEmptyArray(item.values)) {
                item.values = [];
              }
              if (this.isEmptyArray(item.options)) {
                item.options = [];
              }
              return item;
            });
          }
          this.attrList = result;
        }
      });
    },
    isEmptyArray(arr = []) {
      return !arr || arr.length === 0;
    },
    clearAttr(index) {
      this.$confirm("确认要清空所有属性吗？", "警告", {
        confirmButtonText: "清空",
        cancelButtonText: "取消"
      }).then(() => {
        this.attrList[index].values = [];
        this.saveAttr(null, index);
      });
    },
    hasMarkPermission(hideMsg) {
      if (!this.editable || !this.isOriginal) {
        if (!hideMsg) {
          this.$message({
            message: "当前状态无编辑权限！",
            type: "error"
          });
        }
        return false;
      }
      return true;
    },
    dropAttrData(ev, i) {
      if (!this.hasMarkPermission()) {
        return;
      }
      let id = ev.dataTransfer.getData("id");
      let text = ev.dataTransfer.getData("text");
      this.doAddAttrSelect(id, text, i);
    },
    doAddAttrSelect(id, text, i, notShowMsg) {
      id = trimStr(id);
      text = trimStr(text);
      if (!id || !text) {
        this.$message({
          message: "无效的属性标注",
          type: "warning"
        });
        return;
      }
      // const labelId = ev.dataTransfer.getData("labelId");
      if (!this.addOptionItem(i, id, text, notShowMsg)) {
        return;
      }
      this.saveAttr(this.attrList[i].values, i);
    },
    addOptionItem(i, id, text, notShowMsg) {
      // await sleep(800);
      // 添加下拉框选项
      id = trimStr(id);
      text = trimStr(text);
      let result = null;
      if (text === "") {
        if (!notShowMsg) {
          this.$message({
            message: "属性内容不能为空",
            type: "warning"
          });
        }
        return result;
      }
      let alreadyExists = false;
      if (id === "") {
        id = text;
      } else {
        id = attrEntityPrefix + id;
      }
      for (const v of this.attrList[i].options) {
        if (v.value === id) {
          alreadyExists = true;
          if (!notShowMsg) {
            this.$message({
              message: "该实体已存在",
              type: "warning"
            });
          }
          break;
        }
      }
      if (!alreadyExists) {
        result = this.initOption(id, text);
        this.attrList[i].options.push(result);
        const set = new Set(this.attrList[i].values);
        set.add(id);
        this.attrList[i].values = [...set];
      }
      return result;
    },
    initOption(value, label) {
      return {
        value: value,
        label: label
      };
    },
    allowAttrDrop(ev) {
      ev.preventDefault();
    },
    getDateByVals(vals, index) {
      const data = [];
      if (vals && vals.length > 0) {
        const size = vals.length;
        const options = this.attrList[index].options;
        const optionsMap = new Map();
        if (options) {
          for (let i = 0; i < options.length; i++) {
            optionsMap.set(options[i].value, options[i]);
          }
        }

        for (let i = 0; i < size; i++) {
          const val = vals[i];
          const item = optionsMap.get(val);
          if (item) {
            if (item.value.startsWith(attrEntityPrefix)) {
              data.push(
                this.initOption(
                  item.value.replace(attrEntityPrefix, ""),
                  item.label
                )
              );
            } else {
              data.push(this.initOption(null, item.label));
            }
          } else {
            let newItem = this.addOptionItem(index, null, val, true);
            if (newItem) {
              data.push(this.initOption(null, newItem.label));
            }
          }
        }
      }
      return data;
    },
    saveAttr(vals, index) {
      if (!this.hasMarkPermission()) {
        return;
      }
      const attrData = this.getDateByVals(vals, index);
      const param = {
        taskId: this.annotation.taskId,
        projectId: this.annotation.projectId,
        batchId: this.annotation.batchId,
        articleId: this.annotation.articleId,
        entityId: this.annoid,
        attrLabelId: this.attrList[index].id,
        data: attrData,
        roleId: this.roleId
      };
      this.$http({
        url: this.$http.adornUrl("/attr/addAttributeEntity"),
        method: "post",
        data: this.$http.adornData(param)
      }).then(({ data }) => {
        if (data.msg === "success" && data.code === 0) {
          // AnnoEventsBus.$emit("hideAnnoPopover");
          if (data.data) {
            AnnoEventsBus.$emit(
              "updateCurrDoc",
              data.data.docAllInfo,
              param.entityId
            );
          }
        } else {
          this.attrList[index].options.pop();
          this.attrList[index].values.pop();
          /* this.$message({
            message: data.msg,
            type: "warning",
            duration: 1500
          }); */
        }
      });
    },
    attrFocus(index) {
      // if (!this.hasMarkPermission(true)) {
      //   return;
      // }
      let item = this.attrList[index];
      if (item) {
        let data = {
          currAttrTitle: item.name,
          currAttrId: item.id
        };
        this.$store.dispatch("attr/setCurrAttr", this.$_.cloneDeep(data));
        // 存储当前属性名称，用于添加示例功能
        this.currentAttrName = item.name;
        // 加载属性规则示例
        this.loadAttrExample(item.name);
      }
    },
    // 加载属性规则示例
    loadAttrExample(attrName) {
      if (!attrName) {
        this.currentAttrExample = "";
        return;
      }

      // 调用后端接口获取该属性的规则示例
      this.$http({
        url: this.$http.adornUrl("/label/entity/getLabelRuleExample"),
        method: "get",
        params: this.$http.adornParams({
          projectId: this.annotation.projectId,
          labelName: attrName
        })
      })
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.currentAttrExample = data.data || "";
          } else {
            this.currentAttrExample = "";
          }
        })
        .catch(() => {
          this.currentAttrExample = "";
        });
    },

    // 打开添加到示例弹窗
    openAddToExampleDialog() {
      if (!this.canAddToExample) {
        this.$message({
          message: "请先选中一个已标注的实体和属性！",
          type: "warning"
        });
        return;
      }

      const loading = this.$loading({
        lock: true
      });

      // 重置弹窗数据
      this.examplePreview = "";
      this.selectedRuleForExample = "";
      this.exampleType = "positive";
      this.availableRules = [];
      this.currentSelectedAttribute = null;

      // 获取当前选中属性的信息
      this.getCurrentSelectedAttributeInfo()
        .then(() => {
          // 获取当前属性的规则列表
          return this.getAvailableRules();
        })
        .then(() => {
          // 生成预览示例
          this.generateExamplePreview();
          loading.close();
          this.addToExampleDialogVisible = true;
        })
        .catch((error) => {
          loading.close();
          this.$message({
            message: error.message || "获取数据失败！",
            type: "error"
          });
        });
    },

    // 获取当前选中属性的信息
    getCurrentSelectedAttributeInfo() {
      return new Promise((resolve, reject) => {
        if (!this.currentAttrName) {
          reject(new Error("未选中属性"));
          return;
        }

        // 获取当前属性的选中值
        const selectedValues = this.getSelectedAttributeValues();
        if (!selectedValues || selectedValues.length === 0) {
          reject(new Error("当前属性未选择任何值"));
          return;
        }

        // 查找所有相关的实体
        const relatedEntities = this.findRelatedEntities(selectedValues);
        if (relatedEntities.length === 0) {
          reject(new Error("找不到与当前属性值相关的实体标注"));
          return;
        }

        // 合并所有相关实体的信息
        this.currentSelectedAttribute =
          this.mergeEntityContexts(relatedEntities);

        resolve();
      });
    },

    // 获取当前属性的选中值
    getSelectedAttributeValues() {
      const selectedValues = [];

      // 遍历当前属性的所有选项，找出被选中的值
      for (let i = 0; i < this.attrList.length; i++) {
        const attr = this.attrList[i];
        if (attr.name === this.currentAttrName) {
          selectedValues.push(...attr.values);
        }
      }
      return selectedValues;
    },

    // 查找与属性值相关的所有实体
    findRelatedEntities(selectedValues) {
      const relatedEntities = [];

      selectedValues.forEach((value) => {
        // 如果值包含 _##_ 前缀，说明是实体ID引用
        if (typeof value === "string" && value.includes("_##_")) {
          const entityId = value.split("_##_")[1];
          if (entityId) {
            const entityElement = document.querySelector(
              `span.tag[annoid="${entityId}"]`
            );
            if (entityElement) {
              const paragraphElement = entityElement.closest("p");
              if (paragraphElement) {
                relatedEntities.push({
                  entityId: entityId,
                  entityElement: entityElement,
                  paragraphElement: paragraphElement,
                  entityContent: entityElement.innerText,
                  paragraphContent: paragraphElement.innerText,
                  paragraphId: paragraphElement.getAttribute("data-id"),
                  start: parseInt(entityElement.getAttribute("start")),
                  end: parseInt(entityElement.getAttribute("end")),
                  attributeValue: value
                });
              }
            }
          }
        } else {
          // 对于非实体ID引用的值（如手动输入的文本），尝试在页面中查找匹配的文本
          const textElements = document.querySelectorAll("span.tag");
          textElements.forEach((element) => {
            if (element.innerText.trim() === value) {
              const paragraphElement = element.closest("p");
              if (paragraphElement) {
                relatedEntities.push({
                  entityId: element.getAttribute("uniqueid"),
                  entityElement: element,
                  paragraphElement: paragraphElement,
                  entityContent: element.innerText,
                  paragraphContent: paragraphElement.innerText,
                  paragraphId: paragraphElement.getAttribute("data-id"),
                  start: parseInt(element.getAttribute("start")),
                  end: parseInt(element.getAttribute("end")),
                  attributeValue: value
                });
              }
            }
          });
        }
      });

      return relatedEntities;
    },

    // 合并多个实体的上下文信息
    mergeEntityContexts(relatedEntities) {
      const uniqueParagraphs = new Map();
      const entityContents = [];
      const attributeValues = [];

      relatedEntities.forEach((entity) => {
        // 收集实体内容
        if (!entityContents.includes(entity.entityContent)) {
          entityContents.push(entity.entityContent);
        }

        // 收集属性值
        if (!attributeValues.includes(entity.attributeValue)) {
          attributeValues.push(entity.attributeValue);
        }

        // 收集唯一的段落
        const paragraphId =
          entity.paragraphId || `para_${entity.start}_${entity.end}`;
        if (!uniqueParagraphs.has(paragraphId)) {
          uniqueParagraphs.set(paragraphId, {
            content: entity.paragraphContent,
            entities: []
          });
        }
        uniqueParagraphs.get(paragraphId).entities.push({
          content: entity.entityContent,
          start: entity.start,
          end: entity.end
        });
      });

      return {
        entityContents: entityContents,
        attributeName: this.currentAttrName,
        attributeValues: attributeValues,
        entityName: this.annoContents.join(" / "),
        entityLabelName: this.annoLabelName,
        paragraphs: Array.from(uniqueParagraphs.values()),
        relatedEntitiesCount: relatedEntities.length
      };
    },

    // 获取当前属性的可用规则
    getAvailableRules() {
      return new Promise((resolve, reject) => {
        if (!this.currentAttrName) {
          reject(new Error("未找到当前属性"));
          return;
        }

        this.$http({
          url: this.$http.adornUrl("/label/entity/getLabelRulesList"),
          method: "get",
          params: this.$http.adornParams({
            projectId: this.annotation.projectId,
            labelName: this.currentAttrName
          })
        })
          .then(({ data }) => {
            if (data && data.code === 0) {
              this.availableRules = data.data || [];
              resolve();
            } else {
              reject(new Error(data.msg || "获取规则失败"));
            }
          })
          .catch(() => {
            reject(new Error("获取规则失败"));
          });
      });
    },

    // 生成预览示例
    generateExamplePreview() {
      if (!this.currentSelectedAttribute) {
        return;
      }

      const articleId = this.annotation.articleId;
      const paragraphs = this.currentSelectedAttribute.paragraphs;

      let exampleContent = `（${articleId}）`;

      // 处理多个段落的情况
      paragraphs.forEach((paragraph, index) => {
        let highlightedContext = paragraph.content;

        // 在段落中高亮所有相关实体
        paragraph.entities.forEach((entity) => {
          const escapedContent = entity.content.replace(
            /[.*+?^${}()|[\]\\]/g,
            "\\$&"
          );
          highlightedContext = highlightedContext.replace(
            new RegExp(escapedContent, "g"),
            `==${entity.content}==`
          );
        });

        exampleContent += highlightedContext;
        if (index < paragraphs.length - 1) {
          exampleContent += "\n\n";
        }
      });

      this.examplePreview = exampleContent;
    },

    // 保存示例
    doSaveExample() {
      // 验证必填项
      if (!this.selectedRuleForExample) {
        this.$message({
          message: "请选择规则！",
          type: "warning"
        });
        return;
      }

      if (!this.examplePreview.trim()) {
        this.$message({
          message: "示例内容不能为空！",
          type: "warning"
        });
        return;
      }

      this.$confirm(`确定要添加当前示例到规则中吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        this.submitExample();
      });
    },

    // 提交示例
    submitExample() {
      const loading = this.$loading({
        lock: true
      });

      // 准备提交数据，处理多实体情况
      const exampleData = {
        projectId: this.annotation.projectId,
        ruleId: this.selectedRuleForExample,
        exampleContent: this.examplePreview,
        exampleType: this.exampleType,
        labelName: this.currentAttrName
      };

      // 如果有相关实体信息，添加实体ID（使用第一个实体作为主要实体）
      if (
        this.currentSelectedAttribute &&
        this.currentSelectedAttribute.relatedEntitiesCount > 0
      ) {
        // 从属性值中提取第一个实体ID
        const firstAttributeValue =
          this.currentSelectedAttribute.attributeValues[0];
        if (
          typeof firstAttributeValue === "string" &&
          firstAttributeValue.includes("_##_")
        ) {
          exampleData.entityId = firstAttributeValue.split("_##_")[1];
        } else {
          // 如果没有实体ID引用，使用当前选中的实体ID（如果有的话）
          exampleData.entityId = this.annoid || null;
        }
      } else {
        exampleData.entityId = this.annoid || null;
      }

      this.$http({
        url: this.$http.adornUrl("/label/entity/addRuleExample"),
        method: "post",
        data: this.$http.adornData(exampleData)
      })
        .then(({ data }) => {
          loading.close();
          if (data && data.code === 0) {
            this.$message({
              message: "示例添加成功！",
              type: "success"
            });
            this.addToExampleDialogVisible = false;
            // 刷新当前属性的示例显示
            this.loadAttrExample(this.currentAttrName);
          } else {
            this.$message({
              message: data.msg || "示例添加失败！",
              type: "error"
            });
          }
        })
        .catch(() => {
          loading.close();
          this.$message({
            message: "示例添加失败！",
            type: "error"
          });
        });
    },

    // 关闭添加到示例弹窗
    closeAddToExampleDialog() {
      this.addToExampleDialogVisible = false;
      this.onAddToExampleDialogClose();
    },

    // 添加到示例弹窗关闭事件处理
    onAddToExampleDialogClose() {
      // 清理数据
      this.examplePreview = "";
      this.selectedRuleForExample = "";
      this.exampleType = "positive";
      this.availableRules = [];
      this.currentSelectedAttribute = null;
    }
  },
  destroyed() {
    window.onresize = null;
    AnnoEventsBus.$off("clearAttrData");
    AnnoEventsBus.$off("doRefreshAttrByLabel");
    AnnoEventsBus.$off("doAddAttrEv");
    this.currentAttrExample = "";
    this.currentAttrName = "";
  }
};
</script>

<style scoped lang="scss">
::v-deep .el-alert__content .el-alert__title {
  font-size: 14px !important;
}

.el-select {
  ::v-deep.el-select__tags-text {
    max-width: 120px !important;
  }
}

.attributes {
  border: 1px solid #ebeef5;
  border-radius: 5px;
  padding: 0;
  background-color: #fafafa;

  .att-list {
    padding: 6px;
    width: 15%;
    display: inline-block;
    font-size: 13px;
  }
}

.att {
  color: #606266;
  font-weight: 600;
  padding-left: 10px;
  border-left: 3px solid #409eff;
  font-size: 13px;
  margin: 16px 0 16px 0;
}

.el-descriptions {
  ::v-deep.el-descriptions-item__label {
    text-align: center;
    width: 80px;
  }
}

::v-deep .my-entity-content {
  max-width: 300px;
}

// 属性容器滚动样式
.attribute-container {
  ::v-deep .el-card__body {
    height: 100%;
    overflow-y: auto;
    padding: 20px;
  }
}

// 属性规则示例样式
::v-deep .v-note-wrapper {
  border: none !important;
  box-shadow: none !important;
}

::v-deep .v-note-wrapper .v-note-panel {
  border: none !important;
}

::v-deep .v-note-wrapper .v-note-panel .v-note-show {
  background-color: #ffffff !important;
  padding: 15px;
  font-size: 13px;
  line-height: 1.6;
  min-height: 200px;
}

::v-deep .v-note-wrapper .v-note-op {
  border-bottom: 1px solid #e8e8e8;
  background-color: #f8f8f8;
}

::v-deep .v-note-wrapper .v-note-op .v-left-item,
::v-deep .v-note-wrapper .v-note-op .v-right-item {
  flex: none;
}

::v-deep .v-note-wrapper .v-note-op .v-left-item .op-icon,
::v-deep .v-note-wrapper .v-note-op .v-right-item .op-icon {
  display: none;
}

::v-deep .v-note-wrapper .v-note-op .v-right-item .op-icon.fa-arrows-alt {
  display: inline-block;
}
</style>
