<template>
  <div class="mod-config">
    <el-form
      :inline="true"
      :model="dataForm"
      @keyup.enter.native="getDataList()"
    >
      <el-form-item label="文书内部编号">
        <el-input
          style="width: 150px"
          v-model="dataForm.articleId"
          placeholder="文书内部编号"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="标题">
        <el-input
          style="width: 150px"
          v-model="dataForm.articleName"
          placeholder="标题"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item
        v-if="
          roleId === this.$RoleEnum.projectAdmin ||
          roleId === this.$RoleEnum.auditor
        "
        label="标注员"
      >
        <el-select style="width: 100px" v-model="dataForm.annotator" clearable>
          <div v-for="(value, key) in annotatorList" :key="'his-aud1' + key">
            <el-option :label="value" :value="key"></el-option>
          </div>
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="roleId === this.$RoleEnum.projectAdmin"
        label="审核员"
      >
        <el-select style="width: 100px" v-model="dataForm.auditor" clearable>
          <div v-for="(value, key) in auditorList" :key="'his-aud2' + key">
            <el-option :label="value" :value="key"></el-option>
          </div>
        </el-select>
      </el-form-item>
      <el-form-item label="状态">
        <el-select
          class="status-width"
          v-model="dataForm.status"
          placeholder="状态"
          clearable
        >
          <el-option
            v-if="roleId === this.$RoleEnum.projectAdmin"
            label="未标注"
            :value="this.$NoteEnum.unmarked"
          >
          </el-option>
          <el-option
            v-if="
              roleId === this.$RoleEnum.annotator ||
              roleId === this.$RoleEnum.projectAdmin
            "
            label="标注中"
            :value="this.$NoteEnum.noting"
          >
          </el-option>
          <el-option
            v-if="
              roleId === this.$RoleEnum.annotator ||
              roleId === this.$RoleEnum.projectAdmin
            "
            label="已标注"
            :value="this.$NoteEnum.marked"
          >
          </el-option>
          <el-option
            v-if="
              roleId === this.$RoleEnum.auditor ||
              roleId === this.$RoleEnum.projectAdmin
            "
            label="审核中"
            :value="this.$NoteEnum.reviewing"
          >
          </el-option>
          <el-option label="已审核" :value="this.$NoteEnum.reviewed">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          icon="el-icon-search"
          @click="
            () => {
              this.pageIndex = 1;
              this.getDataList();
            }
          "
          >查询</el-button
        >
      </el-form-item>
      <el-button
        v-if="roleId === this.$RoleEnum.projectAdmin"
        style="float: right"
        type="danger"
        @click="annotationAll()"
        plain
        size="small"
        >一键全部标注完成
      </el-button>
    </el-form>

    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      style="width: 100%"
    >
      <el-table-column
        width="130"
        prop="articleId"
        header-align="center"
        align="center"
        show-overflow-tooltip
        label="文书内部编号"
      >
      </el-table-column>
      <el-table-column
        prop="articleName"
        header-align="center"
        align="center"
        show-overflow-tooltip
        label="标题"
      >
      </el-table-column>
      <el-table-column
        width="100"
        v-if="
          roleId === this.$RoleEnum.projectAdmin ||
          roleId === this.$RoleEnum.auditor
        "
        prop="annotator"
        header-align="center"
        align="center"
        label="标注员"
      >
        <template slot-scope="scope">
          <div>{{ annotatorMap.get(scope.row.annotator + "") }}</div>
        </template>
      </el-table-column>
      <el-table-column
        width="100"
        v-if="roleId === this.$RoleEnum.projectAdmin"
        prop="auditor"
        header-align="center"
        align="center"
        label="审核员"
      >
        <template slot-scope="scope">
          <div>{{ auditorMap.get(scope.row.auditor + "") }}</div>
        </template>
      </el-table-column>
      <el-table-column
        width="160"
        v-if="roleId === this.$RoleEnum.annotator"
        prop="annoStartTime"
        header-align="center"
        align="center"
        label="标注开始时间"
      >
      </el-table-column>
      <el-table-column
        width="160"
        prop="annoEndTime"
        v-if="
          roleId === this.$RoleEnum.annotator ||
          roleId === this.$RoleEnum.projectAdmin
        "
        header-align="center"
        align="center"
        label="标注完成时间"
      >
      </el-table-column>
      <el-table-column
        width="160"
        v-if="roleId === this.$RoleEnum.auditor"
        prop="auditStartTime"
        header-align="center"
        align="center"
        label="审核开始时间"
      >
      </el-table-column>
      <el-table-column
        width="160"
        v-if="
          roleId === this.$RoleEnum.auditor ||
          roleId === this.$RoleEnum.projectAdmin
        "
        prop="auditEndTime"
        header-align="center"
        align="center"
        label="审核完成时间"
      >
      </el-table-column>
      <el-table-column
        width="80"
        prop="status"
        header-align="center"
        align="center"
        label="状态"
      >
        <template slot-scope="scope">
          <el-tag
            v-if="scope.row.status === $NoteEnum.unmarked"
            size="small"
            type="info"
            >未标注</el-tag
          >
          <el-tag v-if="scope.row.status === $NoteEnum.noting" size="small"
            >标注中</el-tag
          >
          <el-tag
            v-if="scope.row.status === $NoteEnum.marked"
            size="small"
            type="success"
            >已标注</el-tag
          >
          <el-tag
            v-if="scope.row.status === $NoteEnum.reviewing"
            size="small"
            type="warning"
            >审核中</el-tag
          >
          <el-tag
            v-if="scope.row.status === $NoteEnum.reviewed"
            size="small"
            type="danger"
            >已审核</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column
        header-align="center"
        align="center"
        width="100"
        label="操作"
      >
        <template slot-scope="scope">
          <el-tooltip
            v-if="
              roleId === $RoleEnum.projectAdmin ||
              roleId === $RoleEnum.projectWatcher
            "
            content="查看"
            placement="top"
            effect="light"
          >
            <i class="pointer" @click="infoForProject(scope.row)">
              <icon-svg
                :name="'see'"
                class="icon-inner icon-primary"
              ></icon-svg>
            </i>
          </el-tooltip>
          <el-tooltip v-else content="查看" placement="top" effect="light">
            <i class="pointer" @click="info(scope.row.noteId)">
              <icon-svg
                :name="'see'"
                class="icon-inner icon-primary"
              ></icon-svg>
            </i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    >
    </el-pagination>
  </div>
</template>

<script>
import moment from "moment";

export default {
  data() {
    return {
      roleId: "",
      userId: "",
      annotatorList: [],
      auditorList: [],
      dataForm: {
        batchId: "",
        articleId: "",
        articleName: "",
        createTime: "",
        lastUpdateTime: "",
        status: "",
        invalid: "",
        lockUser: "",
        annotator: "",
        annoStartTime: "",
        annoEndTime: "",
        auditor: "",
        auditStartTime: "",
        auditEndTime: ""
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      projectEnv: {}
    };
  },
  props: ["batchId"],
  components: {},
  created() {
    this.roleId = this.$store.state.user.roleId;
    if (this.$store.state.user.roleId === this.$RoleEnum.projectWatcher) {
      this.roleId = this.$RoleEnum.projectAdmin;
    }
    this.userId = this.$store.state.user.id;
  },
  computed: {
    annotatorMap: function () {
      const tempMap = new Map();
      for (const k of Object.keys(this.annotatorList)) {
        tempMap.set(k, this.annotatorList[k]);
      }
      return tempMap;
    },
    auditorMap: function () {
      const tempMap = new Map();
      for (const k of Object.keys(this.auditorList)) {
        tempMap.set(k, this.auditorList[k]);
      }
      return tempMap;
    }
  },
  mounted() {
    // console.log(this.$NoteEnum.unmarked)
    // console.log(this.$NoteEnum.noting)
    // console.log(this.$NoteEnum.marked)
    // console.log(this.$NoteEnum.reviewing)
    // console.log(this.$NoteEnum.reviewed)
    // 标注员
    // console.log('roleID = ', this.roleId)
    this.pageIndex = 1;
    this.getDataList();
  },
  filters: {
    dateFrm: function (value) {
      return moment(value).format("YYYY-MM-DD hh:mm:ss");
    }
  },
  methods: {
    annotationAll() {
      this.$confirm("确定把所有文书状态全部设置为已标注?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        this.dataListLoading = true;
        this.$http({
          url: this.$http.adornUrl(`/batch/annotationAll/${this.batchId}`),
          method: "get"
        })
          .then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: "操作成功",
                type: "success",
                duration: 3000
              });
            }
            this.dataListLoading = false;
            this.getDataList();
          })
          .catch(() => {
            this.dataListLoading = false;
            this.$message({
              message: "操作失败",
              type: "error",
              duration: 3000
            });
            this.getDataList();
          });
      });
    },
    getDataList() {
      // console.log('batchId = ', this.batchId)
      // console.log('userId = ', this.userId)
      if (this.roleId === this.$RoleEnum.annotator) {
        this.getAnnotatorDataList();
      } else if (this.roleId === this.$RoleEnum.auditor) {
        this.getAuditorDataList();
      } else if (this.roleId === this.$RoleEnum.projectAdmin) {
        this.getProjectorDataList();
      } else if (this.roleId === this.$RoleEnum.projectWatcher) {
        this.getProjectorDataList();
      }
    },
    // 标注员
    getAnnotatorDataList() {
      // console.log('dataForm', this.dataForm)
      this.dataListLoading = true;
      this.$http({
        url: this.$http.adornUrl("/history/annotatorDataList"),
        method: "get",
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          batchId: this.batchId,
          articleId: this.dataForm.articleId,
          articleName: this.dataForm.articleName,
          annotator: this.userId,
          status: this.dataForm.status
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list;
          this.totalPage = data.page.totalCount;
        } else {
          this.dataList = [];
          this.totalPage = 0;
        }
        this.dataListLoading = false;
      });
    },
    // 审核员
    getAuditorDataList() {
      // console.log('dataForm', this.dataForm)
      this.dataListLoading = true;
      this.$http({
        url: this.$http.adornUrl("/history/auditorDataList"),
        method: "get",
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          batchId: this.batchId,
          articleId: this.dataForm.articleId,
          articleName: this.dataForm.articleName,
          annotator: this.dataForm.annotator,
          auditor: this.userId,
          status: this.dataForm.status
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list;
          this.totalPage = data.page.totalCount;
          if (data.annotatorList) {
            this.annotatorList = data.annotatorList;
          } else {
            this.annotatorList = [];
          }
        } else {
          this.dataList = [];
          this.totalPage = 0;
        }
        this.dataListLoading = false;
      });
    },
    // 项目管理员
    getProjectorDataList() {
      // console.log('dataForm', this.dataForm)
      this.dataListLoading = true;
      this.$http({
        url: this.$http.adornUrl("/history/getProjectDataList"),
        method: "get",
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          batchId: this.batchId,
          articleId: this.dataForm.articleId,
          articleName: this.dataForm.articleName,
          annotator: this.dataForm.annotator,
          auditor: this.dataForm.auditor,
          status: this.dataForm.status
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          // console.log(data)
          this.dataList = data.page.list;
          this.totalPage = data.page.totalCount;
          if (data.annotatorList) {
            this.annotatorList = data.annotatorList;
          } else {
            this.annotatorList = [];
          }
          if (data.auditorList) {
            this.auditorList = data.auditorList;
          } else {
            this.auditorList = [];
          }
        } else {
          this.dataList = [];
          this.totalPage = 0;
        }
        this.dataListLoading = false;
      });
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val;
      this.pageIndex = 1;
      this.getDataList();
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val;
      this.getDataList();
    },
    info(id) {
      this.$nextTick(() => {
        this.$router.push({
          name: "annotation-info",
          params: { noteId: id }
        });
      });
    },
    infoForProject(note) {
      const projectEnv = {
        projectId: note.projectId,
        batchId: note.batchId
      };
      this.$store.commit("anno/setBatch", projectEnv);
      this.$router.push({
        name: "article-info",
        params: { noteId: note.noteId }
      });
    }
  }
};
</script>
