<template>
  <div id="drag" :style="{ left: styles.left + 'px', top: styles.top + 'px' }">
    <div class="title">
      <slot name="header">
        <a class="min" href="javascript:;" title="最小化"></a>
        <a class="max" href="javascript:;" title="最大化"></a>
        <a class="revert" href="javascript:;" title="还原"></a>
        <a class="close" href="javascript:;" title="关闭"></a>
      </slot>
    </div>
    <div class="resizeL"></div>
    <div class="resizeT"></div>
    <div class="resizeR"></div>
    <div class="resizeB"></div>
    <div class="resizeLT"></div>
    <div class="resizeTR"></div>
    <div class="resizeBR"></div>
    <div class="resizeLB"></div>
    <div class="content">
      <slot> </slot>
    </div>
  </div>
</template>

<script lang="js">
export default {
  name: "dragPopover",
  props: {
    dragMinWidth: {
      type: Number,
      default: 200
    },
    dragMinHeight: {
      type: Number,
      default: 80
    },
    styles: {
      type: Object,
      default () {
        return {};
      }
    }
  },
  data () {
    return {
      get: {}
      // dragMinWidth: 250,
      // dragMinHeight: 124
    };
  },
  components: {
  },
  created () {

  },
  mounted () {
    this.get = {
      byId: (id) => {
        return typeof id === "string" ? document.getElementById(id) : id;
      },
      byClass: (sClass, oParent) => {
        const aClass = [];
        const reClass = new RegExp("(^| )" + sClass + "( |$)");
        const aElem = this.get.byTagName("*", oParent);
        for (let i = 0; i < aElem.length; i++) reClass.test(aElem[i].className) && aClass.push(aElem[i]);
        return aClass;
      },
      byTagName: (elem, obj) => {
        return (obj || document).getElementsByTagName(elem);
      }
    };
    this.pageResize();
    window.onresize = () => {
      this.pageResize();
    };
  },
  methods: {
    pageResize () {
      const oDrag = document.getElementById("drag");
      const oTitle = this.get.byClass("title", oDrag)[0];
      const oL = this.get.byClass("resizeL", oDrag)[0];
      const oT = this.get.byClass("resizeT", oDrag)[0];
      const oR = this.get.byClass("resizeR", oDrag)[0];
      const oB = this.get.byClass("resizeB", oDrag)[0];
      const oLT = this.get.byClass("resizeLT", oDrag)[0];
      const oTR = this.get.byClass("resizeTR", oDrag)[0];
      const oBR = this.get.byClass("resizeBR", oDrag)[0];
      const oLB = this.get.byClass("resizeLB", oDrag)[0];
      this.drag(oDrag, oTitle);
      // 四角
      this.resize(oDrag, oLT, true, true, false, false);
      this.resize(oDrag, oTR, false, true, false, false);
      this.resize(oDrag, oBR, false, false, false, false);
      this.resize(oDrag, oLB, true, false, false, false);
      // 四边
      this.resize(oDrag, oL, true, false, false, true);
      this.resize(oDrag, oT, false, true, true, false);
      this.resize(oDrag, oR, false, false, false, true);
      this.resize(oDrag, oB, false, false, true, false);
      oDrag.style.left = (document.documentElement.clientWidth - oDrag.offsetWidth) / 2 + "px";
      oDrag.style.top = (document.documentElement.clientHeight - oDrag.offsetHeight) / 2 + "px";
    },
    resize (oParent, handle, isLeft, isTop, lockX, lockY) {
      handle.onmousedown = (e) => {
        const event = e || window.event;
        const disX = event.clientX - handle.offsetLeft;
        const disY = event.clientY - handle.offsetTop;
        const iParentTop = oParent.offsetTop;
        const iParentLeft = oParent.offsetLeft;
        const iParentWidth = oParent.offsetWidth;
        const iParentHeight = oParent.offsetHeight;
        document.onmousemove = (e) => {
          const event = e || window.event;
          const iL = event.clientX - disX;
          const iT = event.clientY - disY;
          const maxW = document.documentElement.clientWidth - oParent.offsetLeft - 2;
          const maxH = document.documentElement.clientHeight - oParent.offsetTop - 2; let iW = isLeft ? iParentWidth - iL : handle.offsetWidth + iL;
          let iH = isTop ? iParentHeight - iT : handle.offsetHeight + iT;
          isLeft && (oParent.style.left = iParentLeft + iL + "px");
          isTop && (oParent.style.top = iParentTop + iT + "px");
          iW < this.dragMinWidth && (iW = this.dragMinWidth);
          iW > maxW && (iW = maxW);
          lockX || (oParent.style.width = iW + "px");
          iH < this.dragMinHeight && (iH = this.dragMinHeight);
          iH > maxH && (iH = maxH);
          lockY || (oParent.style.height = iH + "px");
          if ((isLeft && iW === this.dragMinWidth) || (isTop && iH === this.dragMinHeight)) document.onmousemove = null;
          return false;
        };
        document.onmouseup = function () {
          document.onmousemove = null;
          document.onmouseup = null;
        };
        return false;
      };
    },
    drag (oDrag, handle) {
      let disX = 0;
      let disY = 0;
      // var oMin = this.get.byClass('min', oDrag)[0];
      // var oMax = this.get.byClass('max', oDrag)[0];
      // var oRevert = this.get.byClass('revert', oDrag)[0];
      // var oClose = this.get.byClass('close', oDrag)[0];
      handle = handle || oDrag;
      handle.style.cursor = "move";
      handle.onmousedown = (e) => {
        const event = e || window.event;
        disX = event.clientX - oDrag.offsetLeft;
        disY = event.clientY - oDrag.offsetTop;
        document.onmousemove = (e) => {
          const event = e || window.event;
          let iL = event.clientX - disX;
          let iT = event.clientY - disY;
          const maxL = document.documentElement.clientWidth - oDrag.offsetWidth;
          const maxT = document.documentElement.clientHeight - oDrag.offsetHeight;
          iL <= 0 && (iL = 0);
          iT <= 0 && (iT = 0);
          iL >= maxL && (iL = maxL);
          iT >= maxT && (iT = maxT);
          oDrag.style.left = iL + "px";
          oDrag.style.top = iT + "px";
          return false;
        };
        document.onmouseup = function () {
          document.onmousemove = null;
          document.onmouseup = null;
          this.releaseCapture && this.releaseCapture();
        };
        this.setCapture && this.setCapture();
        return false;
      };
      // // 最大化按钮
      //     oMax.onclick = function () {
      //       oDrag.style.top = oDrag.style.left = 0;
      //       oDrag.style.width = document.documentElement.clientWidth - 2 + 'px';
      //       oDrag.style.height = document.documentElement.clientHeight - 2 + 'px';
      //       this.style.display = 'none';
      //       oRevert.style.display = 'block';
      //     };
      // // 还原按钮
      //     oRevert.onclick = function () {
      //       oDrag.style.width = this.dragMinWidth + 'px';
      //       oDrag.style.height = this.dragMinHeight + 'px';
      //       oDrag.style.left = (document.documentElement.clientWidth - oDrag.offsetWidth) / 2 + 'px';
      //       oDrag.style.top = (document.documentElement.clientHeight - oDrag.offsetHeight) / 2 + 'px';
      //       this.style.display = 'none';
      //       oMax.style.display = 'block';
      //     };
      // // 最小化按钮
      //     oMin.onclick = oClose.onclick = function () {
      //       oDrag.style.display = 'none';
      //       var oA = document.createElement('a');
      //       oA.className = 'open';
      //       oA.href = 'javascript:;';
      //       oA.title = '还原';
      //       document.body.appendChild(oA);
      //       oA.onclick = function () {
      //         oDrag.style.display = 'block';
      //         document.body.removeChild(this);
      //         this.onclick = null;
      //       };
      //     };
      // 阻止冒泡
      // oMin.onmousedown = oMax.onmousedown = oClose.onmousedown = function (event) {
      //   this.onfocus = function () { this.blur() };
      //   (event || window.event).cancelBubble = true
      // };
    }

  },
  destroyed () {
    window.onresize = null;
  }
};
</script>

<style scoped lang="scss">
#drag {
  position: fixed;
  width: 295px;
  height: 163px;
  background: #fff;
  border-radius: 6px;
  box-shadow: rgba(0, 0, 0, 0.8) 0 4px 23px -6px;
  z-index: 99999;
}
#drag .title {
  // margin: 5px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  position: relative;
  height: 30px;
  padding: 0 10px;
  font-size: 14px;
  background-color: #e9e9e9;
}
#drag .title h2 {
  font-size: 14px;
  height: 27px;
  line-height: 24px;
  border-bottom: 1px solid #a1b4b0;
}
#drag .title div {
  position: absolute;
  height: 19px;
  top: 2px;
  right: 0;
}
#drag .title a,
a.open {
  float: left;
  width: 21px;
  height: 19px;
  display: block;
  margin-left: 5px;
  // background: url(images/tool.png) no-repeat;
}
a.open {
  position: absolute;
  top: 10px;
  left: 50%;
  margin-left: -10px;
  background-position: 0 0;
}
a.open:hover {
  background-position: 0 -29px;
}
#drag .title a.min {
  background-position: -29px 0;
}
#drag .title a.min:hover {
  background-position: -29px -29px;
}
#drag .title a.max {
  background-position: -60px 0;
}
#drag .title a.max:hover {
  background-position: -60px -29px;
}
#drag .title a.revert {
  background-position: -149px 0;
  display: none;
}
#drag .title a.revert:hover {
  background-position: -149px -29px;
}
#drag .title a.close {
  background-position: -89px 0;
}
#drag .title a.close:hover {
  background-position: -89px -29px;
}
#drag .content {
  height: calc(100% - 30px);
  overflow: auto;
  margin: 0 5px;
  padding: 0 10px;
}
#drag .resizeBR {
  position: absolute;
  width: 14px;
  height: 14px;
  right: 0;
  bottom: 0;
  overflow: hidden;
  cursor: nw-resize;
  // background: url(images/resize.png) no-repeat;
}
#drag .resizeL,
#drag .resizeT,
#drag .resizeR,
#drag .resizeB,
#drag .resizeLT,
#drag .resizeTR,
#drag .resizeLB {
  position: absolute;
  background: #000;
  overflow: hidden;
  opacity: 0;
  filter: alpha(opacity=0);
}
#drag .resizeL,
#drag .resizeR {
  top: 0;
  width: 5px;
  height: 100%;
  cursor: w-resize;
}
#drag .resizeR {
  right: 0;
}
#drag .resizeT,
#drag .resizeB {
  width: 100%;
  height: 5px;
  cursor: n-resize;
}
#drag .resizeT {
  top: 0;
}
#drag .resizeB {
  bottom: 0;
}
#drag .resizeLT,
#drag .resizeTR,
#drag .resizeLB {
  width: 8px;
  height: 8px;
  background: #ff0;
}
#drag .resizeLT {
  top: 0;
  left: 0;
  cursor: nw-resize;
}
#drag .resizeTR {
  top: 0;
  right: 0;
  cursor: ne-resize;
}
#drag .resizeLB {
  left: 0;
  bottom: 0;
  cursor: ne-resize;
}
</style>
