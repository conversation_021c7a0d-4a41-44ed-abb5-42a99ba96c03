<template>
  <el-card shadow="never" :style="`height: ${fullHeight}px`">
    <div class="entity-list">
      <div class="filter">
        <el-input
          size="small"
          placeholder="输入关键字进行过滤"
          v-model="filterText"
          clearable
          style="margin-right: 15px"
        >
        </el-input>
        <el-button
          style="height: 32px"
          plain
          size="small"
          :icon="expandAll ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"
          @click="expandHandle"
          >{{ expandAll ? "收起" : "展开" }}
        </el-button>
      </div>
      <div class="tree" :style="`height: ${fullHeight - 60}px`">
        <el-tree
          class="filter-tree"
          :data="data"
          :props="defaultProps"
          :node-key="`id`"
          :default-expand-all="expandAll"
          :filter-node-method="filterNode"
          :default-expanded-keys="defaultExpandedKeys"
          @current-change="currentChange"
          ref="entityListTree"
        >
          <div slot-scope="{ node, data }" :tree_annoid="data.id">
            <span
              v-if="node.level === 1"
              style="
                display: inline-block;
                width: 12px;
                height: 12px;
                margin-right: 2px;
                border-radius: 4px;
              "
              :class="`tag-${data.labelId}`"
            ></span>
            <span v-else></span>
            <span>
              <span
                class="tree-label"
                :style="`color: ${node.level === 1 ? '#333333' : '#666666'}`"
              >
                {{ data.label }}:
              </span>
              <span
                :title="data.val | fullContent"
                style="color: #999999; margin-left: 5px"
                >{{ data.val | partContent }}</span
              >
            </span>
          </div>
        </el-tree>
      </div>
    </div>
  </el-card>
</template>

<script>
import { joinArray, sleep, trimStr } from "@/utils";
import { AnnoEventsBus } from "@/utils/bus";

export default {
  name: "entityList",
  data() {
    return {
      filterText: "",
      // fullHeight: 600,
      articleHeight: 150,
      fullHeight: document.documentElement.clientHeight - this.articleHeight,
      data: [],
      defaultProps: {
        children: "children",
        label: "label"
      },
      expandAll: false,
      defaultExpandedKeys: []
    };
  },
  computed: {
    taskId() {
      return this.$store.state.anno.annotation.taskId;
    },
    source() {
      return this.$store.state.anno.annotation.source;
    }
  },
  filters: {
    partContent: function (val) {
      return joinArray(val, " / ", 30);
    },
    fullContent: function (val) {
      return joinArray(val, " / ");
    }
  },
  mounted() {
    AnnoEventsBus.$on("doChangeEntityTree", (currAnnoid) => {
      this.selectedNodeHandle(currAnnoid);
    });
    this.fullHeight = document.documentElement.clientHeight - 160;
    window.onresize = () => {
      return (() => {
        this.fullHeight = document.documentElement.clientHeight - 160;
      })();
    };
    this.initEntityTree();
  },
  methods: {
    initEntityTree() {
      // console.log("initEntityTree");
      this.data = [];
      const params = {
        source: this.source,
        taskId: this.taskId
      };
      this.$http({
        url: this.$http.adornUrl(`/note/getEntityListTree`),
        method: "get",
        params: this.$http.adornParams(params)
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.data = data.data;
        }
      });
    },
    filterNode(value, data) {
      value = trimStr(value).toLowerCase();
      if (!value) {
        return true;
      }
      if (data.label && data.label.toLowerCase().includes(value)) {
        return true;
      }
      if (data.val && data.val.length > 0) {
        const l = data.val.length;
        let hasVal = false;
        for (let i = 0; i < l; i++) {
          if (data.val[i].toLowerCase().includes(value)) {
            hasVal = true;
            break;
          }
        }
        return hasVal;
      }
      return false;
    },
    currentChange(data, node) {
      const annoid = data.id;
      if (annoid) {
        const target = document.querySelector(`span[annoid="${annoid}"]`);
        if (target) {
          target.scrollIntoView();
          const changeData = {
            annoid: trimStr(target.getAttribute("annoid")),
            uniqueid: trimStr(target.getAttribute("uniqueid")),
            labelId: trimStr(target.getAttribute("labelid")),
            content: trimStr(target.innerText),
            annotate: trimStr(target.getAttribute("annotate")),
            is_attr: trimStr(target.getAttribute("is_attr")),
            attr_ids: target.getAttribute("attr_ids"),
            isPre: target.classList.contains("black"),
            showTip: true
          };
          AnnoEventsBus.$emit("doChangeEntity", changeData);
        }
      }
    },
    expandHandle() {
      // console.log(this.$refs.entityListTree.store);
      this.expandAll = !this.expandAll;
      this.expandNodes(this.$refs.entityListTree.store.root);
    },
    // 遍历树形数据，设置每一项的expanded属性，实现展开收起
    expandNodes(node) {
      node.expanded = this.expandAll;
      for (let i = 0; i < node.childNodes.length; i++) {
        node.childNodes[i].expanded = this.expandAll;
        if (node.childNodes[i].childNodes.length > 0) {
          this.expandNodes(node.childNodes[i]);
        }
      }
    },
    selectedNodeHandle(selectedNodeId) {
      if (!selectedNodeId) {
        return;
      }
      // const expandedKeys = new Set(this.defaultExpandedKeys);
      // expandedKeys.add(selectedNodeId);
      this.defaultExpandedKeys = [selectedNodeId];
      this.$nextTick(() => {
        sleep(210).then(() => {
          const treeObj = this.$refs.entityListTree;
          if (treeObj) {
            treeObj.setCheckedKeys([selectedNodeId]);
            treeObj.setCurrentKey(selectedNodeId);
          }
          let note = document.querySelector(
            `.entity-list div[tree_annoid="${selectedNodeId}"]`
          );
          if (note && note.parentNode) {
            //添加树节点选中样式
            note.parentNode.parentNode.focus();
          }
        });
      });
    },
    renderContent(h, { node, data }) {
      const labelArr = data.label.split(":");
      const labelText = labelArr[0];
      const labelDesc = labelArr[1];
      return (
        <div>
          {node.level === 1 ? (
            <span style="display: inline-block; width: 12px; height: 12px; background-color: red; margin-right: 5px; border-radius: 4px;"></span>
          ) : (
            <span></span>
          )}
          <span>
            {
              <span
                style={{
                  color: node.level === 1 ? "#333333" : "#666666"
                }}
              >
                {labelText}:
              </span>
            }
            {<span style="color: #999999; margin-left: 5px;">{labelDesc}</span>}
          </span>
        </div>
      );
    }
  },
  watch: {
    filterText(val) {
      this.$refs.entityListTree.filter(val);
    }
  },
  destroyed() {
    AnnoEventsBus.$off("doChangeEntityTree");
  }
};
</script>

<style scoped lang="scss">
.entity-list {
  display: flex;
  flex-direction: column;

  .filter {
    height: 30px;
    display: flex;
    margin-bottom: 10px;
    justify-content: space-between;
  }

  .tree {
    height: calc(100vh - 190px);
    overflow-y: auto;
  }
  .el-input {
    margin-bottom: 5px;
  }

  //选中样式
  ::v-deep .el-tree-node:focus > .el-tree-node__content {
    background-color: #f0f7ff !important;
    border-radius: 4px;
  }

  .color-box {
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-right: 5px;
    background-color: #daabef;
    border-radius: 4px;
  }

  .level-one {
    font-weight: 600;
  }
}
</style>
