<template>
  <main class="main">
    <div v-if="visible === ''">
      <CommAnnotation
        ref="commAnno"
        :noteId="noteId"
        @nextArticle="getNoteId"
      ></CommAnnotation>
    </div>
    <h2 v-else class="article-tip">{{ visible }}</h2>
  </main>
</template>

<script>
import CommAnnotation from "./comm-annotation";

export default {
  data() {
    return {
      noteId: 0,
      visible: ""
    };
  },
  created() {
    this.projectEnv = JSON.parse(sessionStorage.getItem("workEnv"));
    if (this.projectEnv === null) {
      this.visible = "请先选择项目和批次";
      return;
    }
    this.$store.commit("anno/setBatch", this.projectEnv);
    if (this.$route.params.noteId == null) {
      this.getNoteId();
    }
  },
  mounted() {
    if (
      this.$route.params.noteId &&
      this.$route.params.noteId !== this.noteId
    ) {
      this.visible = "加载中...";
      this.noteId = this.$route.params.noteId;
      this.visible = "";
      this.$nextTick(() => {
        this.$refs.commAnno.getArticle();
      });
    }
  },
  methods: {
    /**
     * 获取系统分配的NoteId
     */
    getNoteId() {
      this.visible = "加载中...";
      this.$http({
        url: this.$http.adornUrl("/note/assignArticle"),
        method: "get",
        params: this.$http.adornParams({
          batchId: this.projectEnv.batchId,
          roleId: this.roleId
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.noteId = data.data;
          if (!this.noteId || this.noteId === 0) {
            this.visible =
              "暂无待" + (this.roleId === 3 ? "审核" : "标注") + "文献";
            document
              .querySelectorAll(".el-loading-mask")
              .forEach((node) => node.parentNode.removeChild(node));
            return;
          }
          this.visible = "";
          this.$nextTick(() => {
            this.$refs.commAnno.getArticle();
          });
        } else {
          this.$message({
            message: data.msg,
            type: "error",
            duration: 10000
          });
        }
      });
    }
  },
  computed: {
    roleId() {
      return this.$store.state.user.roleId;
    }
  },
  components: {
    CommAnnotation
  }
};
</script>

<style scoped lang="scss">
.article-tip {
  margin-top: 10%;
  color: gray;
  text-align: center;
}
</style>
