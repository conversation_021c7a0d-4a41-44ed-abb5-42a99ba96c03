<template>
  <div>
    <h2 v-show="!show && visible !== ''" class="article-tip">{{ visible }}</h2>
    <iframe
      class="pdf"
      v-if="show"
      :style="{ height: fullHeight + 'px' }"
      :src="src"
    ></iframe>
  </div>
</template>

<script>
export default {
  data() {
    return {
      fullHeight: document.documentElement.clientHeight - 170,
      src: "",
      show: false,
      visible: ""
    };
  },
  computed: {
    articleId: function () {
      return this.$store.state.anno.annotation.articleId;
    }
  },
  watch: {
    articleId: {
      handler(now, old) {
        if (now === "") {
          this.existPdf();
          return;
        }
        if (now !== old) {
          this.setNewPdf();
        }
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    this.existPdf();
    this.setNewPdf();
  },
  methods: {
    setPdfPath() {
      // let tempUrl = this.$http.adornUrl(`/file/referencePDF/${this.articleId}`)
      const tempUrl = `https://idc.biosino.org/bfms-api/api/article/referencePDF/${this.articleId}`;
      this.src = "pdf/web/viewer.html?file=" + encodeURIComponent(tempUrl);
    },
    existPdf() {
      if (this.articleId === "") {
        this.show = false;
        this.visible = "暂无参考文献";
      }
    },
    setNewPdf() {
      if (this.articleId !== "") {
        this.show = false;
        this.setPdfPath();
        this.show = true;
      }
    }
  }
};
</script>

<style scoped lang="scss">
.article-tip {
  margin-top: 10%;
  color: gray;
  text-align: center;
}

.pdf {
  width: 100%;
}
</style>
