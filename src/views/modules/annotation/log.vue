<template>
  <div>
    <el-form :inline="true" :model="formInline" class="demo-form-inline">
      <el-form-item label="信息">
        <el-input
          v-model="formInline.msg"
          clearable
          placeholder="信息"
        ></el-input>
      </el-form-item>
      <el-form-item label="操作模块">
        <el-select
          clearable
          v-model="formInline.operation"
          class="status-width"
          placeholder="模块"
        >
          <div v-for="(value, key) in entityEnum" :key="'log1' + key">
            <el-option :label="value" :value="key"></el-option>
          </div>
        </el-select>
      </el-form-item>
      <el-form-item label="操作类型">
        <el-select
          clearable
          v-model="formInline.type"
          class="status-width"
          placeholder="类型"
        >
          <div v-for="(value, key) in optionEnum" :key="'log2' + key">
            <el-option :label="value" :value="key"></el-option>
          </div>
        </el-select>
      </el-form-item>
      <el-form-item label="操作用户">
        <el-select
          clearable
          v-model="formInline.account"
          class="status-width"
          placeholder="用户"
        >
          <div v-for="(value, key) in accountList" :key="'log3' + key">
            <el-option :label="value" :value="key"></el-option>
          </div>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          icon="el-icon-search"
          @click="
            () => {
              this.pageIndex = 1;
              this.getDataList();
            }
          "
          type="primary"
          plain
          >查询</el-button
        >
      </el-form-item>
    </el-form>

    <!--表格-->
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      style="width: 100%"
    >
      <el-table-column
        prop="operation"
        header-align="center"
        align="center"
        width="100"
        :formatter="formatter"
        label="操作模块"
      >
      </el-table-column>
      <el-table-column
        prop="type"
        header-align="center"
        align="center"
        width="100"
        label="操作类型"
      >
        <template slot-scope="scope">
          <el-tag v-if="scope.row.type === 1" size="small" type="success"
            >新增</el-tag
          >
          <el-tag v-else-if="scope.row.type === 2" size="small" type="warning"
            >编辑</el-tag
          >
          <el-tag v-else-if="scope.row.type === 3" size="small" type="danger"
            >删除</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column
        prop="msg"
        header-align="center"
        align="center"
        show-overflow-tooltip
        tooltip-effect="light"
        label="信息"
      >
      </el-table-column>
      <!--
            <el-table-column
              label="信息">
              <template slot-scope="scope">
                <el-popover trigger="hover" placement="top">
                  <p>{{ scope.row.msg }}</p>
                  <div slot="reference" class="name-wrapper">
                    <p size="medium">{{ scope.row.msg }}</p>
                  </div>
                </el-popover>
              </template>
            </el-table-column>-->

      <el-table-column
        prop="account"
        header-align="center"
        align="center"
        width="100"
        label="用户"
      >
      </el-table-column>
      <el-table-column
        prop="createTime"
        header-align="center"
        align="center"
        width="200"
        label="创建时间"
      >
        <template slot-scope="scope">
          <div>{{ scope.row.createTime | dateFrm }}</div>
        </template>
      </el-table-column>
      <!--<el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="100"
        label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="toEntity">查看</el-button>
        </template>
      </el-table-column>-->
    </el-table>
    <!--分页-->
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    >
    </el-pagination>
  </div>
</template>

<script>
import moment from "moment";

export default {
  data() {
    return {
      optionEnum: [],
      entityEnum: [],
      dataList: [],
      accountList: [],
      formInline: {
        msg: "",
        type: null,
        operation: null,
        account: null
      },
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      addOrUpdateVisible: false,
      dataListLoading: false
    };
  },
  filters: {
    dateFrm: function (value) {
      return moment(value).format("YYYY-MM-DD hh:mm:ss");
    }
  },
  computed: {
    annotation: function () {
      return this.$store.state.anno.annotation;
    }
  },
  components: {},
  mounted() {
    this.getDataList();
  },
  methods: {
    // 查看标注实体
    /* toEntity() {
        this.$router.push({path: '/annotation/info'})
      }, */
    formatter(row, column) {
      return row.operation + "标注";
    },
    getDataList() {
      this.dataListLoading = true;
      this.$http({
        url: this.$http.adornUrl("/operation/getDocument"),
        method: "get",
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          msg: this.formInline.msg,
          noteId: this.annotation.noteId,
          type: this.formInline.type,
          operation: this.formInline.operation,
          account: this.formInline.account
        })
      }).then(({ data }) => {
        if (
          data &&
          data.code === 0 &&
          data.optionEnum &&
          data.entityEnum &&
          data.account
        ) {
          this.dataList = data.page.list;
          this.totalPage = data.page.totalCount;

          this.optionEnum = data.optionEnum;
          this.entityEnum = data.entityEnum;
          this.accountList = data.account;
        } else {
          this.dataList = [];
          this.totalPage = 0;
          this.optionEnum = [];
          this.entityEnum = [];
          this.accountList = [];
        }
        this.dataListLoading = false;
      });
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val;
      this.pageIndex = 1;
      this.getDataList();
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val;
      this.getDataList();
    }
  }
};
</script>

<style scoped></style>
