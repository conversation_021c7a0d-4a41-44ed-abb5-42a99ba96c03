<template>
  <div class="mod-home">
    <!--统计-->
    <overview ref="overviewRef"></overview>
    <!--项目总进度趋势图-->
    <progress-chart ref="progressChart"></progress-chart>

    <div class="chart-bar">
      <!--标注进度-->
      <work-chart chart-id="annWorkChart" ref="annWorkChart"></work-chart>
      <!--审核进度-->
      <work-chart
        chart-id="auditorWorkChart"
        ref="auditorWorkChart"
      ></work-chart>
    </div>

    <batch-correct-chart ref="batchCorrectChart"></batch-correct-chart>

    <anno-statistics
      ref="annoStatistics"
      :project-id="projectId"
    ></anno-statistics>

    <auditor-statistics
      ref="auditorStatistics"
      :project-id="projectId"
    ></auditor-statistics>

    <calculation-consistency
      v-if="multiple"
      ref="calculationConsistency"
      :project-id="projectId"
    ></calculation-consistency>

    <div class="chart-bar">
      <!--标注准确率折线图-->
      <correct-rat-chart
        chart-id="correctRateChart"
        ref="correctRateChart"
      ></correct-rat-chart>

      <consistency-chart
        v-if="multiple"
        chart-id="consistencyChart"
        ref="consistencyChart"
      ></consistency-chart>
    </div>

    <dendrogram
      v-if="multiple"
      ref="dendrogram"
      :project-id="projectId"
    ></dendrogram>
  </div>
</template>

<script>
import overview from "@/views/common/overview.vue";
import progressChart from "@/views/common/progress-chart.vue";
import workChart from "@/views/common/work-chart.vue";
import batchCorrectChart from "@/views/common/batch-correct-chart.vue";
import annoStatistics from "@/views/modules/project/components/anno-statistics.vue";
import auditorStatistics from "@/views/modules/project/components/auditor-statistics.vue";
import CalculationConsistency from "@/views/modules/project/components/calculation-consistency.vue";
import Dendrogram from "@/views/modules/project/components/dendrogram.vue";
import CorrectRatChart from "@/views/modules/project/components/correct-rate-chart.vue";
import ConsistencyChart from "@/views/modules/project/components/consistency-chart.vue";

export default {
  props: {
    projectId: {
      required: true,
      type: String
    }
  },
  name: "statistics",
  data() {
    return {
      multiple: false
    };
  },
  components: {
    ConsistencyChart,
    CorrectRatChart,
    Dendrogram,
    CalculationConsistency,
    overview,
    progressChart,
    workChart,
    annoStatistics,
    auditorStatistics,
    batchCorrectChart
  },
  mounted() {
    this.$nextTick(() => {
      this.refreshData();
    });
  },
  methods: {
    refreshData() {
      this.$http({
        url: this.$http.adornUrl("/project/info/" + this.projectId),
        method: "get"
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.$refs.overviewRef.init(this.projectId);
          this.$refs.progressChart.init(this.projectId);

          this.$refs.annWorkChart.init(
            this.projectId,
            this.$RoleEnum.annotator
          );
          this.$refs.auditorWorkChart.init(
            this.projectId,
            this.$RoleEnum.auditor
          );

          this.$refs.batchCorrectChart.init(this.projectId);

          this.$refs.annoStatistics.init(this.projectId);
          this.$refs.auditorStatistics.init(this.projectId);

          this.$refs.correctRateChart.init(this.projectId);

          if (data.project.markRounds > 1) {
            this.multiple = true;
            this.$nextTick(() => {
              this.$refs.consistencyChart.init(this.projectId);
              this.$refs.calculationConsistency.init(this.projectId);
              this.$refs.dendrogram.init(this.projectId);
            });
          }
        }
      });
    }
  }
};
</script>

<style scoped lang="scss">
.mod-home {
  line-height: 1.5;
}

.home-top,
.chart-bar {
  display: flex;
  justify-content: space-between;

  .item {
    padding: 10px 20px;
    flex-grow: 1;
    flex-shrink: 1;
    flex-basis: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    border-radius: 8px;

    &:hover {
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }

    div:first-child {
      margin-right: 35px;
      width: 55px;
      height: 55px;
      text-align: center;
      line-height: 63px;
      background-color: #579aff;
      border-radius: 8px;

      .icon-svg,
      i {
        font-size: 1.5rem;
        color: #fff;
      }
    }

    div:last-child {
      display: flex;
      flex-direction: column;
      text-align: right;

      span:first-child {
        font-size: 1.4rem;
        color: #182524;
        font-weight: 600;
      }

      span:last-child {
        color: #6b7373;
      }
    }
  }

  .item:not(:last-child),
  .el-card:not(:last-child) {
    margin-right: 10px;
  }

  .el-card {
    flex-grow: 1;
    flex-shrink: 1;
    flex-basis: 100%;

    ::v-deep .el-card__body {
      height: 100% !important;
    }
  }
}
</style>
