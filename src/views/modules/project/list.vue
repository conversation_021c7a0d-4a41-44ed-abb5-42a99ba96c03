<template>
  <div>
    <el-form :inline="true" :model="formInline" class="demo-form-inline">
      <el-form-item label="名称">
        <el-input
          v-model="formInline.name"
          clearable
          placeholder="名称"
          maxlength="30"
        ></el-input>
      </el-form-item>
      <el-form-item label="状态">
        <el-select
          clearable
          v-model="formInline.status"
          class="status-width"
          placeholder="状态"
        >
          <el-option label="正常" value="1"></el-option>
          <el-option label="禁用" value="0"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="创建时间">
        <el-date-picker
          v-model="formInline.date"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button @click="getDataList" icon="el-icon-search">查询</el-button>
      </el-form-item>

      <el-form-item>
        <el-button
          icon="el-icon-plus"
          v-if="isAuth('project:editable')"
          type="primary"
          @click="addOrUpdateHandle()"
          >新增
        </el-button>
      </el-form-item>
    </el-form>

    <!--添加或更新-->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    ></add-or-update>
    <!-- 上传预标注信息 -->
    <upload-pre-anno
      v-if="uploadPreAnnoVisible"
      ref="uploadPreAnno"
      @refreshDataList="getDataList"
    ></upload-pre-anno>
    <!--上传关系标注数据-->
    <upload-relation
      v-if="loadPreRelationshipVisible"
      ref="loadPreRelationship"
      @refreshDataList="getDataList"
    ></upload-relation>
    <!--数据导出-->
    <Export v-if="exportVisible" ref="export"></Export>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @sort-change="sortChange"
      style="width: 100%; border-radius: 8px"
      :header-row-style="{ height: '44px' }"
      :header-cell-style="{
        background: 'rgb(246 247 249)',
        padding: '0px'
      }"
    >
      <el-table-column
        prop="name"
        header-align="center"
        align="center"
        label="名称"
        sortable="custom"
        min-width="90"
      >
      </el-table-column>
      <el-table-column
        prop="markRounds"
        header-align="center"
        align="center"
        label="标注轮数"
        width="80"
      >
      </el-table-column>
      <el-table-column
        prop="description"
        header-align="center"
        align="center"
        label="描述"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
      <el-table-column
        prop="code"
        header-align="center"
        align="center"
        width="270"
        label="识别码"
      >
        <template slot-scope="scope">
          <el-row type="flex" justify="space-around" style="text-align: center">
            <el-col>
              <div style="font-size: 12px">{{ scope.row.code }}</div>
            </el-col>
            <el-col :span="4">
              <el-button
                @click="resetToken(scope.row.projectId)"
                icon="el-icon-refresh"
                circle
                size="mini"
                plain
              ></el-button>
            </el-col>
          </el-row>
        </template>
      </el-table-column>

      <el-table-column
        prop="createTime"
        header-align="center"
        align="center"
        width="160"
        sortable="custom"
        label="创建时间"
      >
        <template slot-scope="scope">
          <div>{{ scope.row.createTime | dateFrm }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="status"
        header-align="center"
        align="center"
        width="70"
        label="状态"
      >
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === 0" size="small" type="danger"
            >禁用</el-tag
          >
          <el-tag v-else size="small">正常</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        header-align="center"
        width="290"
        align="left"
        label="操作"
      >
        <template slot-scope="scope">
          <el-tooltip content="批次管理" placement="top" effect="light">
            <router-link :to="'/project/batch/' + scope.row.projectId">
              <i class="pointer">
                <icon-svg
                  :name="'project'"
                  class="icon-inner icon-primary"
                ></icon-svg>
              </i>
            </router-link>
          </el-tooltip>
          <el-tooltip content="实体标签管理" placement="top" effect="light">
            <i class="pointer" @click="privateEntityLabelManagement(scope.row)">
              <icon-svg
                :name="'entity-label'"
                class="icon-inner icon-primary"
              ></icon-svg>
            </i>
          </el-tooltip>
          <el-tooltip content="关系标签管理" placement="top" effect="light">
            <i
              class="pointer"
              @click="privateRelationLabelManagement(scope.row)"
            >
              <icon-svg
                :name="'relation'"
                class="icon-inner icon-primary"
              ></icon-svg>
            </i>
          </el-tooltip>
          <el-tooltip content="关系模板管理" placement="top" effect="light">
            <i
              class="pointer"
              @click="privateRelationPatternManagement(scope.row)"
            >
              <icon-svg
                :name="'moban'"
                class="icon-inner icon-primary"
              ></icon-svg>
            </i>
          </el-tooltip>
          <el-tooltip content="消歧数据管理" placement="top" effect="light">
            <i class="pointer" @click="privateUmlsConcept(scope.row)">
              <icon-svg
                :name="'uml'"
                class="icon-inner icon-primary"
              ></icon-svg>
            </i>
          </el-tooltip>
          <el-tooltip content="统计" placement="top" effect="light">
            <router-link :to="'/project/statistics/' + scope.row.projectId">
              <i style="margin-right: 0" class="pointer">
                <icon-svg :name="'jindu'" class="icon-inner"></icon-svg>
              </i>
            </router-link>
          </el-tooltip>
          <el-tooltip
            v-if="isAuth('project:editable')"
            content="预标注数据导入"
            placement="top"
            effect="light"
          >
            <i
              class="el-icon-upload2 icon-inner icon-warning ml-2"
              @click="uploadPreAnno(scope.row)"
            ></i>
          </el-tooltip>
          <el-tooltip content="数据导出" placement="top" effect="light">
            <i
              @click="exportData(scope.row.projectId)"
              class="el-icon-download icon-primary"
            >
            </i>
          </el-tooltip>

          <el-tooltip content="数据验证" placement="top" effect="light">
            <router-link :to="'/project/verify/' + scope.row.projectId">
              <i class="el-icon-document-checked icon-gray"></i>
            </router-link>
          </el-tooltip>

          <el-tooltip
            v-if="isAuth('project:editable')"
            content="编辑"
            placement="top"
            effect="light"
          >
            <i
              class="el-icon-edit icon-primary"
              @click="addOrUpdateHandle(scope.row)"
            ></i>
          </el-tooltip>
          <el-tooltip
            v-if="scope.row.isAuth && isAuth('project:editable')"
            content="删除"
            placement="top"
            effect="light"
          >
            <i
              class="el-icon-delete icon-red"
              @click="deleteHandle(scope.row.projectId)"
            ></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    >
    </el-pagination>
  </div>
</template>

<script>
import AddOrUpdate from "./add-or-update";
import UploadPreAnno from "./upload-pre-anno";
import UploadRelation from "./upload-relation";
import Export from "./export.vue";
import moment from "moment";

export default {
  data() {
    return {
      formInline: {
        name: "",
        status: "",
        orderBy: undefined,
        isAsc: undefined,
        date: []
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 20,
      totalPage: 0,
      addOrUpdateVisible: false,
      uploadPreAnnoVisible: false,
      loadPreRelationshipVisible: false,
      exportVisible: false,
      dataListLoading: false
    };
  },
  filters: {
    dateFrm: function (value) {
      return moment(value).format("YYYY-MM-DD HH:mm:ss");
    }
  },
  components: {
    Export,
    AddOrUpdate,
    UploadPreAnno,
    UploadRelation
  },
  mounted() {
    this.getDataList();
  },
  methods: {
    getDataList() {
      this.dataListLoading = true;
      this.$http({
        url: this.$http.adornUrl("/project/list"),
        method: "get",
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          orderBy: this.formInline.orderBy,
          isAsc: this.formInline.isAsc,
          name: this.formInline.name,
          status: this.formInline.status,
          roleId: this.$store.state.user.roleId,
          startDate:
            (this.formInline?.date != null && this.formInline?.date[0]) ||
            undefined,
          endDate:
            (this.formInline?.date != null && this.formInline?.date[1]) ||
            undefined
        })
      })
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.dataList = data.page.list;
            this.totalPage = data.page.totalCount;
          } else {
            this.dataList = [];
            this.totalPage = 0;
          }
        })
        .finally(() => {
          this.dataListLoading = false;
        });
    },
    sortChange(column) {
      if (column.order === "descending") {
        this.formInline.orderBy = column.prop;
        this.formInline.isAsc = false;
      } else {
        this.formInline.orderBy = column.prop;
        this.formInline.isAsc = true;
      }
      // 什么排序都不选择，恢复默认
      if (column.order == null) {
        this.formInline.orderBy = undefined;
        this.formInline.isAsc = undefined;
      }
      this.pageIndex = 1;
      this.getDataList();
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val;
      this.pageIndex = 1;
      this.getDataList();
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val;
      this.getDataList();
    },
    // 新增 / 修改
    addOrUpdateHandle(project) {
      this.addOrUpdateVisible = true;
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(project);
      });
    },
    deleteHandle(id) {
      this.$confirm("确认是否删除项目？", "确认信息", {
        dangerouslyUseHTMLString: true,
        distinguishCancelAndClose: true,
        confirmButtonText: "确认",
        cancelButtonText: "取消"
      }).then(() => {
        const loading = this.$loading({
          lock: true,
          text: `删除中`,
          spinner: "el-icon-loading",
          background: "rgba(0, 0, 0, 0.7)"
        });
        this.$http({
          url: this.$http.adornUrl("/project/delete"),
          method: "get",
          params: this.$http.adornParams({
            projectId: id
          })
        })
          .then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                type: "warning",
                message: "项目已删除"
              });
            } else {
              this.$message({
                type: "error",
                message: data.msg
              });
            }
          })
          .finally(() => {
            loading.close();
            this.getDataList();
          });
      });
    },
    exportData(projectId) {
      this.exportVisible = true;
      this.$nextTick(() => {
        this.$refs.export.init(projectId);
      });
    },
    // 实体标签管理
    privateEntityLabelManagement(project) {
      this.$router.push({
        path: "/project/private-entity",
        query: { projectId: project.projectId }
      });
    },
    // 关系标签管理
    privateRelationLabelManagement(project) {
      this.$router.push({
        path: "/project/private-relation",
        query: { projectId: project.projectId }
      });
    },
    // 关系模板管理
    privateRelationPatternManagement(project) {
      this.$router.push({
        path: "/project/private-pattern",
        query: { projectId: project.projectId }
      });
    },
    privateUmlsConcept(project) {
      this.$router.push({
        path: "/project/private-umls",
        query: { projectId: project.projectId }
      });
    },
    // 预标注数据上传
    uploadPreAnno(project) {
      this.uploadPreAnnoVisible = true;
      this.$nextTick(() => {
        this.$refs.uploadPreAnno.init(project.projectId);
      });
    },

    resetToken(projectId) {
      this.$confirm("确定重置项目唯一标识码?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl("/project/resetToken"),
          method: "get",
          params: this.$http.adornParams({
            projectId: projectId
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              type: "success",
              message: "重置成功!"
            });
          }
          this.getDataList();
        });
      });
    }
  }
};
</script>

<style scoped>
.el-button.is-circle {
  padding: 5px;
}
</style>
