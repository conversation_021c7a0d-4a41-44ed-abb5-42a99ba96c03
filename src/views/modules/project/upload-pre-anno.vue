<template>
  <el-dialog
    title="预标注数据导入"
    :visible.sync="dialogVisible"
    @close="closeDialog"
    width="80%"
  >
    <el-tabs type="border-card">
      <el-tab-pane label="实体">
        <upload-pre-entity ref="uploadPreEntity"></upload-pre-entity>
      </el-tab-pane>
      <el-tab-pane label="属性">
        <upload-pre-attr ref="uploadPreAttr"></upload-pre-attr>
      </el-tab-pane>
      <el-tab-pane label="关系">
        <upload-pre-relation ref="uploadPreRelation"></upload-pre-relation>
      </el-tab-pane>
    </el-tabs>
  </el-dialog>
</template>

<script>
import UploadPreEntity from "@/views/modules/project/components/upload-pre-entity";
import UploadPreAttr from "@/views/modules/project/components/upload-pre-attr";
import UploadPreRelation from "@/views/modules/project/components/upload-pre-relation";

export default {
  name: "upload-pre-anno",
  data() {
    return {
      projectId: 0,
      dialogVisible: false
    };
  },
  components: {
    UploadPreEntity,
    UploadPreAttr,
    UploadPreRelation
  },
  methods: {
    init(projectId) {
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.uploadPreEntity.init(projectId);
        this.$refs.uploadPreAttr.init(projectId);
        this.$refs.uploadPreRelation.init(projectId);
      });
    },
    closeDialog() {
      this.$nextTick(() => {
        this.$refs.uploadPreEntity.stopThread();
        this.$refs.uploadPreAttr.stopThread();
        this.$refs.uploadPreRelation.stopThread();
      });
    }
  }
};
</script>

<style scoped lang="scss">
::v-deep .el-tabs--border-card {
  box-shadow: none;
  border-radius: 8px;
}
::v-deep.el-tabs--border-card > .el-tabs__header {
  border-bottom: none;
  ::v-deep.el-tabs__item.is-active {
    border: none !important;
  }
}
::v-deep.el-tabs--border-card > .el-tabs__header .el-tabs__item {
  border: none;
  &.is-active {
    border: none !important;
  }
}
::v-deep .el-tabs__header .el-tabs__nav-wrap,
::v-deep.el-tabs--border-card > .el-tabs__header,
::v-deep.el-tabs--border-card {
  border-radius: 8px !important;
}
</style>
