<template>
  <el-dialog title="数据导出" :visible.sync="dialogVisible" width="30%">
    <el-form :model="dataForm" ref="dataForm" label-width="80px">
      <el-form-item label="批次名" prop="batchId">
        <el-tooltip
          class="item"
          effect="dark"
          content="不选择批次默认导出整个项目的数据"
          placement="top"
        >
          <i class="el-icon-question mr-2"></i>
        </el-tooltip>
        <el-select
          style="width: 90%"
          v-model="dataForm.batchId"
          class="multiple-select"
          filterable
          clearable
          placeholder="批次名"
        >
          <el-option
            v-for="val in batchList"
            :key="'k1' + val.batchId"
            :label="val.name"
            :value="val.batchId"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="标注状态" size="mini" prop="step">
        <el-tooltip
          class="item"
          effect="dark"
          content="已验收：已标注完成，并验收通过；全部：以标注员为单位批量导出，包括所有状态"
          placement="top"
        >
          <i class="el-icon-question mr-2"></i>
        </el-tooltip>

        <el-radio-group v-model="dataForm.step">
          <el-radio :label="this.$NoteEnum.reviewed">已验收</el-radio>
          <el-radio :label="-1">全部</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="模块" size="mini" prop="module">
        <el-tooltip
          class="item"
          effect="dark"
          content="实体标注：只导出实体和实体包含属性的信息；关系标注：导出关系且关系包含的实体和属性"
          placement="top"
        >
          <i class="el-icon-question mr-2"></i>
        </el-tooltip>
        <el-radio-group v-model="dataForm.module">
          <el-radio :label="1">实体标注</el-radio>
          <el-radio :label="2">关系标注</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="格式" size="mini" prop="type">
        <el-tooltip
          class="item"
          effect="dark"
          content="JSON：导出的格式为整个JSON对象；Excel：导出的数据拆分为多个Excel sheet，需要自己根据ID关联信息"
          placement="top"
        >
          <i class="el-icon-question mr-2"></i>
        </el-tooltip>
        <el-radio-group v-model="dataForm.type">
          <el-radio :label="1">JSON</el-radio>
          <el-radio :label="2">Excel</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item
        prop="email"
        label="邮箱"
        :rules="[
          { required: true, message: '请输入邮箱地址', trigger: 'blur' },
          {
            type: 'email',
            message: '请输入正确的邮箱地址',
            trigger: ['blur', 'change']
          }
        ]"
      >
        <el-tooltip
          class="item"
          effect="dark"
          content="导出完成后下载链接将发送到该邮箱中"
          placement="top"
        >
          <i class="el-icon-question mr-2"></i>
        </el-tooltip>
        <el-input style="width: 90%" v-model="dataForm.email"></el-input>
      </el-form-item>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button
        type="primary"
        :loading="downloadLoading"
        @click="exportAnnotation()"
        >确定</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import { Loading } from "element-ui";

export default {
  name: "Export",
  data() {
    return {
      projectId: 0,
      batchList: [],
      dataForm: {
        batchId: null,
        step: this.$NoteEnum.reviewed,
        module: 1,
        email: null,
        type: 1
      },
      dialogVisible: false,
      downloadLoading: false
    };
  },
  methods: {
    init(projectId) {
      this.projectId = projectId;
      this.dialogVisible = true;
      this.getBatchList();
    },
    getBatchList() {
      this.$nextTick(() => {
        this.$refs.dataForm.resetFields();
        this.$http({
          url: this.$http.adornUrl("/batch/list"),
          method: "get",
          params: this.$http.adornParams({
            page: 1,
            limit: 10000,
            projectId: this.projectId
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.batchList = data.page.list;
          } else {
            this.dataList = [];
          }
        });
      });
    },
    exportAnnotation() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          this.$confirm(
            `请确认邮箱是：${this.dataForm.email},并提交数据导出任务`,
            "提示",
            {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning"
            }
          ).then(() => {
            const loadingInstance = Loading.service();
            this.$http({
              url: this.$http.adornUrl("/project/exportAnnotation"),
              method: "post",
              data: this.$http.adornData({
                projectId: this.projectId,
                batchId: this.dataForm.batchId,
                step: this.dataForm.step,
                module: this.dataForm.module,
                type: this.dataForm.type,
                email: this.dataForm.email
              })
            })
              .then(({ data }) => {
                if (data && data.code === 0) {
                  this.$message({
                    message:
                      "导出任务提交成功，导出完成后数据下载链接会发送到您的邮箱",
                    type: "success"
                  });
                  this.dialogVisible = false;
                }
              })
              .finally(() => {
                loadingInstance.close();
              });
          });
        }
      });
    }
  }
};
</script>

<style scoped>
::v-deep .el-form-item__error {
  margin-left: 20px;
}
</style>
