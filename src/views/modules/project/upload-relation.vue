<template>
  <el-dialog title="提示" :visible.sync="dialogVisible" width="60%">
    <el-upload
      ref="upload"
      :http-request="uploadPreAnno"
      :limit="1"
      :multiple="false"
      :show-file-list="true"
      accept=".json"
      action=""
      class="upload-content"
      style="text-align: center"
    >
      <el-button style="width: 200px" type="primary">点击上传</el-button>
      <div slot="tip" class="el-upload__tip">
        <p>只能上传 <strong>JSON</strong> 文件，且不超过100M</p>
      </div>
    </el-upload>

    <!--        <div style="text-align: center">-->
    <!--            <a href="javascript:void(0);" @click.prevent="downloadPreAnnoTemplate" type="primary"><i-->
    <!--                    class="el-icon-download"></i>下载模板</a>-->
    <!--        </div>-->
    <el-button
      style="float: right"
      @click.prevent="getDataList()"
      type="success"
      >刷新</el-button
    >
    <el-table
      border
      :data="currentData"
      v-loading="dataListLoading"
      style="width: 100%"
    >
      <el-table-column
        prop="id"
        label="Id"
        header-align="center"
        align="center"
      >
      </el-table-column>
      <el-table-column
        prop="name"
        label="名称"
        header-align="center"
        align="center"
      >
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="创建时间"
        header-align="center"
        align="center"
      >
      </el-table-column>
      <el-table-column
        prop="status"
        label="状态"
        header-align="center"
        align="center"
      >
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === -1" size="small" type="danger"
            >失败</el-tag
          >
          <el-tag v-if="scope.row.status === 1" size="small">导入中</el-tag>
          <el-tag v-if="scope.row.status === 2" size="small" type="success"
            >成功</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column align="center" header-align="center" label="操作">
        <template slot-scope="scope">
          <el-tooltip
            v-if="isAuth('project:editable') && scope.row.status === -1"
            content="下载错误日志"
            placement="top"
            effect="light"
          >
            <i class="pointer" @click="downloadErrorLog(scope.row.id)">
              <icon-svg :name="'download'" class="icon-inner"></icon-svg>
            </i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      :page-size="pageSize"
      :current-page.sync="currentPage"
      :page-sizes="[5, 10]"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </el-dialog>
</template>

<script>
import { Loading } from "element-ui";

export default {
  name: "upload-pre-relation",
  data() {
    return {
      projectId: 0,
      currentPage: 1,
      pageSize: 5,
      dialogVisible: false,
      tableData: [],
      dataListLoading: false,
      sort: {
        prop: "",
        order: ""
      }
    };
  },
  mounted() {},
  computed: {
    total() {
      return this.tableData.length;
    },
    currentData() {
      if (
        this.tableData === undefined ||
        this.tableData === null ||
        !this.tableData ||
        this.tableData.length === 0
      ) {
        return [];
      }
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.tableData.slice(start, end);
    }
  },
  watch: {
    dialogVisible: function () {
      if (!this.dialogVisible) {
        this.$refs.upload.clearFiles();
      }
    }
  },
  methods: {
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1;
    },
    handleCurrentChange(val) {
      this.currentPage = val;
    },

    init(project) {
      this.dialogVisible = true;
      this.projectId = project.projectId;
      this.getDataList();
      if (!this.projectId) {
        this.dialogVisible = false;
      }
    },

    downloadPreAnnoTemplate() {
      this.$http({
        url: this.$http.adornUrl("/file/preAnnoTemplate"),
        method: "get",
        responseType: "blob"
      })
        .then((resp) => {
          if (resp.headers["content-type"] !== "application/octet-stream") {
            const err = "模板下载失败";
            throw err;
          }
          const url = window.URL.createObjectURL(new Blob([resp.data]));
          const link = document.createElement("a");
          const filename = decodeURI(resp.headers.filename);
          link.href = url;
          link.download = filename;
          link.click();
        })
        .catch((err) => {
          this.$message.error(err);
        });
    },
    uploadPreAnno(params) {
      this.$refs.upload.clearFiles();
      const file = params.file;
      if (!file.name.endsWith(".json") || file.size / 1024 / 1024 > 100) {
        this.$message.error({
          dangerouslyUseHTMLString: true,
          message: "只能上传 <strong>JSON</strong> 文件，且不超过100M"
        });
        params.onError();
        return;
      }

      const loadingInstance = Loading.service();

      const formData = new FormData();
      formData.append("file", file);
      formData.append("projectId", this.projectId);
      this.$http({
        url: this.$http.adornUrl("/project/uploadPreRelation"),
        method: "post",
        timeout: 3 * 60000, // 超时时间改为3分钟
        headers: { "Content-Type": "multipart/form-data;" },
        data: formData
      })
        .then(
          ({ data }) => {
            loadingInstance.close();
            this.dialogVisible = false;
            if (data.code === 0) {
              this.$message.success("后台导入中");
              this.getDataList();
            } else {
              this.$message.error(data.msg);
            }
            this.$emit("refreshDataList");
            params.onSuccess();
          },
          () => {
            loadingInstance.close();
            this.dialogVisible = false;
            this.$message.error("上传超时，请检查网络是否顺畅！");
          }
        )
        .catch(() => {
          loadingInstance.close();
          this.dialogVisible = false;
          this.$message.error("文件上传失败！");
        });
    },
    getDataList() {
      this.$http({
        url: this.$http.adornUrl("/project/listRelationImport"),
        method: "get",
        params: this.$http.adornParams({
          projectId: this.projectId
        })
      }).then(({ data }) => {
        if (data && data.code === 0 && data.data !== undefined) {
          this.tableData = data.data;
        } else {
          this.tableData = [];
        }
        this.dataListLoading = false;
      });
    },
    downloadErrorLog(id) {
      this.$http({
        url: this.$http.adornUrl("/project/relationImportErrorLog"),
        params: this.$http.adornParams({
          id: id
        }),
        method: "get",
        responseType: "blob"
      })
        .then((resp) => {
          if (resp.headers["content-type"] !== "application/octet-stream") {
            const err = "模板下载失败";
            throw err;
          }
          const url = window.URL.createObjectURL(new Blob([resp.data]));
          const link = document.createElement("a");
          const filename = decodeURI(resp.headers.filename);
          link.href = url;
          link.download = filename;
          link.click();
        })
        .catch((err) => {
          this.$message.error(err);
        });
    }
  }
};
</script>

<style scoped></style>
