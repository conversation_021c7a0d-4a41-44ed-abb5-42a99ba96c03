<template>
  <el-dialog
    :title="!dataForm.projectId ? '新增' : '编辑'"
    :visible.sync="visible"
    :close-on-click-modal="false"
    width="60%"
  >
    <el-form
      v-loading="loading"
      element-loading-text="加载中"
      ref="dataForm"
      :model="dataForm"
      :rules="this.dataRule"
      label-width="100px"
    >
      <el-form-item v-show="false" prop="projectId">
        <el-input v-model="dataForm.projectId"></el-input>
      </el-form-item>

      <el-row>
        <el-col :span="12">
          <el-form-item label="项目名称" prop="name">
            <el-input
              v-model="dataForm.name"
              placeholder="项目名称"
              maxlength="30"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="项目配置" prop="projectConfig">
            <el-select
              :disabled="dataForm.projectId != null"
              v-model="dataForm.projectConfig"
              class="multiple—select"
              placeholder="继承其他项目配置"
              @change="changeExtendProject"
            >
              <el-option
                v-for="project in projectList"
                :key="project.id"
                :label="`${project.name}`"
                :value="project.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="标注员" prop="annotators" required>
            <el-select
              v-model="dataForm.annotators"
              class="multiple—select"
              filterable
              multiple
              placeholder="标注员"
            >
              <el-option
                v-for="annotator in annotators"
                :key="annotator.userId"
                :label="annotator.username"
                :value="annotator.userId"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="审核员" prop="auditors">
            <el-select
              v-model="dataForm.auditors"
              class="multiple—select"
              filterable
              multiple
              placeholder="审核员"
            >
              <el-option
                v-for="auditor in auditors"
                :key="auditor.userId"
                :label="auditor.username"
                :value="auditor.userId"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item
            v-show="currentUserId === dataForm.creatorId || !dataForm.projectId"
            label="共享管理员"
            prop="projectAdmins"
          >
            <el-select
              v-model="dataForm.projectAdmins"
              class="multiple—select"
              filterable
              multiple
              @remove-tag="(arg) => removeAdmin(arg, 'admin')"
              placeholder="共享管理员"
            >
              <el-option
                v-for="admin in projectAdmins"
                :key="admin.userId"
                :label="admin.username"
                :value="admin.userId"
                :disabled="
                  admin.userId === dataForm.creatorId ||
                  admin.userId === currentUserId
                "
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="标注轮数" prop="type">
            <el-radio-group v-model="dataForm.number">
              <el-input-number
                size="small"
                v-model="dataForm.number"
                :disabled="dataForm.projectId != null"
                controls-position="right"
                :min="1"
                :max="5"
              ></el-input-number>
            </el-radio-group>
            <el-tooltip
              class="item"
              effect="dark"
              content="默认1为单人标注模式，N为一篇文书可以被N人标注"
              placement="top"
            >
              <i class="el-icon-question ml-2"></i>
            </el-tooltip>
            <el-checkbox
              v-if="dataForm.number > 1"
              class="ml-4"
              v-model="dataForm.autoReview"
              >自动审核</el-checkbox
            >
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="项目观察员" prop="projectWatchers">
            <el-select
              v-model="dataForm.projectWatchers"
              class="multiple—select"
              filterable
              multiple
              @remove-tag="(arg) => removeAdmin(arg, 'watcher')"
              placeholder="项目观察员"
            >
              <el-option
                v-for="admin in projectWatchers"
                :key="admin.userId"
                :label="admin.username"
                :value="admin.userId"
                :disabled="
                  admin.userId === dataForm.creatorId ||
                  admin.userId === currentUserId
                "
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status" size="mini">
            <el-radio-group v-model="dataForm.status">
              <el-radio :label="1">正常</el-radio>
              <el-radio :label="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="描述" prop="description">
        <el-input
          rows="3"
          v-model="dataForm.description"
          placeholder="描述"
          type="textarea"
          maxlength="255"
        ></el-input>
      </el-form-item>

      <el-row>
        <el-col :span="24">
          <el-form-item label="标注规范">
            <mavon-editor
              v-model="dataForm.markdownContent"
              :height="300"
              :toolbars="toolbars"
              :subfield="true"
              default-open="edit"
              @keyup.enter.native.stop
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { Loading } from "element-ui";
import { mavonEditor } from "mavon-editor";
import "mavon-editor/dist/css/index.css";

export default {
  components: {
    mavonEditor
  },
  name: "add-or-update",
  data() {
    const checkProjectName = (rule, value, callback) => {
      if (!value) {
        return callback(new Error("名称不能为空"));
      }
      this.$http({
        url: this.$http.adornUrl("/project/validate/projectName"),
        method: "get",
        params: this.$http.adornParams({
          projectName: value,
          projectId: this.dataForm.projectId || undefined
        })
      })
        .then(({ data }) => {
          if (data && data.code !== 0) {
            callback(new Error(data.msg));
          }
          callback();
        })
        .catch((error) => callback(new Error(error)));
    };

    return {
      currentUserId: undefined,
      visible: false,
      project: null,
      loading: false,
      dataForm: {
        projectId: undefined,
        name: "",
        number: 1,
        description: "",
        autoReview: false,
        status: 1,
        annotators: [],
        auditors: [],
        projectAdmins: [],
        projectWatchers: [],
        projectConfig: undefined,
        preSources: [],
        markdownContent: "# 项目概述\n\n# 文本描述\n\n",
        creatorId: undefined
      },
      users: [],
      preSources: [],
      projectList: [],
      toolbars: {
        bold: true,
        italic: true,
        header: true,
        underline: true,
        strikethrough: true,
        mark: true,
        superscript: true,
        subscript: true,
        quote: true,
        ol: true,
        ul: true,
        link: true,
        imagelink: false,
        code: true,
        table: true,
        fullscreen: true,
        readmodel: true,
        htmlcode: true,
        help: true,
        undo: true,
        redo: true,
        trash: true,
        save: false,
        navigation: true,
        alignleft: true,
        aligncenter: true,
        alignright: true,
        subfield: true,
        preview: true
      },
      dataRule: {
        name: [{ required: true, validator: checkProjectName, trigger: "blur" }]
      }
    };
  },
  watch: {
    visible: function () {
      if (!this.visible) {
        this.$nextTick(() => {
          this.$refs.dataForm.resetFields();
          this.preSources = [];
          this.dataForm.markdownContent = "# 项目概述\n\n# 文本描述\n\n";
        });
      }
    }
  },
  created() {
    this.currentUserId = this.$store.state.user.id;
  },
  computed: {
    annotators: function () {
      return this.users.filter(
        (it) =>
          it.roleIdList &&
          it.roleIdList.indexOf(this.$RoleEnum.annotator) !== -1 &&
          it.userId !== 2
      );
    },
    auditors: function () {
      return this.users.filter(
        (it) =>
          it.roleIdList &&
          it.roleIdList.indexOf(this.$RoleEnum.auditor) !== -1 &&
          it.userId !== 2
      );
    },
    projectAdmins: function () {
      return this.users.filter(
        (it) =>
          it.roleIdList &&
          it.roleIdList.indexOf(this.$RoleEnum.projectAdmin) !== -1 &&
          it.userId !== 2
      );
    },
    projectWatchers: function () {
      return this.users.filter(
        (it) =>
          it.roleIdList &&
          it.roleIdList.indexOf(this.$RoleEnum.projectWatcher) !== -1 &&
          it.userId !== 2
      );
    }
  },
  methods: {
    changeExtendProject(extendPrjId) {
      this.fillPrjInfo(extendPrjId);
    },
    fillPrjInfo(prjId) {
      this.loading = true;
      this.dataForm.number = 1;
      this.dataForm.autoReview = true;
      this.$http({
        url: this.$http.adornUrl("/project/participantUser"),
        method: "get",
        params: this.$http.adornParams({
          projectId: prjId
        })
      })
        .then(({ data }) => {
          if (data && data.code === 0) {
            data = data.data || [];
            this.dataForm.annotators = data
              .filter((it) => it.roleId === this.$RoleEnum.annotator)
              .map((it) => it.userId);
            this.dataForm.auditors = data
              .filter((it) => it.roleId === this.$RoleEnum.auditor)
              .map((it) => it.userId);
            this.dataForm.projectAdmins = data
              .filter((it) => it.roleId === this.$RoleEnum.projectAdmin)
              .map((it) => it.userId);
            this.dataForm.projectWatchers = data
              .filter((it) => it.roleId === this.$RoleEnum.projectWatcher)
              .map((it) => it.userId);

            this.dataForm.number = data.find(
              (it) => it.projectId === Number(prjId)
            )?.markRounds;

            this.dataForm.autoReview = !!data.find(
              (it) => it.projectId === Number(prjId)
            )?.autoReview;
          } else {
            this.dataForm.annotators = [];
            this.dataForm.auditors = [];
          }
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    init: function (project) {
      this.project = project;
      this.visible = true;
      this.loading = true;
      this.$nextTick(() => {
        this.$refs.dataForm.resetFields();
        this.preSources = [];
        this.projectList = [];
        this.dataForm.markdownContent = "# 项目概述\n\n# 文本描述\n\n";

        // 预标注
        /*this.$http({
          url: this.$http.adornUrl("/project/preSources"),
          method: "get"
        }).then(({ data }) => {
          if (data && data.code === 0 && data.data) {
            this.preSources = Object.keys(data.data).map((key) => {
              return { name: key, title: data.data[key] };
            });
          } else {
            this.preSources = [];
          }
        });*/

        // 查询所有标注员和审核员
        this.$http({
          url: this.$http.adornUrl("/project/user"),
          method: "get",
          params: this.$http.adornParams()
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.users = data.users || [];
          } else {
            this.users = [];
          }

          // 新增时，查询有哪些项目
          if (!project) {
            this.$http({
              url: this.$http.adornUrl("/project/findAllProject"),
              method: "get"
            }).then(({ data }) => {
              if (data && data.code === 0 && data.data) {
                this.projectList = Object.keys(data.data).map((key) => {
                  return { id: key, name: data.data[key] };
                });
              } else {
                this.projectList = [];
              }
            });
          }

          // 编辑回显相关信息
          if (project) {
            this.dataForm.projectId = project.projectId;
            this.dataForm.name = project.name;
            this.dataForm.description = project.description;
            this.dataForm.status = project.status;
            this.dataForm.markdownContent =
              project.markdownContent || "# 项目概述\n\n# 文本描述\n\n";
            this.dataForm.creatorId = project.creatorId;

            this.dataForm.projectConfig = project.projectConfig;
            if (project.preSources) {
              this.dataForm.preSources = project.preSources.split(",");
            } else {
              this.dataForm.preSources = [];
            }

            // 标注员审核员查询成功后才查询参与项目的成员
            this.fillPrjInfo(this.dataForm.projectId);
          } else {
            this.loading = false;
          }
        });
        this.$forceUpdate();
      });
    },

    removeAdmin(arg, place) {
      if (arg === this.$store.state.user.id) {
        this.$message.warning("这是当前用户，不能移除");
        if (place === "admin") {
          this.dataForm.projectAdmins.push(arg);
        } else {
          this.dataForm.projectWatchers.push(arg);
        }
        return;
      }
      if (arg === this.dataForm.creatorId) {
        this.$message.warning("这是项目创建者，不能移除");
        if (place === "admin") {
          this.dataForm.projectAdmins.push(arg);
        } else {
          this.dataForm.projectWatchers.push(arg);
        }
      }
    },
    dataFormSubmit() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          if (this.dataForm.annotators.length < this.dataForm.number) {
            this.$message({
              message: "标注员数量不能少于标注轮数",
              type: "error"
            });
            return;
          }
          const loadingInstance = Loading.service();
          this.$http({
            url: this.$http.adornUrl("/project/save"),
            method: "post",
            data: this.$http.adornData({
              projectId: this.dataForm.projectId || undefined,
              name: this.dataForm.name,
              markRounds: this.dataForm.number,
              autoReview: this.dataForm.autoReview,
              description: this.dataForm.description,
              status: this.dataForm.status,
              annotators: this.dataForm.annotators,
              projectConfig: this.dataForm.projectConfig,
              auditors: this.dataForm.auditors,
              creatorId: this.dataForm.creatorId,
              projectAdmins: this.dataForm.projectAdmins,
              projectWatchers: this.dataForm.projectWatchers,
              preSources: this.dataForm.preSources.join(",") || undefined,
              markdownContent: this.dataForm.markdownContent
            })
          })
            .then(({ data }) => {
              this.visible = false;
              if (data && data.code === 0) {
                this.$message({
                  message: "操作成功",
                  type: "success"
                });
              } else {
                this.$message.error(data.msg);
              }
              this.$emit("refreshDataList");
              loadingInstance.close();
            })
            .catch((e) => {
              this.visible = false;
              loadingInstance.close();
              this.$message.error(e);
            });
        }
      });
    }
  }
};
</script>

<style scoped>
.multiple—select {
  width: 100%;
}
.upload-text {
  font-size: 12px;
}
</style>
