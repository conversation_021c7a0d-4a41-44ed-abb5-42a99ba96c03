<template>
  <div>
    <el-row type="flex" style="margin-bottom: 8px">
      <el-col :span="20" style="display: flex; align-items: center">
        <el-upload
          ref="upload"
          :http-request="uploadArticles"
          :limit="1"
          :multiple="false"
          :show-file-list="true"
          accept=".txt"
          action=""
          class="upload-content"
        >
          <el-button
            class="upload-btn"
            type="primary"
            icon="el-icon-upload2"
            size="mini"
            >上传文件</el-button
          >
        </el-upload>
        <el-button
          @click.prevent="downloadTemplate"
          icon="el-icon-download"
          type="primary"
          class="download-btn"
          >下载模板</el-button
        >
        <!--        <a-->
        <!--          href="javascript:void(0);"-->
        <!--          @click.prevent="downloadTemplate"-->
        <!--          type="primary"-->
        <!--          class="download-btn"-->
        <!--        >-->
        <!--          <i class="el-icon-download ml-1"></i>下载模板-->
        <!--        </a>-->
        <span class="el-upload__tip"
          >支持上传大小不超过1M，格式为txt的文件</span
        >
      </el-col>
      <el-col :span="4">
        <el-tooltip
          class="item"
          effect="light"
          content="刷新"
          placement="top-start"
        >
          <el-button
            size="small"
            class="refresh-btn"
            style="float: right"
            icon="el-icon-refresh"
            @click="getDataList()"
            circle
          ></el-button>
        </el-tooltip>
      </el-col>
    </el-row>
    <el-table
      v-loading="dataListLoading"
      :header-cell-style="{ background: '#F5F7FA' }"
      :data="dataList"
      border
      :row-style="{ height: '30px' }"
      style="width: 100%"
      type="expand"
    >
      <el-table-column
        label="文件"
        min-width="180px"
        prop="name"
        header-align="center"
        align="center"
      >
        <template slot-scope="scope">
          <a
            href="javascript:void(0);"
            @click="download('source', scope.row.id)"
            >{{ scope.row.name }}</a
          >
        </template>
      </el-table-column>
      <el-table-column min-width="100px" align="center" label="总条数">
        <template slot-scope="scope">
          {{ scope.row.data.total }}
        </template>
      </el-table-column>
      <el-table-column min-width="100px" align="center" label="成功数">
        <template slot-scope="scope">
          {{ scope.row.data.success }}
        </template>
      </el-table-column>
      <el-table-column min-width="100px" align="center" label="失败数">
        <template slot-scope="scope">
          {{ scope.row.data.fail }}
        </template>
      </el-table-column>
      <el-table-column min-width="100px" align="center" label="重复数">
        <template slot-scope="scope">
          {{ scope.row.data.repeat }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        min-width="200px"
        label="上传时间"
        prop="createTime"
      ></el-table-column>
      <el-table-column
        min-width="100px"
        align="center"
        label="导入状态"
        prop="status"
      >
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === -1" size="small" type="danger"
            >导入失败
          </el-tag>
          <el-tag v-if="scope.row.status === 0" size="small">待导入</el-tag>
          <el-tag v-if="scope.row.status === 1" size="small">导入中</el-tag>
          <el-tag v-if="scope.row.status === 2" size="small" type="success"
            >导入完成
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        v-if="editable"
        label="操作"
        prop="alteration"
        width="200px"
        min-width="100px"
      >
        <template slot-scope="scope">
          <a
            v-if="scope.row.status === 2"
            href="javascript:void(0);"
            @click="exportResult(scope.row.id)"
            ><i class="el-icon-download"></i>导出日志</a
          >
          <a
            style="margin-left: 10px"
            v-if="scope.row.status === 2 && scope.row.data.fail > 0"
            href="javascript:void(0);"
            @click="reloadArticles(scope.row.id)"
            ><i class="el-icon-refresh-left"></i>重新导入</a
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    >
    </el-pagination>
  </div>
</template>

<script>
import { Loading } from "element-ui";
import FileSaver from "file-saver";
import Thread from "@/utils/thread";

export default {
  name: "upload-txt",
  data() {
    return {
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      type: this.$ImportType.article_bfms,
      batchId: 0,
      thread: new Thread({
        start: () => {
          this.getDataList();
        },
        stop: () => {
          console.log("结束轮询");
        },
        time: 3000
      }),
      dataListLoading: false
    };
  },
  mounted() {},
  computed: {
    editable: function () {
      return this.isAuth("project:editable");
    }
  },
  methods: {
    stopThread() {
      this.thread.stop();
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val;
      this.pageIndex = 1;
      this.getDataList();
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val;
      this.getDataList();
    },
    init(batchId) {
      this.batchId = batchId;
      this.$nextTick(() => {
        this.getDataList();
        this.thread.run();
      });
    },
    downloadTemplate() {
      this.$downloadTemplate(
        "article-txt-template.txt",
        "BFMS文章导入--模板.txt"
      );
    },
    getDataList() {
      this.dataListLoading = false;
      this.$http({
        url: this.$http.adornUrl("/importLog/list"),
        method: "get",
        params: this.$http.adornParams({
          batchId: this.batchId,
          type: this.type,
          page: this.pageIndex,
          limit: this.pageSize
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list;
          this.totalPage = data.page.totalCount;
        } else {
          this.dataList = [];
          this.totalPage = 0;
        }
        this.dataListLoading = false;
      });
    },
    uploadArticles: function (params) {
      this.$refs.upload.clearFiles();
      const file = params.file;
      const isTxt = /\.txt$/.test(file.name);

      if (!isTxt || file.size / 1024 / 1024 > 1) {
        this.$message.error({
          dangerouslyUseHTMLString: true,
          message: "只能上传 <strong>txt</strong> 文件，且不超过1M"
        });
        params.onError();
        return;
      }

      const loadingInstance = Loading.service({
        lock: true,
        text: "上传解析中"
      });

      const formData = new FormData();
      formData.append("file", file);
      formData.append("batchId", this.batchId);
      this.$http({
        url: this.$http.adornUrl("/batch/uploadArticles"),
        method: "post",
        timeout: 0,
        headers: { "Content-Type": "multipart/form-data;" },
        data: formData
      }).then(({ data }) => {
        // sleep(3000).then(() => {
        loadingInstance.close();
        // });
        if (data.code === 0) {
          this.$message.success("后台导入中");
          this.getDataList();
        } else {
          this.$message.error(data.msg);
        }
        params.onSuccess();
      });
    },
    download(type, id) {
      this.$http({
        url: this.$http.adornUrl("/importLog/download"),
        params: this.$http.adornParams({
          type: type,
          id: id
        }),
        method: "get",
        responseType: "blob"
      })
        .then((resp) => {
          if (resp.headers["content-type"] !== "application/octet-stream") {
            const err = "错误日志下载失败";
            throw err;
          }
          FileSaver.saveAs(resp.data, decodeURI(resp.headers["filename"]));
        })
        .catch((err) => {
          this.$message.error(err);
        });
    },
    exportResult(id) {
      this.$http({
        url: this.$http.adornUrl("/batch/exportArticleResult"),
        method: "get",
        params: this.$http.adornParams({
          batchId: this.batchId,
          importLogId: id
        })
      })
        .then((resp) => {
          const blob = new Blob([resp.data], {
            type: "application/octet-stream"
          });
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement("a");
          a.href = url;
          a.download = `${id}-文章导入情况.csv`;
          a.click();
          this.logDialogVisible = false;
          this.$emit("refreshDataList");
        })
        .catch((err) => {
          this.$message.error(err);
          this.logDialogVisible = false;
          this.$emit("refreshDataList");
        });
    },
    reloadArticles: function (id) {
      this.loading = true;
      this.$http({
        url: this.$http.adornUrl("/batch/reloadArticles"),
        method: "get",
        params: this.$http.adornParams({
          batchId: this.batchId,
          importLogId: id
        })
      })
        .then(({ data }) => {
          this.loading = false;
          this.logDialogVisible = false;
          if (data && data.code === 0) {
            this.$message.success("重新导入中");
          } else {
            this.$message.error(data.msg);
          }
          this.$emit("refreshDataList");
        })
        .catch((err) => {
          this.loading = false;
          this.logDialogVisible = false;
          this.$message.error(err);
          this.$emit("refreshDataList");
        });
      this.getDataList();
    }
  }
};
</script>
<style scoped lang="scss">
.upload-content {
  display: inline-block;
}
.el-button [class*="el-icon-"] + span {
  margin-left: 5px !important;
}
</style>
