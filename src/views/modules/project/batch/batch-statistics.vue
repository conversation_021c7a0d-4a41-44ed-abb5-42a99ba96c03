<template>
  <div class="mod-home">
    <anno-statistics
      ref="annoStatistics"
      :project-id="projectId"
    ></anno-statistics>

    <auditor-statistics
      ref="auditorStatistics"
      :project-id="projectId"
    ></auditor-statistics>

    <calculation-consistency
      ref="calculationConsistency"
      :project-id="projectId"
    ></calculation-consistency>

    <div class="chart-bar">
      <!--标注准确率折线图-->
      <correct-rat-chart
        chart-id="correctRateChart"
        ref="correctRateChart"
      ></correct-rat-chart>

      <consistency-chart
        chart-id="consistencyChart"
        ref="consistencyChart"
      ></consistency-chart>
    </div>
  </div>
</template>

<script>
import annoStatistics from "@/views/modules/project/components/anno-statistics.vue";
import auditorStatistics from "@/views/modules/project/components/auditor-statistics.vue";
import CalculationConsistency from "@/views/modules/project/components/calculation-consistency.vue";
import CorrectRatChart from "@/views/modules/project/components/correct-rate-chart.vue";
import ConsistencyChart from "@/views/modules/project/components/consistency-chart.vue";

export default {
  props: {
    projectId: {
      required: true,
      type: String
    },
    batchId: {
      required: true,
      type: String
    }
  },
  name: "BatchStatistics",
  data() {
    return {};
  },
  components: {
    ConsistencyChart,
    CorrectRatChart,
    CalculationConsistency,
    annoStatistics,
    auditorStatistics
  },
  mounted() {
    this.$nextTick(() => {
      this.refreshData();
    });
  },
  methods: {
    refreshData() {
      this.$refs.annoStatistics.init(this.projectId, false, this.batchId);
      this.$refs.auditorStatistics.init(this.projectId, false, this.batchId);
      this.$refs.calculationConsistency.init(
        this.projectId,
        false,
        this.batchId
      );
      this.$refs.correctRateChart.init(this.projectId, this.batchId);
      this.$refs.consistencyChart.init(this.projectId, this.batchId);
    }
  }
};
</script>

<style scoped lang="scss">
.mod-home {
  line-height: 1.5;
}

.home-top,
.chart-bar {
  display: flex;
  justify-content: space-between;

  .item {
    padding: 10px 20px;
    flex-grow: 1;
    flex-shrink: 1;
    flex-basis: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    border-radius: 8px;

    &:hover {
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }

    div:first-child {
      margin-right: 35px;
      width: 55px;
      height: 55px;
      text-align: center;
      line-height: 63px;
      background-color: #579aff;
      border-radius: 8px;

      .icon-svg,
      i {
        font-size: 1.5rem;
        color: #fff;
      }
    }

    div:last-child {
      display: flex;
      flex-direction: column;
      text-align: right;

      span:first-child {
        font-size: 1.4rem;
        color: #182524;
        font-weight: 600;
      }

      span:last-child {
        color: #6b7373;
      }
    }
  }

  .item:not(:last-child),
  .el-card:not(:last-child) {
    margin-right: 10px;
  }

  .el-card {
    flex-grow: 1;
    flex-shrink: 1;
    flex-basis: 100%;

    ::v-deep .el-card__body {
      height: 100% !important;
    }
  }
}
</style>
