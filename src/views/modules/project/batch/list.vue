<template>
  <div>
    <div class="back-head">
      <a class="go-back" href="javascript:0;" @click="$router.go(-1)">
        <i class="el-icon-arrow-left"></i>返回
      </a>
      批次管理
    </div>

    <el-form :inline="true" :model="formInline" class="demo-form-inline">
      <el-form-item label="名称">
        <el-input
          v-model="formInline.name"
          style="width: 100px"
          clearable
          placeholder="名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="材料来源">
        <el-input
          v-model="formInline.materialSource"
          style="width: 110px"
          clearable
          placeholder="材料来源"
        ></el-input>
      </el-form-item>
      <el-form-item label="状态">
        <el-select
          v-model="formInline.status"
          class="status-width"
          clearable
          placeholder="状态"
        >
          <el-option label="正常" value="1"></el-option>
          <el-option label="禁用" value="0"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="创建时间">
        <el-date-picker
          v-model="formInline.date"
          end-placeholder="结束时间"
          range-separator="-"
          start-placeholder="开始时间"
          type="daterange"
          value-format="yyyy-MM-dd"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button
          icon="el-icon-search"
          @click="
            () => {
              this.pageIndex = 1;
              this.getDataList();
            }
          "
          >查询</el-button
        >
      </el-form-item>

      <el-form-item v-if="isAuth('project:editable')">
        <el-button
          icon="el-icon-plus"
          type="primary"
          @click="addOrUpdateHandle()"
          >新增</el-button
        >
      </el-form-item>
    </el-form>

    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    ></add-or-update>
    <upload-articles
      v-if="uploadArticlesVisible"
      ref="uploadArticles"
      @refreshDataList="getDataList"
    ></upload-articles>
    <upload-self-articles
      v-if="uploadArticleVisible"
      ref="uploadArticle"
      @refreshDataList="getDataList"
    >
    </upload-self-articles>

    <el-table
      v-loading="dataListLoading"
      :data="dataList"
      border
      :header-cell-style="{ background: '#F5F7FA' }"
      style="width: 100%; border-radius: 8px"
      :header-row-style="{ height: '44px' }"
    >
      <el-table-column
        align="center"
        header-align="center"
        label="ID"
        prop="batchId"
        width="50"
      >
      </el-table-column>
      <el-table-column
        align="center"
        header-align="center"
        label="项目名称"
        min-width="150"
        prop="projectName"
      >
      </el-table-column>
      <el-table-column
        align="center"
        header-align="center"
        label="批次名称"
        min-width="150"
        prop="name"
      >
      </el-table-column>
      <el-table-column
        align="center"
        header-align="center"
        label="材料来源"
        :show-overflow-tooltip="true"
        prop="materialSource"
      >
      </el-table-column>
      <el-table-column
        align="center"
        header-align="center"
        label="检索条件"
        :show-overflow-tooltip="true"
        prop="searchCriteria"
      >
      </el-table-column>
      <el-table-column
        align="center"
        header-align="center"
        label="描述"
        :show-overflow-tooltip="true"
        prop="description"
      >
      </el-table-column>
      <el-table-column
        align="center"
        header-align="center"
        label="创建时间"
        prop="createTime"
        width="160"
      >
        <template slot-scope="scope">
          <div>{{ scope.row.createTime | dateFrm }}</div>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        header-align="center"
        label="文书数"
        prop="totalArticle"
        width="70"
      >
      </el-table-column>
      <el-table-column
        align="center"
        header-align="center"
        label="状态"
        prop="status"
        width="80"
      >
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === 0" size="small" type="danger"
            >禁用</el-tag
          >
          <el-tag v-else size="small">正常</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        header-align="center"
        label="操作"
        width="150"
      >
        <template slot-scope="scope">
          <el-tooltip
            v-if="isAuth('project:editable')"
            content="数据导入"
            placement="top"
            effect="light"
          >
            <i
              class="el-icon-upload icon-primary icon-inner"
              @click="bnlpImport(scope.row)"
            ></i>
          </el-tooltip>
          <el-tooltip content="查看文书列表" placement="top" effect="light">
            <i
              class="el-icon-view icon-primary icon-inner ml-1"
              @click="viewArticle(scope.row.batchId)"
            ></i>
          </el-tooltip>
          <el-tooltip content="统计" placement="top" effect="light">
            <router-link
              :to="
                '/batch/statistics/' +
                scope.row.projectId +
                '/' +
                scope.row.batchId
              "
            >
              <i style="margin-right: 0" class="pointer">
                <icon-svg :name="'jindu'" class="icon-inner"></icon-svg>
              </i>
            </router-link>
          </el-tooltip>
          <el-tooltip
            v-if="isAuth('project:editable')"
            content="编辑"
            placement="top"
            effect="light"
          >
            <i
              class="el-icon-edit icon-primary"
              @click="addOrUpdateHandle(scope.row.batchId)"
            ></i>
          </el-tooltip>
          <el-tooltip
            v-if="isAuth('project:editable')"
            content="删除"
            placement="top"
            effect="light"
          >
            <i
              class="el-icon-delete icon-red"
              @click="deleteHandle(scope.row.batchId)"
            ></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page="pageIndex"
      :page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
    >
    </el-pagination>
    <article-list v-if="articleListVisible" ref="articleList" />
  </div>
</template>

<script>
import moment from "moment";
import AddOrUpdate from "./add-or-update";
import UploadArticles from "./upload-articles";
import ArticleList from "./article-list";
import UploadSelfArticles from "./upload-self-articles";

export default {
  name: "list",
  props: {
    projectId: {
      required: true
    }
  },
  data() {
    return {
      articleListVisible: false,
      dataList: [],
      pageIndex: 1,
      pageSize: 20,
      totalPage: 0,
      addOrUpdateVisible: false,
      dataListLoading: false,
      uploadArticlesVisible: false,
      uploadArticleVisible: false,
      formInline: {
        name: "",
        materialSource: "",
        status: "",
        date: []
      }
    };
  },
  filters: {
    dateFrm: function (value) {
      return moment(value).format("YYYY-MM-DD HH:mm:ss");
    }
  },
  components: {
    AddOrUpdate,
    UploadArticles,
    ArticleList,
    UploadSelfArticles
  },
  mounted() {
    if (!this.projectId) {
      this.$router.push({ name: "home" });
    } else {
      this.pageIndex = 1;
      this.getDataList();
    }
  },
  methods: {
    getDataList() {
      this.dataListLoading = true;
      this.$http({
        url: this.$http.adornUrl("/batch/list"),
        method: "get",
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          projectId: this.projectId || undefined,
          name: this.formInline.name,
          materialSource: this.formInline.materialSource,
          status: this.formInline.status,
          startDate:
            (this.formInline?.date != null && this.formInline?.date[0]) ||
            undefined,
          endDate:
            (this.formInline?.date != null && this.formInline?.date[1]) ||
            undefined
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list;
          this.totalPage = data.page.totalCount;
        } else {
          this.dataList = [];
          this.totalPage = 0;
        }
        this.dataListLoading = false;
      });
    },
    addOrUpdateHandle(batch) {
      this.addOrUpdateVisible = true;
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(this.projectId, batch);
      });
    },
    importArticlesHandle(batch) {
      this.uploadArticlesVisible = true;
      this.$nextTick(() => {
        this.$refs.uploadArticles.init("import", this.projectId, batch);
      });
    },
    bnlpImport(batch) {
      this.uploadArticleVisible = true;
      this.$nextTick(() => {
        this.$refs.uploadArticle.init(this.projectId, batch);
      });
    },
    importArticleResultHandle(batch) {
      this.uploadArticlesVisible = true;
      this.$nextTick(() => {
        this.$refs.uploadArticles.init("log", this.projectId, batch);
      });
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val;
      this.pageIndex = 1;
      this.getDataList();
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val;
      this.getDataList();
    },
    deleteHandle(id) {
      this.$confirm(
        "确认是否删除该批次？（耗时较长，请耐心等待）",
        "确认信息",
        {
          dangerouslyUseHTMLString: true,
          distinguishCancelAndClose: true,
          confirmButtonText: "确认",
          cancelButtonText: "取消"
        }
      ).then(() => {
        const loading = this.$loading({
          lock: true,
          text: `删除中`,
          spinner: "el-icon-loading",
          background: "rgba(0, 0, 0, 0.7)"
        });
        this.$http({
          url: this.$http.adornUrl("/batch/delete"),
          method: "get",
          timeout: 180000, // 3分钟
          params: this.$http.adornParams({
            batchId: id
          })
        })
          .then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                type: "warning",
                message: "批次已删除"
              });
            }
          })
          .finally(() => {
            this.getDataList();
            loading.close();
          });
      });
    },
    lookArticleList(batchId) {
      this.articleListVisible = true;
      this.$nextTick(() => {
        this.$refs.articleList.init(batchId);
      });
    },
    viewArticle(batchId) {
      this.$router.push({
        path: "/batch/article/" + batchId
      });
    }
  }
};
</script>
