<template>
  <div>
    <el-row>
      <el-col :span="2">
        <div class="back-head" style="margin: 10px auto">
          <a class="go-back" href="javascript:0;" @click="$router.go(-1)">
            <i class="el-icon-arrow-left"></i>返回
          </a>
        </div>
      </el-col>
      <el-col :span="22">
        <el-form :inline="true" :model="dataForm" ref="searchForm">
          <el-form-item prop="articleId">
            <el-input
              v-model="dataForm.articleId"
              placeholder="文书编号"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item prop="articleName">
            <el-input
              v-model="dataForm.articleName"
              placeholder="文书标题"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-select
              placeholder="标注员"
              style="width: 100px"
              v-model="dataForm.annotatorId"
              clearable
            >
              <div
                v-for="(value, key) in annotatorList"
                :key="'annotator-select-' + key"
              >
                <el-option
                  :label="value.username"
                  :value="value.userId"
                ></el-option>
              </div>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select
              placeholder="审核员"
              style="width: 100px"
              v-model="dataForm.auditorId"
              clearable
            >
              <div
                v-for="(value, key) in auditorList"
                :key="'auditor-select-' + key"
              >
                <el-option
                  :label="value.username"
                  :value="value.userId"
                ></el-option>
              </div>
              <el-option label="自动审核" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select
              placeholder="废弃"
              style="width: 80px"
              v-model="dataForm.invalid"
              clearable
            >
              <el-option label="是" :value="1"></el-option>
              <el-option label="否" :value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="date">
            <el-date-picker
              v-model="dataForm.date"
              value-format="yyyy-MM-dd"
              style="width: 300px"
              type="daterange"
              range-separator="-"
              start-placeholder="更新开始时间"
              end-placeholder="更新结束时间"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button @click="getDataList" icon="el-icon-search"
              >查询</el-button
            >
            <el-button
              @click="resetForm"
              plain
              type="info"
              icon="el-icon-refresh-left"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <el-row>
      <el-tabs v-model="activeStep" @tab-click="handleClick">
        <el-tab-pane label="全部" name="all"></el-tab-pane>
        <el-tab-pane label="待标注" name="unmarked"></el-tab-pane>
        <el-tab-pane label="标注中" name="noting"></el-tab-pane>
        <el-tab-pane label="待审核" name="marked"></el-tab-pane>
        <el-tab-pane label="审核中" name="reviewing"></el-tab-pane>
        <el-tab-pane label="已验收" name="reviewed"></el-tab-pane>
      </el-tabs>
    </el-row>
    <el-row>
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        @sort-change="sortChange"
        @selection-change="selectionChangeHandle"
        style="width: 100%; border-radius: 8px"
        :header-row-style="{ height: '44px' }"
        :header-cell-style="{
          background: 'rgb(246 247 249)',
          padding: '0px'
        }"
      >
        <el-table-column
          type="selection"
          header-align="center"
          align="center"
          width="50"
        >
        </el-table-column>
        <el-table-column
          width="200"
          prop="articleId"
          sortable="custom"
          :show-overflow-tooltip="true"
          label="文书编号"
        ></el-table-column>
        <el-table-column
          prop="articleName"
          sortable="custom"
          :show-overflow-tooltip="true"
          label="文书标题"
        ></el-table-column>
        <el-table-column width="55" prop="step" align="center" label="废弃">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.invalid === 1" size="small" type="danger"
              >是
            </el-tag>
            <el-tag size="small" type="info" v-else>否</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          min-width="50"
          prop="annotators"
          align="center"
          :show-overflow-tooltip="true"
          label="标注员"
        ></el-table-column>
        <el-table-column
          width="100"
          prop="auditor"
          align="center"
          label="审核员"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          width="100"
          prop="step"
          align="center"
          sortable="custom"
          label="状态"
        >
          <template slot-scope="scope">
            <el-tag v-if="scope.row.step === 0" size="small" type="info"
              >待标注
            </el-tag>
            <el-tag
              v-else-if="scope.row.step === 1 || scope.row.step === 5"
              size="small"
              type="primary"
              >标注中
            </el-tag>
            <el-tag
              v-else-if="scope.row.step === 2 || scope.row.step === 6"
              size="small"
              type="warning"
              >待审核
            </el-tag>
            <el-tag v-else-if="scope.row.step === 3" size="small" type="danger"
              >审核中
            </el-tag>
            <el-tag v-else-if="scope.row.step === 4" size="small" type="success"
              >已验收
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          width="155"
          :formatter="updateDateTime"
          prop="updateTime"
          label="更新时间"
          sortable="custom"
          align="center"
        ></el-table-column>
        <el-table-column width="100" label="操作" align="left">
          <template slot-scope="scope">
            <el-tooltip content="查看" placement="top" effect="light">
              <i
                class="el-icon-view icon-primary"
                @click="toTaskDetail(scope.row.noteId)"
              >
              </i>
            </el-tooltip>
            <el-tooltip
              v-if="isAuth('project:editable')"
              content="重置"
              placement="top"
              effect="light"
            >
              <i
                class="el-icon-refresh-left icon-red"
                @click="deleteHandle(0, scope.row.noteId)"
              >
              </i>
            </el-tooltip>
            <el-tooltip
              v-if="isAuth('project:editable')"
              content="删除"
              placement="top"
              effect="light"
            >
              <i
                class="el-icon-delete icon-red"
                @click="deleteHandle(1, scope.row.noteId)"
              ></i>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <div class="d-flex align-items-center mt-1">
        <div v-show="tableData.length > 0">
          <el-button
            v-if="isAuth('project:editable')"
            type="warning"
            size="small"
            icon="el-icon-edit"
            :disabled="dataListSelections.length <= 0"
            @click="deleteHandle(0)"
            >批量重置
          </el-button>
          <el-button
            v-if="isAuth('project:editable')"
            type="danger"
            size="small"
            icon="el-icon-delete"
            :disabled="dataListSelections.length <= 0"
            @click="deleteHandle(1)"
            >批量删除
          </el-button>
          <el-button
            v-if="activeStep === 'noting' && isAuth('project:editable')"
            type="danger"
            size="small"
            icon="el-icon-s-promotion"
            :disabled="dataListSelections.length <= 0"
            @click="batchSubmit()"
            >批量强制提交
          </el-button>
        </div>
        <div class="ml-auto pb-1">
          <el-pagination
            @size-change="sizeChangeHandle"
            @current-change="currentChangeHandle"
            :current-page="pageIndex"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            :total="totalPage"
            layout="total, sizes, prev, pager, next, jumper"
          >
          </el-pagination>
        </div>
      </div>
    </el-row>
  </div>
</template>

<script>
import { Loading } from "element-ui";
import { FromTypeEnum, setPageIds } from "@/utils/pageIdUtil";

export default {
  props: {
    batchId: {
      required: true
    }
  },
  data() {
    return {
      fromType: FromTypeEnum.article_list_of_batch,
      loading: false,
      annotatorList: [],
      auditorList: [],
      dataForm: {
        articleId: "",
        articleName: "",
        date: [],
        annotatorId: undefined,
        auditorId: undefined,
        invalid: undefined,
        orderBy: undefined,
        isAsc: undefined
      },
      activeStep: "all",
      tableData: [],
      dataListSelections: [],
      pageIndex: 1,
      pageSize: 20,
      totalPage: 0
    };
  },
  computed: {
    roleId() {
      return this.$store.state.user.roleId;
    }
  },
  created() {
    this.init();
  },
  mounted() {
    this.init();
    this.getAnnotatorList();
    this.getAuditorList();
  },
  methods: {
    getAnnotatorList: async function () {
      await this.$http({
        url: this.$http.adornUrl("/task/queryUserList"),
        method: "get",
        params: this.$http.adornParams({
          batchId: this.batchId,
          roleId: this.$RoleEnum.annotator
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.annotatorList = data.data;
        }
      });
    },
    getAuditorList: async function () {
      await this.$http({
        url: this.$http.adornUrl("/task/queryUserList"),
        method: "get",
        params: this.$http.adornParams({
          batchId: this.batchId,
          roleId: this.$RoleEnum.auditor
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.auditorList = data.data;
        }
      });
    },
    toTaskDetail(noteId) {
      this.$router.push({
        path: "/annotask/detail",
        query: {
          batchId: Number(this.batchId),
          noteId: Number(noteId),
          editable: false,
          annotatorId: this.dataForm.annotatorId,
          fromType: this.fromType
        }
      });
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val;
      this.pageIndex = 1;
      this.getDataList();
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val;
      this.getDataList();
    },
    init() {
      const query = sessionStorage.getItem(
        this.userId + "-" + this.batchId + "-batchArticleQueryList"
      );
      if (!query) {
        this.getDataList();
        return;
      }
      const params = JSON.parse(query);
      this.pageIndex = params.page;
      this.pageSize = params.limit;
      this.dataForm.orderBy = params.orderBy;
      this.dataForm.isAsc = params.isAsc;
      this.activeStep = params.activeStep;
      this.dataForm.invalid = params.invalid;
      this.dataForm.annotatorId = params.annotatorId;
      this.dataForm.auditorId = params.auditorId;
      this.dataForm.articleId = params.articleId;
      this.dataForm.articleName = params.articleName;
      if (params.startDate && params.endDate) {
        this.dataForm.date = [];
        this.dataForm.date[0] = params.startDate;
        this.dataForm.date[1] = params.endDate;
      }
      this.getDataList();
    },
    // 查询表格
    getDataList() {
      this.loading = true;
      const params = {
        page: this.pageIndex,
        limit: this.pageSize,
        orderBy: this.dataForm.orderBy,
        isAsc: this.dataForm.isAsc,
        roleId: this.roleId,
        batchId: Number(this.batchId),
        annotatorId: this.dataForm.annotatorId,
        auditorId: this.dataForm.auditorId,
        activeStep: this.activeStep,
        invalid: this.dataForm.invalid,
        articleId: this.dataForm.articleId,
        articleName: this.dataForm.articleName,
        startDate:
          (this.dataForm?.date != null && this.dataForm?.date[0]) || undefined,
        endDate:
          (this.dataForm?.date != null && this.dataForm?.date[1]) || undefined
      };
      // 把用户的查询条件缓存，方便使用
      sessionStorage.setItem(
        this.userId + "-" + this.batchId + "-batchArticleQueryList",
        JSON.stringify(params)
      );
      this.$http({
        url: this.$http.adornUrl("/batch/getArticleList"),
        method: "get",
        params: this.$http.adornParams(params)
      })
        .then(({ data }) => {
          let pageIdsData = null;
          if (data && data.code === 0) {
            let result = data.data;
            this.tableData = result.listData.list || [];
            this.totalPage = result.listData.totalCount;
            pageIdsData = result.pageIdsVOList;
          } else {
            this.dataList = [];
            this.totalPage = 0;
          }
          setPageIds(this.fromType, pageIdsData);
        })
        .catch(() => {
          setPageIds(this.fromType, null);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 删除/重置
    deleteHandle(deleted, id) {
      const ids = this.dataListSelections.map((item) => {
        return item.noteId;
      });
      this.$confirm(
        `确定进行【${id ? "" : "批量"} ${deleted ? "删除" : "重置"}】操作?`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      )
        .then(() => {
          const loadingInstance = Loading.service();
          this.$http({
            url: this.$http.adornUrl("/batch/resetArticle/" + deleted),
            method: "post",
            timeout: 300000, // 5分钟
            data: this.$http.adornData(id ? [id] : ids, false)
          })
            .then(({ data }) => {
              if (data && data.code === 0) {
                this.$message({
                  message: "操作成功",
                  type: "success",
                  duration: 3000
                });
              } else {
                this.$message.error(data.msg);
              }
              this.getDataList();
            })
            .finally(() => {
              loadingInstance.close();
            });
        })
        .catch(() => {});
    },
    // 强制提交
    batchSubmit() {
      const ids = this.dataListSelections.map((item) => {
        return item.noteId;
      });
      this.$confirm(`确定进行批量强制提交操作?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          const loadingInstance = Loading.service();
          this.$http({
            url: this.$http.adornUrl("/batch/batchSubmit"),
            method: "post",
            timeout: 300000, // 5分钟
            data: this.$http.adornData(ids, false)
          })
            .then(({ data }) => {
              if (data && data.code === 0) {
                this.$message({
                  message: "操作成功",
                  type: "success",
                  duration: 3000
                });
              } else {
                this.$message.error(data.msg);
              }
              this.getDataList();
            })
            .finally(() => {
              loadingInstance.close();
            });
        })
        .catch(() => {});
    },
    sortChange(column) {
      if (column.order === "descending") {
        this.dataForm.orderBy = column.prop;
        this.dataForm.isAsc = false;
      } else {
        this.dataForm.orderBy = column.prop;
        this.dataForm.isAsc = true;
      }
      // 什么排序都不选择，恢复默认
      if (column.order == null) {
        this.dataForm.orderBy = undefined;
        this.dataForm.isAsc = undefined;
      }
      this.pageIndex = 1;
      this.getDataList();
    },
    // tabs 标签页被点击
    handleClick(tab) {
      this.activeStep = tab.name;
      this.pageIndex = 1;
      this.getDataList();
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val;
    },
    // 定制化日期显示
    updateDateTime(row, column) {
      return this.$tools.updateDateTime(row, column);
    },
    resetForm() {
      this.$refs.searchForm.resetFields();
      this.dataForm.date = [];
      this.dataForm.orderBy = undefined;
      this.dataForm.isAsc = undefined;
      this.dataForm.invalid = undefined;
      this.dataForm.annotatorId = undefined;
      this.dataForm.auditorId = undefined;
      this.dataForm.articleId = "";
      this.dataForm.articleName = "";
      this.getDataList();
    }
  }
};
</script>

<style>
.el-tree-node__label {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 15em;
}
</style>
