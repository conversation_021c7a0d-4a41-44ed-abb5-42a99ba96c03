<template>
  <el-dialog
    :title="!dataForm.batchId ? '新增' : '编辑'"
    :visible.sync="visible"
    width="30%"
  >
    <el-form
      :model="dataForm"
      :rules="this.dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="80px"
    >
      <el-form-item prop="projectId" v-show="false">
        <el-input v-model="dataForm.projectId"></el-input>
      </el-form-item>
      <el-form-item prop="batchId">
        <el-input v-model="dataForm.batchId" v-show="false"></el-input>
      </el-form-item>

      <el-form-item label="批次名称" prop="name">
        <el-input
          v-model="dataForm.name"
          placeholder="批次名称"
          maxlength="10"
        ></el-input>
      </el-form-item>
      <el-form-item label="材料来源" prop="materialSource">
        <el-input
          v-model="dataForm.materialSource"
          placeholder="材料来源"
          maxlength="255"
        ></el-input>
      </el-form-item>
      <el-form-item label="检索条件" prop="searchCriteria">
        <el-input
          v-model="dataForm.searchCriteria"
          maxlength="255"
          placeholder="检索条件"
        ></el-input>
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input
          v-model="dataForm.description"
          type="textarea"
          maxlength="255"
          placeholder="描述"
        ></el-input>
      </el-form-item>

      <el-form-item label="分配模式" size="mini" prop="mode">
        <el-radio-group v-model="dataForm.mode">
          <el-radio :label="0">按劳分配</el-radio>
          <el-radio :label="1">
            均衡分配
            <el-tooltip
              class="item"
              effect="dark"
              content="说明：按照文书数、标注轮数和标注员数等平均分配标注任务。注意：导入文书后，系统就会自动分配任务。请提前设置好标注员。"
              placement="top"
              ><i class="el-icon-question"></i>
            </el-tooltip>
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item
        label="自由标注"
        size="mini"
        prop="free"
        v-show="dataForm.mode === 0"
      >
        <el-radio-group v-model="dataForm.free">
          <el-radio :label="0">禁用</el-radio>
          <el-radio :label="1">启用</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="状态" size="mini" prop="status">
        <el-radio-group v-model="dataForm.status">
          <el-radio :label="1">正常</el-radio>
          <el-radio :label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { Loading } from "element-ui";

export default {
  name: "add-or-update",
  data() {
    const checkBatchName = (rule, value, callback) => {
      if (!value) {
        return callback(new Error("名称不能为空"));
      }
      this.$http({
        url: this.$http.adornUrl("/batch/validate/batchName"),
        method: "get",
        params: this.$http.adornParams({
          batchName: value,
          batchId: this.dataForm.batchId || undefined,
          projectId: this.dataForm.projectId || undefined
        })
      })
        .then(({ data }) => {
          if (data && data.code !== 0) {
            callback(new Error(data.msg));
          }
          callback();
        })
        .catch((error) => callback(new Error(error)));
    };

    return {
      visible: false,
      dataForm: {
        projectId: 0,
        batchId: 0,
        name: "",
        materialSource: "",
        searchCriteria: "",
        description: "",
        free: 0,
        mode: 0,
        status: 1,
        labelIds: []
      },
      dataRule: {
        name: [{ required: true, validator: checkBatchName, trigger: "blur" }]
        // materialSource: [
        //   { required: true, message: "材料来源不能为空", trigger: "blur" }
        // ],
        // searchCriteria: [
        //   { required: true, message: "检索条件不能为空", trigger: "blur" }
        // ]
      }
    };
  },
  methods: {
    init(projectId, batchId) {
      if (!projectId) {
        this.$router.push({ name: "home" });
      }
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.dataForm.resetFields();

        this.dataForm.projectId = projectId;
        this.dataForm.batchId = batchId;
        this.$http({
          url: this.$http.adornUrl("/batch/info"),
          method: "get",
          params: this.$http.adornParams({
            batchId: batchId
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            if (data.batch) {
              this.dataForm.projectId = data.batch.projectId;
              this.dataForm.batchId = data.batch.batchId;
              this.dataForm.name = data.batch.name;
              this.dataForm.materialSource = data.batch.materialSource;
              this.dataForm.searchCriteria = data.batch.searchCriteria;
              this.dataForm.description = data.batch.description;
              this.dataForm.free = data.batch.free;
              this.dataForm.mode = data.batch.mode;
              this.dataForm.status = data.batch.status;
              this.dataForm.labelIds = data.batch.labelIds;
            }
          }
        });
      });
    },
    dataFormSubmit() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          const loadingInstance = Loading.service();
          this.$http({
            url: this.$http.adornUrl("/batch/save"),
            method: "post",
            data: this.$http.adornData({
              projectId: this.dataForm.projectId,
              batchId: this.dataForm.batchId || undefined,
              name: this.dataForm.name,
              materialSource: this.dataForm.materialSource,
              searchCriteria: this.dataForm.searchCriteria,
              description: this.dataForm.description,
              free: this.dataForm.free,
              mode: this.dataForm.mode,
              status: this.dataForm.status
            })
          })
            .then(({ data }) => {
              this.visible = false;
              if (data && data.code === 0) {
                this.$message({
                  message: "操作成功",
                  type: "success"
                });
              } else {
                this.$message.error(data.msg);
              }
              this.$emit("refreshDataList");
              loadingInstance.close();
            })
            .catch((e) => {
              this.visible = false;
              loadingInstance.close();
              this.$message.error(e);
            });
        }
      });
    }
  }
};
</script>

<style scoped>
.multiple—select {
  width: 100%;
}
</style>
