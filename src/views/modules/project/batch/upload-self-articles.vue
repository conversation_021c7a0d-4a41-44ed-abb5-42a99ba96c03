<template>
  <el-dialog
    title="数据导入"
    :visible.sync="dialogVisible"
    @close="refreshDataList"
    width="80%"
  >
    <el-tabs type="border-card">
      <el-tab-pane label="自有数据导入">
        <upload-json ref="uploadJson"></upload-json>
      </el-tab-pane>
      <el-tab-pane label="BFMS数据导入">
        <upload-txt ref="uploadTxt"></upload-txt>
      </el-tab-pane>
    </el-tabs>
  </el-dialog>
</template>

<script>
import UploadJson from "./upload-json";
import UploadTxt from "@/views/modules/project/batch/upload-txt";

export default {
  name: "upload-self-articles",
  data() {
    return {
      dialogVisible: false,
      tableData: [],
      page: 1,
      size: 10,
      type: 1,
      tableLoading: false,
      pageSizes: [10, 20, 50, 100] //可选择的一页多少条
    };
  },
  components: {
    UploadTxt,
    UploadJson
  },
  computed: {
    editable: function () {
      return this.isAuth("project:editable");
    },
    total() {
      return this.tableData.length;
    }
  },
  methods: {
    init(projectId, batch) {
      this.loading = false;
      this.projectId = projectId;
      this.batch = batch;
      this.result = [];
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.uploadJson.init(this.batch.batchId);
        this.$refs.uploadTxt.init(this.batch.batchId);
      });
    },
    refreshDataList() {
      this.$emit("refreshDataList");
      this.$nextTick(() => {
        this.$refs.uploadJson.stopThread();
        this.$refs.uploadTxt.stopThread();
      });
    }
  }
};
</script>

<style scoped lang="scss">
::v-deep .el-tabs--border-card {
  box-shadow: none;
  border-radius: 8px;
}
::v-deep.el-tabs--border-card > .el-tabs__header {
  border-bottom: none;
  ::v-deep.el-tabs__item.is-active {
    border: none !important;
  }
}
::v-deep.el-tabs--border-card > .el-tabs__header .el-tabs__item {
  border: none;
  &.is-active {
    border: none !important;
  }
}
::v-deep .el-tabs__header .el-tabs__nav-wrap,
::v-deep.el-tabs--border-card > .el-tabs__header,
::v-deep.el-tabs--border-card {
  border-radius: 8px !important;
}
</style>
