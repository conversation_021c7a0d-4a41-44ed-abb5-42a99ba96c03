<template>
  <div>
    <el-dialog
      :visible.sync="importDialogVisible"
      title="导入文书"
      width="20%"
      @closed="closedDialog"
    >
      <el-upload
        ref="upload"
        :http-request="uploadArticles"
        :limit="1"
        :multiple="false"
        :show-file-list="true"
        accept="*.txt"
        action=""
        class="upload-content"
        style="text-align: center"
      >
        <el-button style="width: 200px" type="primary">点击上传</el-button>
        <p slot="tip" class="el-upload__tip">
          只能上传 <strong>txt</strong> 文件，且不超过1M
        </p>
      </el-upload>
    </el-dialog>

    <el-dialog
      :visible.sync="logDialogVisible"
      title="导入情况"
      width="20%"
      v-loading="loading"
    >
      <el-row>
        <el-col class="log-item">{{ `导入总条数：${result.length}` }}</el-col>
      </el-row>
      <el-row>
        <el-col class="log-item">{{
          `导入成功条数：${successArticles.length}`
        }}</el-col>
      </el-row>
      <el-row>
        <el-col class="log-item">{{
          `导入失败条数：${failArticles.length}`
        }}</el-col>
      </el-row>
      <el-row>
        <el-col class="log-item">{{
          `重复导入条数：${repeatArticles.length}`
        }}</el-col>
      </el-row>
      <el-row>
        <el-col class="log-item">{{
          `待导入条数：${waitArticles.length}`
        }}</el-col>
      </el-row>

      <el-row
        type="flex"
        class="row-bg"
        justify="space-between"
        style="margin-top: 20px"
      >
        <el-col :span="8">
          <el-button size="small" type="primary" @click="exportResult(batchId)"
            >导出日志</el-button
          >
        </el-col>
        <el-col :span="6">
          <el-button
            size="small"
            type="success"
            @click="uploadArticlesResult(batchId)"
            >刷新</el-button
          >
        </el-col>
        <el-col :span="8">
          <el-button
            size="small"
            v-if="isAuth('project:editable')"
            type="danger"
            @click="reloadArticles(batchId)"
            :disabled="failArticles.length === 0"
            >重新导入
          </el-button>
        </el-col>
      </el-row>
    </el-dialog>
  </div>
</template>

<script>
import { Loading } from "element-ui";

export default {
  data() {
    return {
      importDialogVisible: false,
      logDialogVisible: false,
      loadVisible: false,
      resultVisible: false,
      loading: false,
      title: "",
      batchId: 0,
      projectId: 0,
      result: [],
      batch: {}
    };
  },
  computed: {
    successArticles: function () {
      return this.result.filter((it) => it.status === 0);
    },
    repeatArticles: function () {
      return this.result.filter((it) => it.status === 1);
    },
    failArticles: function () {
      return this.result.filter((it) => it.status === 2);
    },
    waitArticles: function () {
      return this.result.filter((it) => it.status === -1);
    }
  },
  methods: {
    init(active, projectId, batch) {
      this.loading = false;
      this.projectId = projectId;
      this.batch = batch;
      this.batchId = batch && batch.batchId;
      this.result = [];
      if (active === "import") {
        this.importDialogVisible = true;
      } else {
        this.logDialogVisible = true;
        this.uploadArticlesResult(this.batchId);
      }
    },
    uploadArticles: function (params) {
      this.$refs.upload.clearFiles();
      const file = params.file;
      if (!file.name.endsWith(".txt") || file.size / 1024 / 1024 > 1) {
        this.$message.error({
          dangerouslyUseHTMLString: true,
          message: "只能上传 <strong>txt</strong> 文件，且不超过1M"
        });
        params.onError();
        return;
      }

      const loadingInstance = Loading.service({
        lock: true,
        text: "上传解析中"
      });

      const formData = new FormData();
      formData.append("file", file);
      formData.append("batchId", this.batchId);
      this.$http({
        url: this.$http.adornUrl("/batch/loadArticles"),
        method: "post",
        timeout: 0,
        headers: { "Content-Type": "multipart/form-data;" },
        data: formData
      }).then(({ data }) => {
        loadingInstance.close();
        this.importDialogVisible = false;
        if (data.code === 0) {
          this.$message.success("后台导入中");
        } else {
          this.$message.error(data.msg);
        }
        this.$emit("refreshDataList");
        params.onSuccess();
      });
    },
    uploadArticlesResult: function (batchId) {
      this.$http({
        url: this.$http.adornUrl("/batch/loadArticleResult"),
        method: "get",
        params: this.$http.adornParams({
          batchId: batchId
        })
      })
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.result = data.result;
          } else {
            this.$message.error(data.msg);
            this.logDialogVisible = false;
          }
          this.$emit("refreshDataList");
        })
        .catch((err) => {
          this.logDialogVisible = false;
          this.$message.error(err);
          this.$emit("refreshDataList");
        });
    },
    reloadArticles: function (batchId) {
      this.loading = true;
      this.$http({
        url: this.$http.adornUrl("/batch/reloadArticles"),
        method: "get",
        params: this.$http.adornParams({
          batchId: batchId
        })
      })
        .then(({ data }) => {
          this.loading = false;
          this.logDialogVisible = false;
          if (data && data.code === 0) {
            this.$message.success("重新导入中");
          } else {
            this.$message.error(data.msg);
          }
          this.$emit("refreshDataList");
        })
        .catch((err) => {
          this.loading = false;
          this.logDialogVisible = false;
          this.$message.error(err);
          this.$emit("refreshDataList");
        });
    },
    exportResult(batchId) {
      this.$http({
        url: this.$http.adornUrl("/batch/exportArticleResult"),
        method: "get",
        params: this.$http.adornParams({
          batchId: batchId
        })
      })
        .then((resp) => {
          const blob = new Blob([resp.data], {
            type: "application/octet-stream"
          });
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement("a");
          a.href = url;
          a.download = `${this.batch.name}-文章导入情况.csv`;
          a.click();
          this.logDialogVisible = false;
          this.$emit("refreshDataList");
        })
        .catch((err) => {
          this.$message.error(err);
          this.logDialogVisible = false;
          this.$emit("refreshDataList");
        });
    },
    closedDialog() {
      this.$emit("refreshDataList");
    }
  }
};
</script>

<style scoped>
.el-upload--text {
  width: 100%;
}

.log-item {
  margin-bottom: 10px;
}
</style>
