<template>
  <div>
    <el-row type="flex" class="tab-pane">
      <el-col :span="20" class="pane-upload">
        <el-upload
          ref="upload"
          :http-request="uploadArticles"
          :limit="1"
          :multiple="false"
          :show-file-list="true"
          accept=".json,.zip"
          action=""
          class="upload-content"
        >
          <el-button
            size="mini"
            class="upload-btn"
            type="primary"
            icon="el-icon-upload2"
            >上传文件
          </el-button>
        </el-upload>
        <el-button
          @click.prevent="
            downloadTemplate(
              'article-json-template.json',
              '自有文章批量上传--模板.json'
            )
          "
          icon="el-icon-download"
          type="primary"
          class="download-btn"
          >下载JSON模板
        </el-button>
        <el-button
          @click.prevent="
            downloadTemplate(
              'article-zip-template.zip',
              '自有文章批量上传--模板.zip'
            )
          "
          icon="el-icon-download"
          type="primary"
          class="download-btn"
          >下载ZIP模板
        </el-button>
        <!--        <a-->
        <!--          href="javascript:void(0);"-->
        <!--          @click.prevent="downloadTemplate"-->
        <!--          type="primary"-->
        <!--          class="download-btn"-->
        <!--        >-->
        <!--          <i class="el-icon-download ml-1"></i>下载模板-->
        <!--        </a>-->
        <span class="el-upload__tip"
          >支持上传大小不超过100M，格式为json或zip的文件</span
        >
      </el-col>
      <el-col :span="4">
        <el-tooltip
          class="item"
          effect="light"
          content="刷新"
          placement="top-start"
        >
          <el-button
            size="small"
            class="refresh-btn"
            style="float: right"
            icon="el-icon-refresh"
            @click="getDataList()"
            circle
          ></el-button>
        </el-tooltip>

        <!--        <el-button-->
        <!--          style="float: right"-->
        <!--          size="small"-->
        <!--          type="success"-->
        <!--          icon="el-icon-refresh-right"-->
        <!--          @click="getDataList()"-->
        <!--          >刷新-->
        <!--        </el-button>-->
      </el-col>
    </el-row>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      :row-style="{ height: '30px' }"
      :header-cell-style="{ background: '#F5F7FA' }"
      style="width: 100%"
      type="expand"
    >
      <el-table-column
        label="文件"
        prop="name"
        min-width="200px"
        header-align="center"
        align="center"
      >
        <template slot-scope="scope">
          <a
            href="javascript:void(0);"
            @click="download('source', scope.row.id)"
            >{{ scope.row.name }}</a
          >
        </template>
      </el-table-column>
      <el-table-column min-width="100px" align="center" label="记录数">
        <template slot-scope="scope">
          {{ scope.row.data }}
        </template>
      </el-table-column>
      <el-table-column
        min-width="200px"
        align="center"
        label="上传时间"
        prop="createTime"
      ></el-table-column>
      <el-table-column
        min-width="100px"
        align="center"
        label="导入状态"
        prop="status"
      >
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === -1" size="small" type="danger"
            >导入失败
          </el-tag>
          <el-tag v-if="scope.row.status === 0" size="small">待导入</el-tag>
          <el-tag v-if="scope.row.status === 1" size="small">导入中</el-tag>
          <el-tag v-if="scope.row.status === 2" size="small" type="success"
            >导入完成
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        v-if="editable"
        label="日志"
        prop="alteration"
        width="200px"
        min-width="100px"
      >
        <template slot-scope="scope">
          <a
            v-if="scope.row.status === -1"
            href="javascript:void(0);"
            @click="download('log', scope.row.id)"
            ><i class="el-icon-download"></i>导出日志</a
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    >
    </el-pagination>
  </div>
</template>

<script>
import { Loading } from "element-ui";
import FileSaver from "file-saver";
import Thread from "@/utils/thread";

export default {
  name: "upload-json",
  data() {
    return {
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      type: this.$ImportType.article_bnlp,
      batchId: 0,
      thread: new Thread({
        start: () => {
          this.getDataList();
        },
        stop: () => {
          console.log("结束轮询");
        },
        time: 3000
      }),
      dataListLoading: false
    };
  },
  mounted() {},
  computed: {
    editable: function () {
      return this.isAuth("project:editable");
    }
  },
  methods: {
    stopThread() {
      this.thread.stop();
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val;
      this.pageIndex = 1;
      this.getDataList();
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val;
      this.getDataList();
    },
    init(batchId) {
      this.batchId = batchId;
      this.$nextTick(() => {
        this.getDataList();
        this.thread.run();
      });
    },
    getDataList() {
      this.dataListLoading = false;
      this.$http({
        url: this.$http.adornUrl("/importLog/list"),
        method: "get",
        params: this.$http.adornParams({
          batchId: this.batchId,
          type: this.type,
          page: this.pageIndex,
          limit: this.pageSize
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list;
          this.totalPage = data.page.totalCount;
        } else {
          this.dataList = [];
          this.totalPage = 0;
        }
        this.dataListLoading = false;
      });
    },
    uploadArticles: function (params) {
      this.$refs.upload.clearFiles();
      const file = params.file;
      const isJson = /\.json$/.test(file.name);
      const isZip = /\.zip$/.test(file.name);
      if ((!isJson && !isZip) || file.size / 1024 / 1024 > 100) {
        this.$message.error({
          dangerouslyUseHTMLString: true,
          message:
            "只能上传 <strong>json</strong> 或 <strong>zip</strong> 文件，且不超过100M"
        });
        params.onError();
        return;
      }
      const loadingInstance = Loading.service({
        lock: true,
        text: "上传解析中"
      });

      const formData = new FormData();
      formData.append("file", file);
      formData.append("batchId", this.batchId);
      this.$http({
        url: this.$http.adornUrl("/batch/uploadArticles"),
        method: "post",
        timeout: 0,
        headers: { "Content-Type": "multipart/form-data;" },
        data: formData
      }).then(({ data }) => {
        // sleep(3000).then(() => {
        loadingInstance.close();
        // });
        if (data.code === 0) {
          this.$message.success("后台导入中");
          this.getDataList();
        } else {
          this.$message.error(data.msg);
        }
        params.onSuccess();
      });
    },
    downloadTemplate(template_name, download_name) {
      this.$downloadTemplate(template_name, download_name);
    },
    download(type, id) {
      this.$http({
        url: this.$http.adornUrl("/importLog/download"),
        params: this.$http.adornParams({
          type: type,
          id: id
        }),
        method: "get",
        responseType: "blob"
      })
        .then((resp) => {
          if (resp.headers["content-type"] !== "application/octet-stream") {
            throw "错误日志下载失败";
          }
          FileSaver.saveAs(resp.data, decodeURI(resp.headers["filename"]));
        })
        .catch((err) => {
          this.$message.error(err);
        });
    }
  }
};
</script>

<style scoped lang="scss">
.upload-content {
  display: inline-block;
}

.el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
  border-bottom-color: #e4e7ed;
}

.el-button [class*="el-icon-"] + span {
  margin-left: 5px !important;
}
</style>
