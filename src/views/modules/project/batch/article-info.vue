<template>
  <CommAnnotation ref="commAnno" :noteId="noteId"></CommAnnotation>
</template>

<script>
import CommAnnotation from "../../annotation/comm-annotation";

export default {
  name: "ArticleInfo",
  data() {
    return {
      noteId: 0
    };
  },
  computed: {},
  mounted() {
    if (
      this.$route.params.noteId &&
      this.$route.params.noteId !== this.noteId
    ) {
      this.visible = "加载中...";
      this.noteId = this.$route.params.noteId;
      this.visible = "";
      this.$nextTick(() => {
        this.$refs.commAnno.getArticle();
      });
    }
  },
  methods: {},
  components: { CommAnnotation }
};
</script>

<style scoped></style>
