<template>
  <div>
    <el-row type="flex" class="tab-pane">
      <el-col :span="20" class="pane-upload">
        <el-upload
          ref="upload"
          :http-request="uploadArticles"
          :limit="1"
          :multiple="false"
          :show-file-list="true"
          accept=".json"
          action=""
          class="upload-content"
        >
          <el-button
            icon="el-icon-upload2"
            size="mini"
            type="primary"
            class="upload-btn"
            >上传文件
          </el-button>
        </el-upload>
        <el-button
          @click.prevent="downloadTemplate"
          icon="el-icon-download"
          type="primary"
          class="download-btn"
          >下载模板
        </el-button>
        <!--        <a-->
        <!--          href="javascript:void(0);"-->
        <!--          @click.prevent="downloadTemplate"-->
        <!--          type="primary"-->
        <!--          class="download-btn"-->
        <!--        >-->
        <!--          <i class="el-icon-download ml-1"></i>下载模板-->
        <!--        </a>-->
        <span class="el-upload__tip">只能上传json文件，且不超过30M</span>
      </el-col>
      <el-col :span="4">
        <el-tooltip
          class="item"
          effect="light"
          content="刷新"
          placement="top-start"
        >
          <el-button
            icon="el-icon-refresh"
            circle
            style="float: right"
            size="small"
            @click="getDataList()"
          >
          </el-button>
        </el-tooltip>
      </el-col>
    </el-row>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      :row-style="{ height: '30px' }"
      style="width: 100%"
      type="expand"
    >
      <el-table-column
        label="文件"
        prop="name"
        min-width="200px"
        header-align="center"
        align="center"
      >
        <template slot-scope="scope">
          <a
            href="javascript:void(0);"
            @click="download('source', scope.row.id)"
            >{{ scope.row.name }}</a
          >
        </template>
      </el-table-column>
      <el-table-column
        min-width="200px"
        align="center"
        label="上传时间"
        prop="createTime"
      ></el-table-column>
      <el-table-column
        min-width="70"
        prop="enabled"
        header-align="center"
        align="center"
        label="启用状态"
      >
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.enabled"
            active-color="#13ce66"
            inactive-color="#ff4949"
            :active-value="1"
            :inactive-value="0"
            :disabled="scope.row.status === -1"
            @change="changeEnabled(scope.row)"
          >
          </el-switch>
          <!--          <el-tag v-if="scope.row.status === 1" size="small">启用</el-tag>-->
          <!--          <el-tag v-if="scope.row.status === 0" size="small" type="danger">禁用</el-tag>-->
        </template>
      </el-table-column>
      <el-table-column
        min-width="100px"
        align="center"
        label="导入状态"
        prop="status"
      >
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === -1" size="small" type="danger"
            >失败
          </el-tag>
          <el-tag v-if="scope.row.status === 0" size="small">待导入</el-tag>
          <el-tag v-if="scope.row.status === 1" size="small">导入中</el-tag>
          <el-tag v-if="scope.row.status === 2" size="small" type="success"
            >导入完成
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="操作"
        prop="alteration"
        width="300px"
        min-width="300px"
      >
        <template slot-scope="scope">
          <a
            v-if="scope.row.status === -1"
            href="javascript:void(0);"
            @click="download('log', scope.row.id)"
            ><i class="el-icon-download"></i>错误日志下载</a
          >
          <a
            v-if="scope.row.status !== 1"
            href="javascript:void(0);"
            class="ml-3"
            style="color: #f56c6c"
            @click="deleteHandle(scope.row.id)"
          >
            <i class="el-icon-delete"></i>删除
          </a>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    >
    </el-pagination>
  </div>
</template>

<script>
import { Loading } from "element-ui";
import FileSaver from "file-saver";
import Thread from "@/utils/thread";

export default {
  name: "upload-pre-entity",
  data() {
    return {
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      type: this.$ImportType.pre_anno_entity,
      projectId: 0,
      thread: new Thread({
        start: () => {
          this.getDataList();
        },
        stop: () => {
          console.log("结束轮询");
        },
        time: 3000
      }),
      dataListLoading: false
    };
  },

  mounted() {},
  computed: {
    editable: function () {
      return this.isAuth("project:editable");
    }
  },
  methods: {
    stopThread() {
      this.thread.stop();
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val;
      this.pageIndex = 1;
      this.getDataList();
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val;
      this.getDataList();
    },
    init(projectId) {
      this.projectId = projectId;
      this.$nextTick(() => {
        this.getDataList();
        this.thread.run();
      });
    },
    getDataList() {
      this.dataListLoading = false;
      this.$http({
        url: this.$http.adornUrl("/importLog/list"),
        method: "get",
        params: this.$http.adornParams({
          projectId: this.projectId,
          type: this.type,
          page: this.pageIndex,
          limit: this.pageSize
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list;
          this.totalPage = data.page.totalCount;
        } else {
          this.dataList = [];
          this.totalPage = 0;
        }
        this.dataListLoading = false;
      });
    },
    uploadArticles: function (params) {
      this.$refs.upload.clearFiles();
      const file = params.file;
      const isJson = /\.json$/.test(file.name);
      if (!isJson || file.size / 1024 / 1024 > 30) {
        this.$message.error({
          dangerouslyUseHTMLString: true,
          message: "只能上传 <strong>json</strong> 文件，且不超过30M"
        });
        params.onError();
        return;
      }
      const loadingInstance = Loading.service({
        lock: true,
        text: "上传解析中"
      });

      const formData = new FormData();
      formData.append("file", file);
      formData.append("projectId", this.projectId);
      this.$http({
        url: this.$http.adornUrl("/project/uploadPreAnno/entity"),
        method: "post",
        timeout: 0,
        headers: { "Content-Type": "multipart/form-data;" },
        data: formData
      }).then(({ data }) => {
        // sleep(3000).then(() => {
        loadingInstance.close();
        // });
        if (data.code === 0) {
          this.$message.success("后台导入中");
          this.getDataList();
        }
        params.onSuccess();
      });
    },
    downloadTemplate() {
      this.$downloadTemplate(
        "pre-entity-template.json",
        "实体预标注上传--模板.json"
      );
    },
    download(type, id) {
      this.$http({
        url: this.$http.adornUrl("/importLog/download"),
        params: this.$http.adornParams({
          type: type,
          id: id
        }),
        method: "get",
        responseType: "blob"
      })
        .then((resp) => {
          if (resp.headers["content-type"] !== "application/octet-stream") {
            const err = "错误日志下载失败";
            throw err;
          }
          FileSaver.saveAs(resp.data, decodeURI(resp.headers["filename"]));
        })
        .catch((err) => {
          this.$message.error(err);
        });
    },
    changeEnabled(row) {
      const loading = this.$loading({
        lock: true,
        text: `正在${row.enabled ? "启用" : "禁用"}中...`,
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)"
      });
      this.$http({
        url: this.$http.adornUrl("/importLog/changeEnabled"),
        method: "post",
        data: this.$http.adornData({
          id: row.id,
          enabled: row.enabled
        })
      })
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: "操作成功",
              type: "success",
              duration: 1500
            });
          } else {
            this.$message.error(data.msg);
            this.getDataList();
          }
        })
        .finally(() => {
          loading.close();
        });
    },
    deleteHandle(id) {
      this.$confirm(
        "确认是否删除该次导入的预标准数据？（耗时较长，请耐心等待）",
        "确认信息",
        {
          dangerouslyUseHTMLString: true,
          distinguishCancelAndClose: true,
          confirmButtonText: "确认",
          cancelButtonText: "取消"
        }
      ).then(() => {
        this.dataListLoading = true;
        this.$http({
          url: this.$http.adornUrl("/project/deletePreAnno/entity"),
          method: "get",
          timeout: 180000, // 3分钟
          params: this.$http.adornParams({
            id: id
          })
        })
          .then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                type: "warning",
                message: "删除成功"
              });
            }
          })
          .finally(() => {
            this.getDataList();
            this.dataListLoading = false;
          });
      });
    }
  }
};
</script>

<style scoped lang="scss">
.upload-content {
  display: inline-block;
}

.el-table {
  border-radius: 8px;
}

::v-deep.el-tabs--border-card > .el-tabs__header {
  border-bottom: none;
}

.download-btn:hover {
  color: #fff;
  text-decoration: none;
}

.download-btn:visited {
  color: #fff;
  text-decoration: none;
}
</style>
