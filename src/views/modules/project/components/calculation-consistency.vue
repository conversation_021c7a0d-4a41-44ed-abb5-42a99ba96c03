<template>
  <div>
    <el-card
      class="card-item"
      v-loading="loading"
      element-loading-text="计算中，请稍等"
    >
      <div class="d-flex d-space-between" style="margin-bottom: 15px">
        <h2 class="title-tip">
          标注结果一致性统计
          <el-tooltip placement="top" effect="light">
            <div slot="content">
              <strong>Fleiss' Kappa (κ):</strong>
              项目中的数据集可能由多位标注员共同完成，不同标注员对数据的理解很容易存在偏差，这极大程度上会影响数据集的一致性。因此可以通过Fleiss'
              Kappa对标注员之间的标注一致性进行评估，以保证数据集的质量。
              <ul>
                <li>&lt; 0 Poor agreement</li>
                <li>0.01 – 0.20 Slight agreement</li>
                <li>0.21 – 0.40 Fair agreement</li>
                <li>0.41 – 0.60 Moderate agreement</li>
                <li>0.61 – 0.80 Substantial agreement</li>
                <li>0.81 – 1.00 Almost perfect agreement</li>
              </ul>
              参考资料：
              <a href="https://zhuanlan.zhihu.com/p/547781481" target="_blank"
                >如何评价数据标注中的一致性？以信息抽取为例，浅谈Fleiss'
                Kappa</a
              >
              <br />
              <br />

              <strong>Krippendorff's alpha (α):</strong>
              Krippendorff 's alpha
              (α)是一种可靠性系数，用于衡量观察者、编码员、裁判、评分者、注释者或测量工具之间的一致性，这些工具在典型的非结构化现象之间绘制了差异，或者为它们分配了可计算的值。alpha出现在内容分析中，但广泛适用于两种或多种生成数据的方法，这些方法应用于同一组对象、预定义的分析单元或项目，问题是结果数据在多大程度上可以信任，以表示值得分析的东西。
              <ul>
                <li>Slight agreement: 0.2 &lt; α ≤ 0.4</li>
                <li>Fair agreement: 0.4 &lt; α ≤ 0.6</li>
                <li>Moderate agreement: 0.6 &lt; α ≤ 0.8</li>
                <li>Substantial agreement: α > 0.8</li>
                <li>
                  Near-perfect agreement: When observers agree perfectly, α=1
                </li>
              </ul>
              参考资料：
              <a
                href="https://en.wikipedia.org/wiki/Krippendorff%27s_alpha"
                target="_blank"
                >Krippendorff's alpha（维基百科）</a
              >
              <br />
              <br />
              <br />
              <br />

              <strong>计算条件：</strong>
              <ul>
                <li>1. 必须是多轮标注的项目</li>
                <li>
                  2. 多个标注员共同标注过同一批文书，且标注任务的状态是：已标注
                  or 审核中 or 已验收
                </li>
              </ul>
              <br />
              <strong
                >备注：默认结果是凌晨4点统计的，如果需要实时结果，请点击右侧刷新按钮</strong
              >
            </div>
            <i class="el-icon-question"></i>
          </el-tooltip>
        </h2>
        <div>
          <el-button
            type="primary"
            size="small"
            plain
            icon="el-icon-download"
            @click="exportEntity"
            >导出实体标签一致性
          </el-button>
          <el-button
            type="primary"
            size="small"
            plain
            icon="el-icon-refresh"
            :disabled="loading"
            @click="init(projectId, true, batchId)"
            >刷新
          </el-button>
          <el-button
            type="primary"
            size="small"
            plain
            icon="el-icon-download"
            @click="handleExport"
            >导出
          </el-button>
        </div>
      </div>
      <el-table
        :data="dataList"
        border
        size="mini"
        style="width: 100%"
        max-height="300"
      >
        <el-table-column sortable label="标注员">
          <template slot-scope="scope">
            <span>{{ scope.row.annotater.join("、") }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="实体标注 Fleiss' Kappa (κ)"
          prop="entityKappa"
          sortable
        >
          <template slot-scope="scope">
            <span>{{ scope.row.entityKappa.toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="实体标注 Krippendorff's Alpha (α)"
          prop="entityAlpha"
          sortable
        >
          <template slot-scope="scope">
            <span>{{ scope.row.entityAlpha.toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="属性标注 Fleiss' Kappa (κ)"
          prop="attrKappa"
          sortable
        >
          <template slot-scope="scope">
            <span>{{ scope.row.attrKappa.toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="属性标注 Krippendorff's Alpha (α)"
          prop="attrAlpha"
          sortable
        >
          <template slot-scope="scope">
            <span>{{ scope.row.attrAlpha.toFixed(2) }}</span>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
import * as XLSX from "xlsx";
import { AnnoEventsBus } from "@/utils/bus";

export default {
  props: {
    projectId: {
      required: true,
      type: String
    }
  },
  name: "CalculationConsistency",
  data() {
    return {
      loading: false,
      batchId: undefined,
      dataList: []
    };
  },
  computed: {
    roleId() {
      return this.$store.state.user.roleId;
    }
  },
  methods: {
    init(projectId, refresh = false, batchId) {
      if (batchId) {
        this.batchId = batchId;
      }
      this.loading = true;
      this.$http({
        url: this.$http.adornUrl(`/statistics/calculationConsistency`),
        method: "get",
        timeout: 0,
        params: this.$http.adornParams({
          projectId,
          batchId,
          refresh
        })
      })
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.dataList = data.data || [];
            if (refresh) {
              AnnoEventsBus.$emit(
                "RefreshConsistencyChart",
                projectId,
                batchId
              );
            }
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 导出
    handleExport() {
      if (!this.dataList || this.dataList.length === 0) {
        this.$message.info("暂无数据");
        return;
      }
      let exportData = JSON.parse(JSON.stringify(this.dataList));

      exportData.forEach((obj) => {
        obj.annotater = obj.annotater.join("、");
        obj.entityKappa = obj.entityKappa.toFixed(2);
        obj.entityAlpha = obj.entityAlpha.toFixed(2);
        obj.attrKappa = obj.attrKappa.toFixed(2);
        obj.attrAlpha = obj.attrAlpha.toFixed(2);
      });
      const loading = this.$loading({
        lock: true,
        text: "导出中",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)"
      });
      const headers = [
        "标注员",
        "实体标注 Fleiss' Kappa (κ)",
        "实体标注 Krippendorff's Alpha（α）",
        "属性标注 Fleiss' Kappa (κ)",
        "属性标注 Krippendorff's Alpha (α)"
      ];
      const fields = [
        "annotater",
        "entityKappa",
        "entityAlpha",
        "attrKappa",
        "attrAlpha"
      ];
      const wb = XLSX.utils.book_new();
      // 生成 Worksheet 对象
      const ws = XLSX.utils.json_to_sheet(exportData, {
        header: fields
      });
      XLSX.utils.sheet_add_aoa(ws, [headers], { origin: "A1" });
      // 添加 Worksheet 到 Workbook
      XLSX.utils.book_append_sheet(wb, ws, "Sheet1");
      // 将 Workbook 导出为 Excel 文件
      XLSX.writeFile(wb, `标注员标注一致性统计.xlsx`);
      loading.close();
    },
    exportEntity() {
      this.$prompt("请输入接收计算结果的邮箱", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPattern:
          /[\w!#$%&'*+/=?^_`{|}~-]+(?:\.[\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\w](?:[\w-]*[\w])?\.)+[\w](?:[\w-]*[\w])?/,
        inputErrorMessage: "邮箱格式不正确"
      })
        .then(({ value }) => {
          this.$http({
            url: this.$http.adornUrl(`/statistics/exportEntityConsistency`),
            method: "get",
            params: this.$http.adornParams({
              projectId: this.projectId,
              batchId: this.batchId,
              email: value
            })
          }).then(({ data }) => {
            this.$message({
              type: "success",
              message: "已提交导出申请，数据稍后发送至您的邮箱"
            });
          });
        })
        .catch(() => {});
    }
  }
};
</script>

<style scoped lang="scss">
.card-item {
  margin: 15px 0;
}

.title-tip {
  margin: 3px 0 0 5px;
  font-size: 18px;
  font-family: sans-serif;
}
</style>
