<template>
  <div>
    <el-card
      class="card-item"
      v-loading="loading"
      element-loading-text="计算中，请稍等"
    >
      <div class="d-flex d-space-between" style="margin-bottom: 15px">
        <h2 class="title-tip">
          标注相似性聚类树图
          <el-tooltip placement="top" effect="light">
            <div slot="content">
              <p>
                在聚类树中，y轴的高度表示簇之间的距离，也就是两个簇或样本合并时的相似性/不相似性程度。具体来说，y轴的数值（称为“连结高度”）越小，代表聚类之间的相似性越高；反之，y轴数值越大则表示聚类之间的相似性较低。
              </p>
              <strong>计算条件：</strong>
              <ul>
                <li>1. 必须是多轮标注的项目</li>
                <li>
                  2. 当前项目中标注任务的状态是：已标注 或 审核中 或
                  已验收的文书
                </li>
                <li>3. 选取的计算元素为标注员标注的所有实体（不包含属性）</li>
              </ul>
              <br />
              <strong
                >备注：当前展示的内容是上一次查询的结果，如果需要实时结果，请点击右侧刷新按钮</strong
              >
            </div>
            <i class="el-icon-question"></i>
          </el-tooltip>
        </h2>
        <div>
          <el-button
            type="primary"
            size="small"
            plain
            icon="el-icon-refresh"
            @click="init(projectId, true)"
            >刷新
          </el-button>
        </div>
      </div>
      <el-empty v-if="!hasData" :description="errMsg"></el-empty>
      <div v-else class="demo-image__preview" style="text-align: center">
        <el-image
          style="width: 1000px; height: 800px"
          :src="pngURL"
          :preview-src-list="[pngURL]"
        >
        </el-image>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  props: {
    projectId: {
      required: true,
      type: String
    }
  },
  name: "Dendrogram",
  data() {
    return {
      pngURL: `${process.env.VUE_APP_BASE_API}/temp//${this.projectId}.png`,
      hasData: false,
      errMsg: "",
      loading: false
    };
  },
  methods: {
    init(projectId, refresh = false) {
      this.loading = true;
      this.hasData = false;
      this.$http({
        url: this.$http.adornUrl(`/statistics/drawDendrogram`),
        method: "get",
        timeout: 0,
        params: this.$http.adornParams({
          projectId: Number(projectId),
          refresh: refresh
        })
      })
        .then(({ data }) => {
          if (data.code === 303) {
            this.errMsg = data.msg;
          }
          if (data.code === 0) {
            this.errMsg = "";
            this.hasData = true;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    }
  }
};
</script>

<style scoped lang="scss">
.card-item {
  margin: 15px 0;
}
.title-tip {
  margin: 3px 0 0 5px;
  font-size: 18px;
  font-family: sans-serif;
}
</style>
