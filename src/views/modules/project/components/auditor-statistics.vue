<template>
  <div>
    <el-card class="card-item">
      <div class="d-flex d-space-between" style="margin-bottom: 15px">
        <h2 class="title-tip">
          审核员审核统计
          <el-tooltip placement="top" effect="light">
            <div slot="content">
              <strong>文书数:</strong> 用户审核过的文书数<br /><br />
              <strong>实体标注:</strong>
              所有实体总数（该用户审核文书时新增、编辑、删除的实体数）<br /><br />
              <strong>属性标注:</strong>
              所有属性总数（该用户审核文书时新增、编辑、删除的属性数）<br /><br />
              <strong>实体属性关联数:</strong>
              所有填写或者拖拽的属性数量（该用户审核文书时新增的数量）<br /><br />
              <strong>关系组数:</strong>
              以模板为单位，所有关系组数（该用户审核文书时新增、编辑、删除的关系组数）<br /><br />
              <strong>关系条数:</strong>
              以一条三元组为单位，所有关系条数（该用户审核文书时新增、编辑的关系条数）<br /><br />
              <strong>平均耗时:</strong> 审核完成一篇文书的平均耗时
              （统计范围是：10秒~1小时内的数据）
              <br />
              <br />
              <strong
                >备注：默认结果是凌晨4点统计的，如果需要实时结果，请点击右侧刷新按钮</strong
              >
            </div>
            <i class="el-icon-question"></i>
          </el-tooltip>
        </h2>
        <div>
          <el-button
            type="primary"
            size="small"
            plain
            icon="el-icon-refresh"
            :disabled="loading"
            @click="init(projectId, true, batchId)"
            >刷新
          </el-button>
          <el-button
            type="primary"
            size="small"
            plain
            icon="el-icon-download"
            @click="handleExport"
            >导出
          </el-button>
        </div>
      </div>
      <el-table
        v-loading="loading"
        :data="dataList"
        border
        max-height="300px"
        size="mini"
        style="width: 100%"
      >
        <el-table-column label="用户名" sortable prop="username">
        </el-table-column>
        <el-table-column label="文书数" sortable prop="count">
        </el-table-column>
        <el-table-column sortable sort-by="entityCount" label="实体数">
          <template slot-scope="scope">
            <span
              >{{ scope.row.entityCount }} (
              {{ scope.row.correctEntityCount }})</span
            >
          </template>
        </el-table-column>
        <el-table-column label="属性数" sortable sort-by="attrCount">
          <template slot-scope="scope">
            <span
              >{{ scope.row.attrCount }} (
              {{ scope.row.correctAttrCount }})</span
            >
          </template>
        </el-table-column>
        <el-table-column
          label="实体属性关联数"
          sortable
          sort-by="entityAttrCount"
          width="130"
        >
          <template slot-scope="scope">
            <span
              >{{ scope.row.entityAttrCount }} (
              {{ scope.row.correctAttrCount }})</span
            >
          </template>
        </el-table-column>
        <el-table-column label="关系组数" sortable sort-by="relationGroupCount">
          <template slot-scope="scope">
            <span
              >{{ scope.row.relationGroupCount }} (
              {{ scope.row.correctRelationGroupCount }})</span
            >
          </template>
        </el-table-column>
        <el-table-column label="关系条数" sortable prop="relationCount">
          <template slot-scope="scope">
            <span
              >{{ scope.row.relationCount }} (
              {{ scope.row.correctRelationCount }})</span
            >
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          label="平均耗时"
          sortable
          prop="avgTime"
        >
          <template slot-scope="scope">
            {{ scope.row.avgTime | processTime }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="导出数据" width="120">
          <template slot-scope="scope">
            <el-tooltip
              content="导出该用户审核的详细数据"
              placement="top"
              effect="light"
            >
              <i
                class="el-icon-download icon-primary"
                @click="exportDetail(scope.row.userId, scope.row.username)"
              >
                导出详情</i
              >
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
import * as XLSX from "xlsx";
import { AnnoEventsBus } from "@/utils/bus";

export default {
  props: {
    projectId: {
      required: true,
      type: String
    }
  },
  name: "AnnoStatistics",
  data() {
    return {
      loading: false,
      batchId: undefined,
      dataList: []
    };
  },
  created() {},
  computed: {
    roleId() {
      return this.$store.state.user.roleId;
    }
  },
  mounted() {},
  methods: {
    init(projectId, refresh = false, batchId) {
      if (batchId) {
        this.batchId = batchId;
      }
      this.loading = true;
      this.$http({
        url: this.$http.adornUrl(`/statistics/roleStatistics`),
        method: "get",
        params: this.$http.adornParams({
          projectId,
          batchId: this.batchId ? this.batchId : undefined,
          roleId: this.$RoleEnum.auditor,
          refresh: refresh
        })
      })
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.dataList = data.data || [];
            if (refresh) {
              AnnoEventsBus.$emit("RefreshAnnoChart", projectId, batchId);
            }
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 导出
    handleExport() {
      if (!this.dataList || this.dataList.length === 0) {
        this.$message.info("暂无数据");
        return;
      }
      let exportData = JSON.parse(JSON.stringify(this.dataList));
      let that = this;
      exportData.forEach((obj) => {
        obj.avgTime = that.$tools.processTime(obj.avgTime);
        delete obj.userId;
        delete obj.correctRate;
        delete obj.needTotal;
        delete obj.correctTotal;
        delete obj.errorTotal;
        delete obj.missTotal;
        delete obj.precision;
        delete obj.recall;
        delete obj.f1Score;
        delete obj.rework;
      });
      const loading = this.$loading({
        lock: true,
        text: "导出中",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)"
      });
      const headers = [
        "审核员",
        "文书数",
        "实体总数",
        "审核实体数",
        "属性总数",
        "审核属性数",
        "实体属性关联数",
        "审核实体属性关联数",
        "关系组总数",
        "审核关系组数",
        "关系条数",
        "审核关系条数",
        "平均耗时"
      ];
      const fields = [
        "username",
        "count",
        "entityCount",
        "correctEntityCount",
        "attrCount",
        "correctAttrCount",
        "entityAttrCount",
        "correctEntityAttr",
        "relationGroupCount",
        "correctRelationGroupCount",
        "relationCount",
        "correctRelationCount",
        "avgTime"
      ];
      const wb = XLSX.utils.book_new();
      // 生成 Worksheet 对象
      const ws = XLSX.utils.json_to_sheet(exportData, {
        header: fields
      });
      XLSX.utils.sheet_add_aoa(ws, [headers], { origin: "A1" });
      // 添加 Worksheet 到 Workbook
      XLSX.utils.book_append_sheet(wb, ws, "Sheet1");
      // 将 Workbook 导出为 Excel 文件
      XLSX.writeFile(wb, `审核员工作量总览.xlsx`);
      loading.close();
    },
    exportDetail(userId, username) {
      const loading = this.$loading({
        lock: true,
        text: "导出中,请稍等",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)"
      });
      this.$http({
        url: this.$http.adornUrl(`/statistics/annoDetails`),
        method: "get",
        timeout: 3000000, // 超出超时时间5分钟
        params: this.$http.adornParams({
          projectId: Number(this.projectId),
          roleId: this.$RoleEnum.auditor,
          userId
        })
      })
        .then(({ data }) => {
          if (data && data.code === 0) {
            let downloadList = data.data;
            if (!downloadList || downloadList.length <= 0) {
              this.$message.info("暂无数据");
            } else {
              let that = this;
              downloadList.forEach((obj) => {
                obj.invalid = obj.invalid === 1 ? "是" : "否";
                obj.diffTime = that.$tools.processTime(obj.diffTime);
                delete obj.correctRate;
                delete obj.taskId;
                delete obj.step;
                delete obj.auditorId;
                delete obj.auditor;
                delete obj.batchId;
                delete obj.annoStartTime;
                delete obj.annoEndTime;
                delete obj.needTotal;
                delete obj.correctTotal;
                delete obj.errorTotal;
                delete obj.missTotal;
              });
              const headers = [
                "批次名",
                "文书编号",
                "状态",
                "实体总数",
                "审核实体数",
                "属性总数",
                "审核属性数",
                "实体属性关联数",
                "审核实体属性关联数",
                "关系组总数",
                "审核关系组数",
                "关系条数",
                "审核关系条数",
                "打回信息",
                "废弃",
                "开始审核时间",
                "结束审核时间",
                "耗时"
              ];
              const fields = [
                "batchName",
                "articleId",
                "stepStr",
                "entityCount",
                "correctEntityCount",
                "attrCount",
                "correctAttrCount",
                "entityAttrCount",
                "correctEntityAttr",
                "relationGroupCount",
                "correctRelationGroupCount",
                "relationCount",
                "correctRelationCount",
                "repulseMsg",
                "invalid",
                "auditStartTime",
                "auditEndTime",
                "diffTime"
              ];
              const wb = XLSX.utils.book_new();
              // 生成 Worksheet 对象
              const ws = XLSX.utils.json_to_sheet(downloadList, {
                header: fields
              });
              XLSX.utils.sheet_add_aoa(ws, [headers], { origin: "A1" });
              // 添加 Worksheet 到 Workbook
              XLSX.utils.book_append_sheet(wb, ws, "Sheet1");
              // 将 Workbook 导出为 Excel 文件
              XLSX.writeFile(wb, `${username}-详细审核信息.xlsx`);
            }
          }
        })
        .finally(() => {
          loading.close();
        });
    }
  },
  components: {}
};
</script>

<style scoped lang="scss">
.card-item {
  margin: 15px 0;
}
.title-tip {
  margin: 3px 0 0 5px;
  font-size: 18px;
  font-family: sans-serif;
}
</style>
