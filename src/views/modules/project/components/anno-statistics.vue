<template>
  <div>
    <el-card class="card-item">
      <div class="d-flex d-space-between" style="margin-bottom: 15px">
        <h2 class="title-tip">
          标注员标注统计
          <el-tooltip placement="top" effect="light">
            <div slot="content">
              <strong>文书数:</strong> 用户标注过的文书数<br /><br />
              <strong>实体标注:</strong>
              用户标注过的所有文书中的实体数（该标注员已审核文书中标注正确的实体数量）<br /><br />
              <strong>属性标注:</strong>
              用户标注过的所有文书中的属性数（该标注员已审核文书中标注正确的属性数量）<br /><br />
              <strong>实体属性关联数:</strong>
              用户在所有实体中填写或者拖拽的属性数量<br /><br />
              <strong>关系组数:</strong>
              以模板为单位，用户创建的关系组数（标注正确的数量）<br /><br />
              <strong>关系条数:</strong>
              以一条三元组为单位，用户创建的关系条数<br /><br />
              <strong>应标数:</strong>
              标注员应该标注的实体、属性、关系总量<br /><br />
              <strong>标对数:</strong>
              标注员标注正确的实体、属性、关系数量<br /><br />
              <strong>标错数:</strong>
              标注员标注错误的实体、属性、关系数量<br /><br />
              <strong>漏标数:</strong>
              标注员应该标注，但是没有标注的实体、属性、关系数量<br /><br />
              <strong>准确率:</strong>
              标对数 / (标对数 + 标错数 + 漏标数)<br /><br />
              <strong>精确率:</strong>
              标对数 / (标对数 + 标错数)<br /><br />
              <strong>召回率:</strong>
              标对数 / (标对数 + 漏标数)<br /><br />
              <strong>F1值:</strong>
              (2 * 精确率 * 召回率) / (精确率 + 召回率)<br /><br />
              <strong>返工率:</strong>
              用户标注的文书被审核员打回的比例（被打回的文书数 /
              标注过的文书总数）<br /><br />
              <strong>平均耗时:</strong> 用户标注完成一篇文书的平均耗时
              （统计范围是：10秒~1小时内的数据）
              <br />
              <br />
              <strong
                >备注：默认结果是凌晨4点统计的，如果需要实时结果，请点击右侧刷新按钮</strong
              >
            </div>
            <i class="el-icon-question"></i>
          </el-tooltip>
        </h2>
        <div>
          <el-button
            type="primary"
            size="small"
            plain
            icon="el-icon-refresh"
            :disabled="loading"
            @click="init(projectId, true, batchId)"
            >刷新
          </el-button>
          <el-button
            type="primary"
            size="small"
            plain
            icon="el-icon-download"
            @click="handleExport"
            >导出
          </el-button>
        </div>
      </div>
      <el-table
        v-loading="loading"
        :data="dataList"
        border
        max-height="300px"
        size="mini"
        style="width: 100%"
      >
        <el-table-column label="用户名" sortable prop="username">
        </el-table-column>
        <el-table-column label="文书数" sortable prop="count">
        </el-table-column>
        <el-table-column sortable sort-by="entityCount" label="实体数">
          <template slot-scope="scope">
            <span
              >{{ scope.row.entityCount }} (
              {{ scope.row.correctEntityCount }})</span
            >
          </template>
        </el-table-column>
        <el-table-column label="属性数" sortable sort-by="attrCount">
          <template slot-scope="scope">
            <span
              >{{ scope.row.attrCount }} (
              {{ scope.row.correctAttrCount }})</span
            >
          </template>
        </el-table-column>
        <el-table-column
          label="实体属性关联数"
          sortable
          prop="entityAttrCount"
          width="130"
        >
        </el-table-column>
        <el-table-column label="关系组数" sortable sort-by="relationGroupCount">
          <template slot-scope="scope">
            <span
              >{{ scope.row.relationGroupCount }} (
              {{ scope.row.correctRelationGroupCount }})</span
            >
          </template>
        </el-table-column>
        <el-table-column label="关系条数" sortable prop="relationCount">
        </el-table-column>
        <el-table-column label="应标数" width="100" sortable prop="needTotal">
        </el-table-column>
        <el-table-column
          label="标对数(TP)"
          width="110"
          sortable
          prop="correctTotal"
        >
        </el-table-column>
        <el-table-column
          label="标错数(FP)"
          width="110"
          sortable
          prop="errorTotal"
        >
        </el-table-column>
        <el-table-column
          label="漏标数(FN)"
          width="110"
          sortable
          prop="missTotal"
        >
        </el-table-column>

        <el-table-column
          align="center"
          label="准确率"
          sortable
          prop="correctRate"
        >
          <template slot-scope="scope">
            {{ scope.row.correctRate | reworkFormat }}
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          label="精确率"
          sortable
          prop="precision"
        >
          <template slot-scope="scope">
            {{ scope.row.precision | reworkFormat }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="召回率" sortable prop="recall">
          <template slot-scope="scope">
            {{ scope.row.recall | reworkFormat }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="F1值" sortable prop="f1Score">
          <template slot-scope="scope">
            {{ scope.row.f1Score | reworkFormat }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="返工率" sortable prop="rework">
          <template slot-scope="scope">
            {{ scope.row.rework | reworkFormat }}
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          label="平均耗时"
          sortable
          prop="avgTime"
        >
          <template slot-scope="scope">
            {{ scope.row.avgTime | processTime }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="导出数据" width="100">
          <template slot-scope="scope">
            <el-tooltip
              content="导出该用户标注详细数据"
              placement="top"
              effect="light"
            >
              <i
                class="el-icon-download icon-primary"
                @click="exportDetail(scope.row.userId, scope.row.username)"
              >
                导出详情</i
              >
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
import * as XLSX from "xlsx";
import { AnnoEventsBus } from "@/utils/bus";

export default {
  props: {
    projectId: {
      required: true,
      type: String
    }
  },
  name: "AnnoStatistics",
  data() {
    return {
      loading: false,
      batchId: undefined,
      dataList: []
    };
  },
  created() {},
  computed: {
    roleId() {
      return this.$store.state.user.roleId;
    }
  },
  mounted() {},
  methods: {
    init(projectId, refresh = false, batchId) {
      if (batchId) {
        this.batchId = batchId;
      }
      this.loading = true;
      this.$http({
        url: this.$http.adornUrl(`/statistics/roleStatistics`),
        method: "get",
        params: this.$http.adornParams({
          projectId,
          batchId: this.batchId ? this.batchId : undefined,
          roleId: this.$RoleEnum.annotator,
          refresh: refresh
        })
      })
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.dataList = data.data || [];
            if (refresh) {
              AnnoEventsBus.$emit("RefreshAnnoChart", projectId, batchId);
            }
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 导出
    handleExport() {
      if (!this.dataList || this.dataList.length === 0) {
        this.$message.info("暂无数据");
        return;
      }
      let exportData = JSON.parse(JSON.stringify(this.dataList));
      let that = this;
      exportData.forEach((obj) => {
        obj.correctRate =
          obj.correctRate !== 0 ? obj.correctRate.toFixed(2) + "%" : "-";

        obj.precision =
          obj.precision !== 0 ? obj.precision.toFixed(2) + "%" : "-";

        obj.recall = obj.recall !== 0 ? obj.recall.toFixed(2) + "%" : "-";

        obj.f1Score = obj.f1Score !== 0 ? obj.f1Score.toFixed(2) + "%" : "-";

        obj.rework = obj.rework !== 0 ? obj.rework.toFixed(2) + "%" : "-";

        obj.avgTime = that.$tools.processTime(obj.avgTime);
        delete obj.userId;
        delete obj.correctEntityAttr;
        delete obj.correctRelationCount;
      });
      const loading = this.$loading({
        lock: true,
        text: "导出中",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)"
      });
      const headers = [
        "标注员",
        "文书数",
        "实体数",
        "正确实体数",
        "属性数",
        "正确属性数",
        "实体属性关联数",
        "关系组数",
        "正确关系组数",
        "关系条数",
        "应标数",
        "标对数(TP)",
        "标错数(FP)",
        "漏标数(FN)",
        "准确率",
        "精确率",
        "召回率",
        "F1值",
        "返工率",
        "平均耗时"
      ];
      const fields = [
        "username",
        "count",
        "entityCount",
        "correctEntityCount",
        "attrCount",
        "correctAttrCount",
        "entityAttrCount",
        "relationGroupCount",
        "correctRelationGroupCount",
        "relationCount",
        "needTotal",
        "correctTotal",
        "errorTotal",
        "missTotal",
        "correctRate",
        "precision",
        "recall",
        "f1Score",
        "rework",
        "avgTime"
      ];
      const wb = XLSX.utils.book_new();
      // 生成 Worksheet 对象
      const ws = XLSX.utils.json_to_sheet(exportData, {
        header: fields
      });
      XLSX.utils.sheet_add_aoa(ws, [headers], { origin: "A1" });
      // 添加 Worksheet 到 Workbook
      XLSX.utils.book_append_sheet(wb, ws, "Sheet1");
      // 将 Workbook 导出为 Excel 文件
      XLSX.writeFile(wb, `标注员工作量总览.xlsx`);
      loading.close();
    },
    exportDetail(userId, username) {
      const loading = this.$loading({
        lock: true,
        text: "导出中,请稍等",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)"
      });
      this.$http({
        url: this.$http.adornUrl(`/statistics/annoDetails`),
        method: "get",
        timeout: 3000000, // 超出超时时间5分钟
        params: this.$http.adornParams({
          projectId: Number(this.projectId),
          roleId: this.$RoleEnum.annotator,
          userId
        })
      })
        .then(({ data }) => {
          if (data && data.code === 0) {
            let downloadList = data.data;
            if (!downloadList || downloadList.length <= 0) {
              this.$message.info("暂无数据");
            } else {
              let that = this;
              downloadList.forEach((obj) => {
                obj.invalid = obj.invalid === 1 ? "是" : "否";
                obj.correctRate =
                  obj.correctRate !== 0
                    ? obj.correctRate.toFixed(2) + "%"
                    : "-";

                obj.diffTime = that.$tools.processTime(obj.diffTime);
                delete obj.taskId;
                delete obj.step;
                delete obj.auditorId;
                delete obj.batchId;
                delete obj.auditStartTime;
                delete obj.auditEndTime;
                delete obj.correctEntityAttr;
                delete obj.correctRelationCount;
              });
              const headers = [
                "批次名",
                "文书编号",
                "状态",
                "实体数",
                "正确实体数",
                "属性数",
                "正确属性数",
                "实体属性关联数",
                "关系组数",
                "正确关系组数",
                "关系条数",
                "应标数",
                "标对数(TP)",
                "标错数(FP)",
                "漏标数(FN)",
                "正确率",
                "打回信息",
                "审核员",
                "废弃",
                "开始标注时间",
                "结束标注时间",
                "耗时"
              ];
              const fields = [
                "batchName",
                "articleId",
                "stepStr",
                "entityCount",
                "correctEntityCount",
                "attrCount",
                "correctAttrCount",
                "entityAttrCount",
                "relationGroupCount",
                "correctRelationGroupCount",
                "relationCount",
                "needTotal",
                "correctTotal",
                "errorTotal",
                "missTotal",
                "correctRate",
                "repulseMsg",
                "auditor",
                "invalid",
                "annoStartTime",
                "annoEndTime",
                "diffTime"
              ];
              const wb = XLSX.utils.book_new();
              // 生成 Worksheet 对象
              const ws = XLSX.utils.json_to_sheet(downloadList, {
                header: fields
              });
              XLSX.utils.sheet_add_aoa(ws, [headers], { origin: "A1" });
              // 添加 Worksheet 到 Workbook
              XLSX.utils.book_append_sheet(wb, ws, "Sheet1");
              // 将 Workbook 导出为 Excel 文件
              XLSX.writeFile(wb, `${username}-详细标注信息.xlsx`);
            }
          }
        })
        .finally(() => {
          loading.close();
        });
    }
  },
  components: {}
};
</script>

<style scoped lang="scss">
.card-item {
  margin: 15px 0;
}

.title-tip {
  margin: 3px 0 0 5px;
  font-size: 18px;
  font-family: sans-serif;
}
</style>
