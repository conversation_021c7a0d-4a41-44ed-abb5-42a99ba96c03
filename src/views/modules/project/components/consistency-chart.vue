<template>
  <el-card style="margin-top: 15px">
    <div style="width: 100%; height: 50vh" id="chart-consistency"></div>
  </el-card>
</template>

<script>
import * as echarts from "echarts";
import { AnnoEventsBus } from "@/utils/bus";

export default {
  name: "ConsistencyChart",
  created() {
    AnnoEventsBus.$on("RefreshConsistencyChart", (projectId, batchId) => {
      this.init(projectId, batchId);
    });
  },
  methods: {
    init(projectId, batchId) {
      const myChart = echarts.init(
        document.getElementById("chart-consistency")
      );
      myChart.showLoading();
      this.$http({
        url: this.$http.adornUrl(`/statistics/consistencyChart`),
        method: "get",
        params: this.$http.adornParams({
          projectId,
          batchId
        })
      })
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.drawDateStatistics(myChart, data.data);
          }
        })
        .catch((err) => {
          this.$message.error(err);
        })
        .finally(() => {
          myChart.hideLoading();
          myChart.resize();
        });
    },
    drawDateStatistics(myChart, data) {
      // 判断数据是否存在有效数据（即至少有一个非零值）
      const hasValidData = data?.series?.some((series) =>
        series.data.some((val) => val !== 0)
      );

      myChart.setOption({
        title: {
          text: "标注一致性"
        },
        legend: {
          data: data?.series.map((item) => item.name),
          width: "80%"
        },
        tooltip: {
          trigger: "axis",
          formatter: function (params) {
            // 根据值大小降序排序
            params.sort((a, b) => b.value - a.value);
            let result = params[0].name + "<br/>";
            params.forEach((item) => {
              result +=
                item.marker +
                item.seriesName +
                ": " +
                item.value.toFixed(2) +
                "<br/>";
            });
            return result;
          }
        },
        graphic: {
          // 如果存在有效数据，则隐藏 "暂无数据" 提示，否则显示
          invisible: hasValidData,
          type: "text",
          left: "center",
          top: "middle",
          style: {
            text: "暂无数据",
            fill: "#888"
          }
        },
        xAxis: {
          type: "category",
          data: data?.xAxis
        },
        yAxis: {
          type: "value",
          name: "实体标注 Fleiss' Kappa (κ)"
        },
        grid: {
          left: "10%",
          right: "10%"
        },
        series: data?.series,
        dataZoom: [
          {
            show: true,
            realtime: true
          },
          {
            type: "inside",
            show: true,
            xAxisIndex: [0],
            start: 0,
            end: 100
          }
        ]
      });
    }
  },
  destroyed() {
    AnnoEventsBus.$off("RefreshConsistencyChart");
  }
};
</script>

<style scoped lang="scss">
.mod-home {
  line-height: 1.5;
}
.home-top,
.chart-bar {
  display: flex;
  justify-content: space-between;
  .item {
    padding: 10px 20px;
    flex-grow: 1;
    flex-shrink: 1;
    flex-basis: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    border-radius: 8px;
    &:hover {
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }
    div:first-child {
      margin-right: 35px;
      width: 55px;
      height: 55px;
      text-align: center;
      line-height: 63px;
      background-color: #579aff;
      border-radius: 8px;
      .icon-svg,
      i {
        font-size: 1.5rem;
        //color: #fff;
      }
    }
    div:last-child {
      display: flex;
      flex-direction: column;
      text-align: right;
      span:first-child {
        font-size: 1.4rem;
        color: #182524;
        font-weight: 600;
      }
      span:last-child {
        color: #6b7373;
      }
    }
  }
  .item:not(:last-child),
  .el-card:not(:last-child) {
    margin-right: 10px;
  }
  .el-card {
    flex-grow: 1;
    flex-shrink: 1;
    flex-basis: 100%;
    ::v-deep .el-card__body {
      height: 100% !important;
    }
  }
}
</style>
