<template>
  <el-dialog title="其他任务数据对比" :visible.sync="dialogVisible" width="80%">
    <el-table
      :data="dataList"
      border
      :header-cell-style="{ background: '#F5F7FA' }"
      style="width: 100%; border-radius: 8px"
      max-height="600"
      :header-row-style="{ height: '44px' }"
    >
      <el-table-column label="任务名" sortable prop="taskName">
      </el-table-column>

      <el-table-column
        align="left"
        min-width="100"
        header-align="center"
        label="实体:属性标签名"
        prop="labelName"
      >
      </el-table-column>

      <el-table-column label="导入数" sortable prop="entityTotal">
      </el-table-column>

      <el-table-column label="应标数" sortable prop="needTotal">
      </el-table-column>

      <el-table-column label="标对数(TP)" sortable prop="correctTotal">
      </el-table-column>

      <el-table-column label="标错数(FP)" sortable prop="errorTotal">
      </el-table-column>

      <el-table-column label="漏标数(FN)" sortable prop="missTotal">
      </el-table-column>

      <el-table-column
        align="center"
        label="准确率"
        sortable
        prop="correctRate"
      >
        <template slot-scope="scope">
          {{ scope.row.correctRate | reworkFormat }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="精确率" sortable prop="precision">
        <template slot-scope="scope">
          {{ scope.row.precision | reworkFormat }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="召回率" sortable prop="recall">
        <template slot-scope="scope">
          {{ scope.row.recall | reworkFormat }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="F1值" sortable prop="f1Score">
        <template slot-scope="scope">
          {{ scope.row.f1Score | reworkFormat }}
        </template>
      </el-table-column>
    </el-table>

    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">关 闭</el-button>
      <el-button type="primary" @click="handleExport">导 出</el-button>
    </span>
  </el-dialog>
</template>

<script>
import * as XLSX from "xlsx";

export default {
  name: "list",

  data() {
    return {
      id: undefined,
      dialogVisible: false,
      checkList: [],
      dataList: []
    };
  },
  components: {},

  methods: {
    init(id) {
      this.id = id;
      this.getDataList();
    },
    getDataList() {
      this.$http({
        url: this.$http.adornUrl("/verify/label/listByOther"),
        method: "get",
        params: this.$http.adornParams({
          verifyLabelId: this.id
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.data;
          if (this.dataList.length < 2) {
            this.$message.warning("其他任务中没有该标签数据");
          } else {
            this.dialogVisible = true;
          }
        } else {
          this.dataList = [];
        }
      });
    },
    handleExport() {
      if (!this.dataList || this.dataList.length === 0) {
        this.$message.info("暂无数据");
        return;
      }
      let exportData = JSON.parse(JSON.stringify(this.dataList));
      exportData.forEach((obj) => {
        obj.correctRate =
          obj.correctRate !== 0 ? obj.correctRate.toFixed(2) + "%" : "-";

        obj.precision =
          obj.precision !== 0 ? obj.precision.toFixed(2) + "%" : "-";

        obj.recall = obj.recall !== 0 ? obj.recall.toFixed(2) + "%" : "-";

        obj.f1Score = obj.f1Score !== 0 ? obj.f1Score.toFixed(2) + "%" : "-";

        delete obj.id;
        delete obj.type;
        delete obj.labelId;
        delete obj.verifyTaskId;
        delete obj.deleted;
      });
      const loading = this.$loading({
        lock: true,
        text: "导出中",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)"
      });
      const headers = [
        "任务名",
        "标签名",
        "导入实体数",
        "应标实体数",
        "标对数(TP)",
        "标错数(FP)",
        "漏标数(FN)",
        "准确率",
        "精确率",
        "召回率",
        "F1值"
      ];
      const fields = [
        "taskName",
        "labelName",
        "entityTotal",
        "needTotal",
        "correctTotal",
        "errorTotal",
        "missTotal",
        "correctRate",
        "precision",
        "recall",
        "f1Score"
      ];
      const wb = XLSX.utils.book_new();
      // 生成 Worksheet 对象
      const ws = XLSX.utils.json_to_sheet(exportData, {
        header: fields
      });
      XLSX.utils.sheet_add_aoa(ws, [headers], { origin: "A1" });
      // 添加 Worksheet 到 Workbook
      XLSX.utils.book_append_sheet(wb, ws, "Sheet1");
      // 将 Workbook 导出为 Excel 文件
      XLSX.writeFile(wb, `数据验证-实体标签详情.xlsx`);
      loading.close();
    }
  }
};
</script>
