<template>
  <div>
    <div class="back-head">
      <a class="go-back" href="javascript:0;" @click="$router.go(-1)">
        <i class="el-icon-arrow-left"></i>返回
      </a>
      数据验证
    </div>

    <el-form :inline="true" :model="formInline" class="demo-form-inline">
      <el-form-item label="任务名">
        <el-input
          v-model="formInline.taskName"
          style="width: 200px"
          clearable
          placeholder="任务名"
        ></el-input>
      </el-form-item>

      <el-form-item label="状态">
        <el-select
          v-model="formInline.status"
          class="status-width"
          clearable
          style="width: 200px"
          placeholder="状态"
        >
          <el-option label="分析完成" value="2"></el-option>
          <el-option label="分析中" value="1"></el-option>
          <el-option label="待导入" value="0"></el-option>
          <el-option label="导入失败" value="-1"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="创建时间">
        <el-date-picker
          v-model="formInline.date"
          end-placeholder="结束时间"
          range-separator="-"
          start-placeholder="开始时间"
          type="daterange"
          value-format="yyyy-MM-dd"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button
          icon="el-icon-search"
          @click="
            () => {
              this.pageIndex = 1;
              this.getDataList(true);
            }
          "
          >查询</el-button
        >
      </el-form-item>

      <el-form-item v-if="isAuth('project:editable')">
        <el-button
          icon="el-icon-plus"
          type="primary"
          @click="addOrUpdateHandle()"
          >新增</el-button
        >
      </el-form-item>
    </el-form>

    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList(true)"
    ></add-or-update>

    <el-table
      v-loading="dataListLoading"
      :data="dataList"
      border
      :header-cell-style="{ background: '#F5F7FA' }"
      style="width: 100%; border-radius: 8px"
      :header-row-style="{ height: '44px' }"
    >
      <el-table-column
        align="center"
        header-align="center"
        label="ID"
        prop="id"
        width="50"
      >
      </el-table-column>
      <el-table-column
        align="center"
        header-align="center"
        label="任务名"
        show-overflow-tooltip
        prop="taskName"
      >
      </el-table-column>

      <el-table-column
        align="center"
        header-align="center"
        label="验证预标注"
        min-width="130"
        show-overflow-tooltip
        prop="preSourceName"
      >
      </el-table-column>

      <el-table-column
        align="center"
        header-align="center"
        label="比对批次名"
        show-overflow-tooltip
        prop="batchName"
      >
      </el-table-column>

      <el-table-column
        align="center"
        header-align="center"
        label="描述"
        :show-overflow-tooltip="true"
        prop="description"
      >
      </el-table-column>

      <el-table-column
        align="center"
        header-align="center"
        label="计算方式"
        prop="method"
        width="100"
      >
        <template slot-scope="scope">
          <span v-if="scope.row.method === 1">基于位置</span>
          <span v-if="scope.row.method === 2">基于文本</span>
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        header-align="center"
        label="状态"
        prop="status"
        width="100"
      >
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === -1" size="small" type="danger"
            >导入失败
          </el-tag>
          <el-tag v-if="scope.row.status === 0" size="small">待导入</el-tag>
          <el-tag v-if="scope.row.status === 1" size="small">分析中</el-tag>
          <el-tag v-if="scope.row.status === 2" size="small" type="success"
            >分析完成
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        header-align="center"
        label="创建时间"
        prop="createTime"
        width="160"
      >
        <template slot-scope="scope">
          <div>{{ scope.row.createTime | dateFrm }}</div>
        </template>
      </el-table-column>

      <el-table-column
        align="right"
        header-align="center"
        label="操作"
        width="100"
      >
        <template slot-scope="scope">
          <!--          <el-tooltip
            v-if="scope.row.status === -1"
            content="下载错误日志"
            placement="top"
            effect="light"
          >
            <i
              class="el-icon-s-release icon-red"
              @click="downloadErrLog(scope.row.id)"
            ></i>
          </el-tooltip>-->

          <el-tooltip
            v-if="scope.row.status === 2"
            content="查看标签详情"
            placement="top"
            effect="light"
          >
            <i
              class="el-icon-view icon-primary"
              @click="showLabelList(scope.row.id)"
            ></i>
          </el-tooltip>

          <!--          <el-tooltip
            v-if="isAuth('project:editable')"
            content="数据导入"
            placement="top"
            effect="light"
          >
            <i
              class="el-icon-upload icon-primary"
              @click="uploadJSON(scope.row.id)"
            ></i>
          </el-tooltip>-->

          <el-tooltip
            v-if="isAuth('project:editable')"
            content="编辑"
            placement="top"
            effect="light"
          >
            <i
              class="el-icon-edit icon-primary"
              @click="addOrUpdateHandle(scope.row.id)"
            ></i>
          </el-tooltip>
          <el-tooltip
            v-if="isAuth('project:editable')"
            content="删除"
            placement="top"
            effect="light"
          >
            <i
              class="el-icon-delete icon-red"
              @click="deleteHandle(scope.row.id)"
            ></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page="pageIndex"
      :page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
    >
    </el-pagination>

    <upload-data
      ref="uploadFile"
      @refreshDataList="getDataList(true)"
    ></upload-data>
    <verity-label-list ref="verityLabelList"></verity-label-list>
  </div>
</template>

<script>
import moment from "moment";
import AddOrUpdate from "./add-or-update";
import UploadData from "./upload-data";
import VerityLabelList from "./verity-label-list";
import Thread from "@/utils/thread";
import FileSaver from "file-saver";

export default {
  name: "list",
  props: {
    projectId: {
      required: true
    }
  },
  data() {
    return {
      articleListVisible: false,
      dataList: [],
      pageIndex: 1,
      pageSize: 20,
      totalPage: 0,
      addOrUpdateVisible: false,
      dataListLoading: false,
      thread: new Thread({
        start: () => {
          this.getDataList(false);
        },
        stop: () => {
          console.log("结束轮询");
        },
        time: 3000
      }),
      formInline: {
        taskName: "",
        status: "",
        date: []
      }
    };
  },
  filters: {
    dateFrm: function (value) {
      return moment(value).format("YYYY-MM-DD HH:mm:ss");
    }
  },
  components: {
    AddOrUpdate,
    VerityLabelList,
    UploadData
  },
  mounted() {
    if (!this.projectId) {
      this.$router.push({ name: "home" });
    } else {
      this.pageIndex = 1;
      this.thread.run();
      this.getDataList(true);
    }
  },
  methods: {
    getDataList(manually = false) {
      if (manually) {
        this.dataListLoading = true;
      }

      this.$http({
        url: this.$http.adornUrl("/verify/list"),
        method: "get",
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          projectId: this.projectId || undefined,
          taskName: this.formInline.taskName,
          status: this.formInline.status,
          startDate:
            (this.formInline?.date != null && this.formInline?.date[0]) ||
            undefined,
          endDate:
            (this.formInline?.date != null && this.formInline?.date[1]) ||
            undefined
        })
      })
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.dataList = data.page.list;
            this.totalPage = data.page.totalCount;
          } else {
            this.dataList = [];
            this.totalPage = 0;
          }
        })
        .finally(() => {
          if (manually) {
            this.dataListLoading = false;
          }
        });
    },
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true;
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(this.projectId, id);
      });
    },
    showLabelList(id) {
      this.$nextTick(() => {
        this.$refs.verityLabelList.init(id);
      });
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val;
      this.pageIndex = 1;
      this.getDataList();
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val;
      this.getDataList();
    },
    // 上传JSON
    uploadJSON(taskId) {
      this.$nextTick(() => {
        this.$refs.uploadFile.init(taskId);
        this.thread.run();
      });
    },
    downloadErrLog(id) {
      this.$http({
        url: this.$http.adornUrl("/verify/downloadLog"),
        params: this.$http.adornParams({
          taskId: id
        }),
        method: "get",
        responseType: "blob"
      })
        .then((resp) => {
          if (resp.headers["content-type"] !== "application/octet-stream") {
            throw "错误日志下载失败";
          }
          FileSaver.saveAs(resp.data, decodeURI(resp.headers["filename"]));
        })
        .catch((err) => {
          this.$message.error(err);
        });
    },
    deleteHandle(id) {
      this.$confirm("确认是否删除该任务？", "确认信息", {
        dangerouslyUseHTMLString: true,
        distinguishCancelAndClose: true,
        confirmButtonText: "确认",
        cancelButtonText: "取消"
      }).then(() => {
        this.dataListLoading = true;
        this.$http({
          url: this.$http.adornUrl("/verify/delete"),
          method: "get",
          timeout: 180000, // 3分钟
          params: this.$http.adornParams({
            taskId: id
          })
        })
          .then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                type: "success",
                message: "任务已删除"
              });
            }
          })
          .finally(() => {
            this.getDataList();
            this.dataListLoading = false;
          });
      });
    }
  },
  beforeDestroy() {
    this.thread.stop();
  }
};
</script>
