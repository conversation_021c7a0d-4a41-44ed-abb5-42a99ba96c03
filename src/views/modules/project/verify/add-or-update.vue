<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '编辑'"
    :visible.sync="visible"
    width="30%"
  >
    <el-form
      :model="dataForm"
      ref="taskForm"
      :rules="rules"
      @keyup.enter.native="dataFormSubmit()"
      label-width="150px"
    >
      <el-form-item prop="projectId" v-show="false">
        <el-input v-model="dataForm.projectId"></el-input>
      </el-form-item>

      <el-form-item prop="id">
        <el-input v-model="dataForm.id" v-show="false"></el-input>
      </el-form-item>

      <el-form-item label="任务名" prop="taskName">
        <el-input
          v-model="dataForm.taskName"
          placeholder="任务名"
          maxlength="30"
        ></el-input>
      </el-form-item>

      <el-form-item label="待比对的预标注" prop="preSourceId">
        <el-select
          style="width: 90%; margin-right: 5px"
          v-model="dataForm.preSourceId"
          class="multiple-select"
          filterable
          clearable
          placeholder="预标注数据版本"
        >
          <el-option
            v-for="val in preSource"
            :key="'k1' + val.id"
            :label="val.name"
            :value="val.id"
          >
          </el-option>
        </el-select>

        <el-tooltip
          class="item"
          effect="dark"
          content="选择需要比对的预标准数据，数据可在“预标注数据导入”功能中上传，目前仅支持实体数据"
          placement="top"
        >
          <i class="el-icon-question mr-2"></i>
        </el-tooltip>
      </el-form-item>

      <el-form-item label="标准数据批次名" prop="batchId">
        <el-select
          style="width: 90%; margin-right: 5px"
          v-model="dataForm.batchId"
          class="multiple-select"
          filterable
          clearable
          placeholder="金标准数据批次名"
        >
          <el-option
            v-for="val in batchList"
            :key="'k2' + val.batchId"
            :label="val.name"
            :value="val.batchId"
          >
          </el-option>
        </el-select>

        <el-tooltip
          class="item"
          effect="dark"
          content="由于一篇文书可能出现在多个批次中，所以需要指定批次才能计算"
          placement="top"
        >
          <i class="el-icon-question mr-2"></i>
        </el-tooltip>
      </el-form-item>

      <el-form-item label="计算方法" size="mini" prop="method">
        <el-radio-group v-model="dataForm.method">
          <el-radio :label="1">基于位置</el-radio>
          <el-radio :label="2">基于文本</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="描述" prop="description">
        <el-input
          v-model="dataForm.description"
          type="textarea"
          maxlength="255"
          rows="5"
          placeholder="描述"
        ></el-input>
      </el-form-item>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { Loading } from "element-ui";

export default {
  name: "add-or-update",
  data() {
    return {
      visible: false,
      batchList: [],
      preSource: [],
      dataForm: {
        projectId: 0,
        id: 0,
        preSourceId: undefined,
        batchId: undefined,
        taskName: "",
        description: "",
        method: 1,
        status: 1
      },
      rules: {
        taskName: [
          { required: true, message: "请输入任务名称", trigger: "blur" }
        ],
        preSourceId: [
          {
            required: true,
            message: "请选择待分析的预标注数据",
            trigger: "blur"
          }
        ],
        batchId: [
          { required: true, message: "请选择比对的批次", trigger: "blur" }
        ]
      }
    };
  },
  methods: {
    // 数据回显
    init(projectId, verifyTaskId) {
      this.visible = true;

      this.getBatchList(projectId);
      this.getPreSourceList(projectId);

      this.$nextTick(() => {
        this.$refs.taskForm.resetFields();

        this.dataForm.projectId = projectId;
        this.dataForm.id = verifyTaskId;

        if (verifyTaskId) {
          this.dataForm.id = verifyTaskId;
          this.$http({
            url: this.$http.adornUrl(`/verify/getTaskById/${verifyTaskId}`),
            method: "get"
          }).then(({ data }) => {
            if (data && data.code === 0) {
              const result = data.data;
              if (result) {
                this.dataForm.projectId = result.projectId;
                this.dataForm.id = result.id;
                this.dataForm.preSourceId = result.preSourceId;
                this.dataForm.batchId = result.batchId;
                this.dataForm.taskName = result.taskName;
                this.dataForm.description = result.description;
                this.dataForm.method = result.method;
                this.dataForm.status = result.status;
              }
            }
          });
        }
      });
    },
    getBatchList(projectId) {
      this.$nextTick(() => {
        this.$http({
          url: this.$http.adornUrl("/batch/list"),
          method: "get",
          params: this.$http.adornParams({
            page: 1,
            limit: 10000,
            projectId: projectId
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.batchList = data.page.list;
          }
        });
      });
    },
    getPreSourceList(projectId) {
      this.$nextTick(() => {
        this.$http({
          url: this.$http.adornUrl("/verify/getPreSourceList/" + projectId),
          method: "get"
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.preSource = data.data;
          }
        });
      });
    },
    // 提交
    dataFormSubmit() {
      this.$refs.taskForm.validate((valid) => {
        if (valid) {
          const loadingInstance = Loading.service();
          this.$http({
            url: this.$http.adornUrl("/verify/save"),
            method: "post",
            data: this.$http.adornData({
              projectId: this.dataForm.projectId,
              preSourceId: this.dataForm.preSourceId,
              batchId: this.dataForm.batchId,
              id: this.dataForm.id || undefined,
              taskName: this.dataForm.taskName,
              description: this.dataForm.description,
              method: this.dataForm.method,
              status: this.dataForm.status
            })
          })
            .then(({ data }) => {
              if (data && data.code === 0) {
                this.$message({
                  message: "操作成功",
                  type: "success"
                });
              }
              // 刷新列表
              this.$emit("refreshDataList");
            })
            .catch((e) => {
              this.$message.error(e);
            })
            .finally(() => {
              this.visible = false;
              loadingInstance.close();
            });
        }
      });
    }
  }
};
</script>

<style scoped></style>
