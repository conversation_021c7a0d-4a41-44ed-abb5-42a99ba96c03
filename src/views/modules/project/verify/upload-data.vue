<template>
  <el-dialog title="提示" :visible.sync="dialogVisible" width="20%">
    <el-upload
      ref="upload"
      :http-request="upload"
      :limit="1"
      :multiple="false"
      :show-file-list="true"
      accept=".json"
      action=""
      class="upload-content"
      style="text-align: center"
    >
      <el-button style="width: 200px" type="primary">点击上传</el-button>
      <div slot="tip" class="el-upload__tip">
        <p>仅支持<strong> 实体标注 </strong>数据</p>
        <p>只能上传 <strong>JSON</strong> 文件，且不超过100M</p>
      </div>
    </el-upload>

    <div style="text-align: center">
      <a
        href="javascript:void(0);"
        @click.prevent="downloadTemplate"
        type="primary"
        ><i class="el-icon-download"></i>下载模板</a
      >
    </div>
  </el-dialog>
</template>

<script>
import { Loading } from "element-ui";

export default {
  name: "upload-data",
  data() {
    return {
      taskId: 0,
      dialogVisible: false
    };
  },
  watch: {
    dialogVisible: function () {
      if (!this.dialogVisible) {
        this.$refs.upload.clearFiles();
      }
    }
  },
  methods: {
    init(taskId) {
      this.dialogVisible = true;
      this.taskId = taskId;
    },
    downloadTemplate() {
      this.$http({
        url: this.$http.adornUrl("/file/verifyTemplate"),
        method: "get",
        responseType: "blob"
      })
        .then((resp) => {
          if (resp.headers["content-type"] !== "application/octet-stream") {
            throw "模板下载失败";
          }
          const url = window.URL.createObjectURL(new Blob([resp.data]));
          const link = document.createElement("a");
          const filename = decodeURI(resp.headers.filename);
          link.href = url;
          link.download = filename;
          link.click();
        })
        .catch((err) => {
          this.$message.error(err);
        });
    },
    upload(params) {
      this.$refs.upload.clearFiles();
      const file = params.file;
      if (!file.name.endsWith(".json") || file.size / 1024 / 1024 > 100) {
        this.$message.error({
          dangerouslyUseHTMLString: true,
          message: "只能上传 <strong>JSON</strong> 文件，且不超过100M"
        });
        params.onError();
        return;
      }
      const loadingInstance = Loading.service();

      const formData = new FormData();
      formData.append("file", file);
      formData.append("taskId", this.taskId);

      this.$http({
        url: this.$http.adornUrl("/verify/uploadData"),
        method: "post",
        timeout: 0,
        headers: { "Content-Type": "multipart/form-data;" },
        data: formData
      }).then(
        ({ data }) => {
          if (data.code === 0) {
            this.$emit("refreshDataList");
          } else {
            this.$message.error(data.msg);
          }
          this.dialogVisible = false;
          loadingInstance.close();
          params.onSuccess();
        },
        () => {
          this.dialogVisible = false;
        }
      );
    }
  }
};
</script>

<style scoped></style>
