<template>
  <div>
    <el-form :inline="true" :model="formInline" class="demo-form-inline">
      <el-form-item label="名称">
        <el-input
          v-model="formInline.name"
          clearable
          placeholder="名称"
          maxlength="30"
        ></el-input>
      </el-form-item>
      <el-form-item label="状态">
        <el-select
          clearable
          v-model="formInline.status"
          class="status-width"
          placeholder="状态"
        >
          <el-option label="运行中" value="1"></el-option>
          <el-option label="已完成" value="2"></el-option>
          <el-option label="已暂停" value="3"></el-option>
          <el-option label="失败" value="-1"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="创建时间">
        <el-date-picker
          v-model="formInline.date"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button @click="getDataList" icon="el-icon-search">查询</el-button>
      </el-form-item>

      <el-form-item>
        <el-button icon="el-icon-plus" type="primary" @click="addHandle()"
          >新增
        </el-button>
      </el-form-item>
    </el-form>

    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @sort-change="sortChange"
      style="width: 100%; border-radius: 8px"
      :header-row-style="{ height: '44px' }"
      :header-cell-style="{
        background: 'rgb(246 247 249)',
        padding: '0px'
      }"
    >
      <el-table-column
        label="ID"
        prop="id"
        header-align="center"
        align="center"
        width="100"
      ></el-table-column>
      <el-table-column
        prop="name"
        header-align="center"
        align="center"
        label="名称"
        sortable="custom"
      >
        <template slot-scope="scope">
          <el-link type="primary" @click="showTaskDetail(scope.row)"
            >{{ scope.row.name }}
          </el-link>
        </template>
      </el-table-column>

      <el-table-column
        label="项目名称"
        prop="projectName"
        header-align="center"
        align="center"
      >
      </el-table-column>
      <el-table-column
        prop="sourceBatchName"
        header-align="center"
        align="center"
        label="金标准批次"
      >
      </el-table-column>
      <el-table-column
        prop="batchName"
        header-align="center"
        align="center"
        label="预标注批次"
      >
      </el-table-column>
      <el-table-column
        prop="progress"
        header-align="center"
        align="center"
        label="运行进度"
      >
        <template slot-scope="scope">
          <el-tooltip
            :content="`共：${scope.row.articleIds.length}条，已处理：${scope.row.processSize}条`"
          >
            <el-progress
              :percentage="
                Number.parseInt(
                  (scope.row.processSize / scope.row.articleIds.length) * 100
                )
              "
              :status="progressStatus(scope.row.status)"
            ></el-progress>
          </el-tooltip>
        </template>
      </el-table-column>

      <el-table-column
        prop="status"
        header-align="center"
        align="center"
        label="状态"
      >
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === -1" size="small" type="danger"
            >失败
          </el-tag>
          <el-tag v-if="scope.row.status === 1" size="small" type="warning"
            >运行中
          </el-tag>
          <el-tag v-if="scope.row.status === 2" size="small" type="success"
            >已完成
          </el-tag>
          <el-tag v-if="scope.row.status === 3" size="small" type="info"
            >已暂停
          </el-tag>
        </template>
      </el-table-column>

      <!-- 新增处理时间信息列 -->
      <el-table-column header-align="center" align="center" label="耗时">
        <template slot-scope="scope">
          <div class="time-info">
            <div v-if="scope.row.status === 2">
              总耗时: {{ scope.row.processTime || "-" }}
            </div>
            <div v-if="scope.row.status === 1">
              当前耗时: {{ scope.row.processTime || "-" }}
            </div>
            <div v-if="scope.row.status === 1">
              处理速度: {{ scope.row.processSpeed || "-" }} 条/分钟
            </div>
            <div v-if="scope.row.status === 1">
              预计剩余: {{ scope.row.processLeftTime || "-" }}
            </div>
          </div>
        </template>
      </el-table-column>

      <!-- 新增处理时间信息列 -->
      <el-table-column header-align="center" align="center" label="Token消耗">
        <template slot-scope="scope">
          <div class="time-info">
            <div>Prompt Tokens: {{ scope.row.promptTokens || "-" }}</div>
            <div>
              Completion Tokens: {{ scope.row.completionTokens || "-" }}
            </div>
            <div>Total Tokens: {{ scope.row.totalTokens || "-" }}</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        prop="create_time"
        header-align="center"
        align="center"
        width="160"
        sortable="custom"
        label="创建时间"
      >
        <template slot-scope="scope">
          <div>{{ scope.row.createTime | dateFrm }}</div>
        </template>
      </el-table-column>
      <el-table-column
        header-align="center"
        width="300"
        align="center"
        label="操作"
      >
        <template slot-scope="scope">
          <!-- 暂停按钮 - 只在运行中状态显示 -->
          <el-link
            v-if="scope.row.status === 1"
            type="warning"
            :underline="false"
            @click="pauseTask(scope.row)"
            >暂停
          </el-link>

          <!-- 恢复按钮 - 只在暂停状态显示 -->
          <el-link
            v-if="scope.row.status === 3"
            type="success"
            :underline="false"
            @click="resumeTask(scope.row)"
            >恢复
          </el-link>

          <!-- 分隔符 -->
          <el-divider
            direction="vertical"
            v-if="
              (scope.row.status === 1 || scope.row.status === 3) &&
              (scope.row.status === -1 || scope.row.status === 2)
            "
          ></el-divider>

          <!-- 下载错误日志按钮 -->
          <el-link
            v-if="scope.row.status === -1"
            type="primary"
            :underline="false"
            @click="downloadErrorLog(scope.row)"
            >下载错误日志
          </el-link>

          <!-- 下载结果按钮 -->
          <el-link
            v-if="scope.row.status === 2"
            type="primary"
            :underline="false"
            @click="download(scope.row)"
            >下载结果
          </el-link>

          <!-- 分隔符 -->
          <el-divider
            direction="vertical"
            v-if="scope.row.status === 2"
          ></el-divider>

          <!-- 应用结果按钮 -->
          <el-link
            v-if="scope.row.status === 2"
            type="success"
            :underline="false"
            @click="applyResult(scope.row)"
            >预标注结果
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    >
    </el-pagination>

    <!-- 任务详情对话框 -->
    <el-dialog title="任务详情" :visible.sync="taskDetailVisible" width="50%">
      <el-form label-width="120px">
        <el-form-item label="任务名称">
          <span>{{ taskDetail.name }}</span>
        </el-form-item>
        <el-form-item label="项目名称">
          <span>{{ taskDetail.projectName }}</span>
        </el-form-item>
        <el-form-item label="金标准批次">
          <span>{{ taskDetail.sourceBatchName }}</span>
        </el-form-item>
        <el-form-item label="金标准文献ID">
          <el-input
            type="textarea"
            :rows="4"
            v-model="sourceArticleIdsStr"
            readonly
          ></el-input>
        </el-form-item>
        <el-form-item label="AI标注批次">
          <span>{{ taskDetail.batchName }}</span>
        </el-form-item>
        <el-form-item label="AI标注文献ID">
          <el-input
            type="textarea"
            :rows="4"
            v-model="articleIdsStr"
            readonly
          ></el-input>
        </el-form-item>
        <el-form-item label="Prompt">
          <el-input
            type="textarea"
            :rows="6"
            v-model="promptStr"
            readonly
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="taskDetailVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <!-- 下载格式选择对话框 -->
    <el-dialog
      title="选择下载格式"
      :visible.sync="downloadDialogVisible"
      width="400px"
      custom-class="download-dialog"
    >
      <div style="text-align: center">
        <p class="mb-3">请选择要下载的文件格式：</p>
        <el-button
          type="primary"
          icon="el-icon-document"
          size="medium"
          style="margin-right: 20px; width: 120px"
          @click="downloadFile('json')"
        >
          JSON格式
        </el-button>
        <el-button
          type="success"
          icon="el-icon-s-grid"
          size="medium"
          style="width: 120px"
          @click="downloadFile('excel')"
        >
          Excel格式
        </el-button>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="downloadDialogVisible = false">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import moment from "moment";
import FileSaver from "file-saver";
import Thread from "@/utils/thread";

export default {
  name: "ai-pre-anno",
  data() {
    return {
      thread: new Thread({
        start: () => {
          this.getDataList();
        },
        stop: () => {
          console.log("结束轮询");
        },
        time: 15000
      }),
      formInline: {
        name: "",
        status: "",
        orderBy: undefined,
        isAsc: undefined,
        date: []
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 20,
      totalPage: 5,
      dataListLoading: false,
      taskDetailVisible: false,
      downloadDialogVisible: false,
      currentDownloadRow: null,
      taskDetail: {},
      sourceArticleIdsStr: "",
      articleIdsStr: "",
      promptStr: ""
    };
  },
  filters: {
    dateFrm: function (value) {
      return moment(value).format("YYYY-MM-DD HH:mm:ss");
    }
  },
  mounted() {
    // 页面加载时自动调用获取数据列表
    this.$nextTick(() => {
      this.getDataList();
      this.thread.run();
    });
  },
  //销毁前停止
  beforeDestroy() {
    this.thread.stop();
  },
  methods: {
    getDataList() {
      // 获取数据列表的方法，模拟加载效果
      this.dataListLoading = true;
      this.$http({
        url: this.$http.adornUrl("/aiPreAnnoTask/list"),
        method: "get",
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          orderBy: this.formInline.orderBy,
          isAsc: this.formInline.isAsc,
          name: this.formInline.name,
          status: this.formInline.status,
          startDate:
            (this.formInline?.date != null && this.formInline?.date[0]) ||
            undefined,
          endDate:
            (this.formInline?.date != null && this.formInline?.date[1]) ||
            undefined
        })
      })
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.dataList = data.page.list;
            this.totalPage = data.page.totalCount;
          } else {
            this.dataList = [];
            this.totalPage = 0;
          }
        })
        .finally(() => {
          this.dataListLoading = false;
        });
    },
    progressStatus(status) {
      if (status === -1) return "exception";
      if (status === 2) return "success";
      return null; // 返回null而不是空字符串
    },
    sortChange(column) {
      if (column.order === "descending") {
        this.formInline.orderBy = column.prop;
        this.formInline.isAsc = false;
      } else {
        this.formInline.orderBy = column.prop;
        this.formInline.isAsc = true;
      }
      // 什么排序都不选择，恢复默认
      if (column.order == null) {
        this.formInline.orderBy = undefined;
        this.formInline.isAsc = undefined;
      }
      this.pageIndex = 1;
      this.getDataList();
      this.getDataList();
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val;
      this.pageIndex = 1;
      this.getDataList();
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val;
      this.getDataList();
    },
    // 新增 - 修改为路由跳转
    addHandle() {
      this.$router.push("/tool/ai-pre-anno/add");
    },
    // 结果预览
    previewResult(row) {
      // 预览结果的方法
      this.$http({
        url: this.$http.adornUrl("/aiPreAnnoTask/getResult"),
        params: this.$http.adornParams({
          id: row.id
        }),
        method: "get"
      }).then(({ data }) => {
        if (data.code === 0) {
          console.log(data.data);
        }
      });
    },
    // 结果应用
    applyResult(row) {
      // 确认对话框
      this.$confirm(
        `确认是否将AI预标注数据应用到【${row.projectName}】项目下，确认后能在此项目的预标注数据导入中看【ai_anno_${row.name}_${row.id}.json】记录`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      )
        .then(() => {
          // 显示加载动画
          const loading = this.$loading({
            lock: true,
            text: "应用中...",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)"
          });

          // 发送请求
          this.$http({
            url: this.$http.adornUrl("/aiPreAnnoTask/applyPreAnno"),
            method: "get",
            params: this.$http.adornParams({
              id: row.id
            })
          })
            .then(({ data }) => {
              loading.close();
              if (data && data.code === 0) {
                this.$message({
                  type: "success",
                  message: "应用成功"
                });
              } else {
                this.$message.error(data.msg || "应用失败");
              }
            })
            .catch((error) => {
              loading.close();
              console.error(error);
              this.$message.error("应用失败");
            });
        })
        .catch(() => {
          // 用户取消操作
        });
    },
    download(row) {
      // 打开下载格式选择对话框（仅用于下载结果）
      this.currentDownloadRow = row;
      this.downloadDialogVisible = true;
    },

    downloadErrorLog(row) {
      // 直接下载错误日志，无需弹窗选择格式
      const loading = this.$loading({
        lock: true,
        text: "下载中...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)"
      });

      this.$http({
        url: this.$http.adornUrl("/aiPreAnnoTask/download"),
        params: this.$http.adornParams({
          id: row.id
        }),
        method: "get",
        responseType: "blob"
      })
        .then((resp) => {
          if (resp.headers["content-type"] !== "application/octet-stream") {
            const err = "文件下载失败";
            throw err;
          }
          FileSaver.saveAs(resp.data, `ai_anno_${row.name}_${row.id}.log`);

          this.$message({
            type: "success",
            message: "下载成功"
          });
        })
        .catch((err) => {
          this.$message.error(err);
        })
        .finally(() => {
          loading.close();
        });
    },

    downloadFile(type) {
      const row = this.currentDownloadRow;
      if (!row) return;

      // 关闭对话框
      this.downloadDialogVisible = false;

      // 显示加载动画
      const loading = this.$loading({
        lock: true,
        text: "下载中...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)"
      });

      this.$http({
        url: this.$http.adornUrl("/aiPreAnnoTask/download"),
        params: this.$http.adornParams({
          id: row.id,
          type: type // 添加type参数，指定下载格式
        }),
        method: "get",
        responseType: "blob"
      })
        .then((resp) => {
          if (resp.headers["content-type"] !== "application/octet-stream") {
            const err = "文件下载失败";
            throw err;
          }

          // 根据类型设置不同的文件扩展名
          const fileExtension = type === "excel" ? "xlsx" : "json";
          FileSaver.saveAs(
            resp.data,
            `ai_anno_${row.name}_${row.id}.${fileExtension}`
          );

          this.$message({
            type: "success",
            message: "下载成功"
          });
        })
        .catch((err) => {
          this.$message.error(err);
        })
        .finally(() => {
          loading.close();
        });
    },
    // 显示任务详情
    showTaskDetail(row) {
      this.taskDetail = row;
      this.sourceArticleIdsStr = row.sourceArticleIds
        ? row.sourceArticleIds.join("\n")
        : "";
      this.articleIdsStr = row.articleIds ? row.articleIds.join("\n") : "";
      this.promptStr = row.prompt || "";
      this.taskDetailVisible = true;
    },
    // 暂停任务
    pauseTask(row) {
      this.$confirm(`确认暂停任务【${row.name}】吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          // 显示加载动画
          const loading = this.$loading({
            lock: true,
            text: "暂停中...",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)"
          });

          this.$http({
            url: this.$http.adornUrl("/aiPreAnnoTask/pause"),
            method: "post",
            params: this.$http.adornParams({
              id: row.id
            })
          })
            .then(({ data }) => {
              loading.close();
              if (data && data.code === 0) {
                this.$message({
                  type: "success",
                  message: "任务已暂停"
                });
                this.getDataList(); // 刷新列表
              } else {
                this.$message.error(data.msg || "暂停失败");
              }
            })
            .catch((error) => {
              loading.close();
              console.error(error);
              this.$message.error("暂停失败");
            });
        })
        .catch(() => {
          // 用户取消操作
        });
    },
    // 恢复任务
    resumeTask(row) {
      this.$confirm(`确认恢复任务【${row.name}】吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          // 显示加载动画
          const loading = this.$loading({
            lock: true,
            text: "恢复中...",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)"
          });

          this.$http({
            url: this.$http.adornUrl("/aiPreAnnoTask/resume"),
            method: "post",
            params: this.$http.adornParams({
              id: row.id
            })
          })
            .then(({ data }) => {
              loading.close();
              if (data && data.code === 0) {
                this.$message({
                  type: "success",
                  message: "任务已恢复"
                });
                this.getDataList(); // 刷新列表
              } else {
                this.$message.error(data.msg || "恢复失败");
              }
            })
            .catch((error) => {
              loading.close();
              console.error(error);
              this.$message.error("恢复失败");
            });
        })
        .catch(() => {
          // 用户取消操作
        });
    }
  }
};
</script>

<style scoped>
.el-button.is-circle {
  padding: 5px;
}
</style>
