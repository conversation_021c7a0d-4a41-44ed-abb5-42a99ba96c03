<template>
  <div class="ai-pre-anno-add">
    <el-row>
      <el-col :span="2">
        <div class="back-head" style="margin: 10px auto">
          <a class="go-back" href="javascript:0;" @click="$router.go(-1)">
            <i class="el-icon-arrow-left"></i>返回
          </a>
        </div>
      </el-col>
    </el-row>
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      label-width="120px"
    >
      <!-- 任务名称 -->
      <el-form-item label="任务名称:" prop="name">
        <el-input
          v-model="dataForm.name"
          placeholder="请输入任务名称"
          style="width: 500px"
        ></el-input>
      </el-form-item>

      <!-- 金标准来源 -->
      <el-form-item label="金标准来源:" prop="goldStandard">
        <el-cascader
          v-model="dataForm.goldStandard"
          :props="cascaderProps"
          clearable
          filterable
          placeholder="请选择金标准来源"
          @change="handleGoldStandardChange"
          style="width: 500px"
        ></el-cascader>
      </el-form-item>

      <!-- 金标准文献表格 -->
      <el-form-item label="金标准文献:" prop="goldArticles">
        <div
          class="table-container"
          :class="{ 'disabled-container': goldForm.tableDisabled }"
        >
          <!-- 搜索栏 -->
          <el-form
            :inline="true"
            :model="goldForm.search"
            class="demo-form-inline"
          >
            <el-form-item label="文书编号">
              <el-input
                v-model="goldForm.search.articleId"
                placeholder="输入文书编号进行搜索"
                clearable
                :disabled="goldForm.tableDisabled"
              ></el-input>
            </el-form-item>
            <el-form-item label="文书标题">
              <el-input
                v-model="goldForm.search.articleName"
                placeholder="输入文书标题进行搜索"
                clearable
                :disabled="goldForm.tableDisabled"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button
                @click="searchGoldArticles"
                icon="el-icon-search"
                :disabled="goldForm.tableDisabled"
                >查询
              </el-button>
              <el-button
                @click="resetGoldSearch"
                plain
                type="info"
                icon="el-icon-refresh-left"
                :disabled="goldForm.tableDisabled"
              >
                重置
              </el-button>
            </el-form-item>
            <el-checkbox
              v-model="dataForm.sourceSelectAllPage"
              @change="handleSourceSelectAllPageChange"
              size="large"
            >
              全选所有页
            </el-checkbox>
          </el-form>

          <!-- 表格 -->
          <el-table
            ref="goldTable"
            v-loading="goldForm.loading"
            :data="goldForm.tableData"
            border
            @selection-change="goldSelectionChange"
            @sort-change="goldSortChange"
            style="width: 100%; border-radius: 8px"
            :header-row-style="{ height: '44px' }"
            :header-cell-style="{
              background: 'rgb(246 247 249)',
              padding: '0px'
            }"
            max-height="500px"
            :row-key="(row) => row.articleId"
          >
            <el-table-column
              :reserve-selection="true"
              type="selection"
              header-align="center"
              align="center"
              width="50"
              :selectable="
                () => {
                  return !dataForm.sourceSelectAllPage;
                }
              "
            ></el-table-column>
            <el-table-column
              width="200"
              prop="articleId"
              sortable="custom"
              :show-overflow-tooltip="true"
              label="文书编号"
            ></el-table-column>
            <el-table-column
              prop="articleName"
              sortable="custom"
              :show-overflow-tooltip="true"
              label="文书标题"
            ></el-table-column>
            <el-table-column
              width="100"
              prop="step"
              align="center"
              sortable="custom"
              label="状态"
            >
              <template slot-scope="scope">
                <el-tag v-if="scope.row.step === 0" size="small" type="info"
                  >待标注
                </el-tag>
                <el-tag
                  v-else-if="scope.row.step === 1 || scope.row.step === 5"
                  size="small"
                  type="primary"
                  >标注中
                </el-tag>
                <el-tag
                  v-else-if="scope.row.step === 2 || scope.row.step === 6"
                  size="small"
                  type="warning"
                  >待审核
                </el-tag>
                <el-tag
                  v-else-if="scope.row.step === 3"
                  size="small"
                  type="danger"
                  >审核中
                </el-tag>
                <el-tag
                  v-else-if="scope.row.step === 4"
                  size="small"
                  type="success"
                  >已验收
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column
              width="160"
              prop="updateTime"
              label="更新时间"
              align="center"
            ></el-table-column>
            <template slot="empty">
              <div class="empty-text">
                {{ goldForm.batchId ? "暂无数据" : "请选择金标准来源" }}
              </div>
            </template>
          </el-table>

          <!-- 分页 -->
          <el-pagination
            @size-change="goldSizeChange"
            @current-change="goldCurrentChange"
            :current-page="goldForm.pageIndex"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="goldForm.pageSize"
            :total="goldForm.total"
            layout="total, sizes, prev, pager, next, jumper"
          ></el-pagination>
        </div>
      </el-form-item>
      <el-form-item label="">
        <el-button
          type="primary"
          @click="handleGenPrompt"
          :disabled="
            dataForm.sourceArticleIds.length === 0 &&
            !dataForm.sourceSelectAllPage
          "
          >生成prompt
        </el-button>
        <span class="ml-2" v-if="dataForm.prompt && tokenCount > 0">
          当前Prompt Token数量: {{ tokenCount }}
        </span>
      </el-form-item>

      <!-- Prompt文本区域 -->
      <el-form-item label="Prompt:" prop="prompt">
        <el-input
          type="textarea"
          v-model="dataForm.prompt"
          :rows="10"
          placeholder="请先选择金标准来源，勾选金标准文献，然后点击'生成prompt'按钮自动生成提示信息"
          :disabled="promptInputDisabled"
        ></el-input>
      </el-form-item>

      <!-- AI模型选择 -->
      <el-form-item label="AI模型:" prop="aiModel">
        <el-select
          clearable
          v-model="dataForm.aiModel"
          placeholder="请选择AI模型"
          style="width: 500px"
          @change="handleAiModelChange"
        >
          <el-option label="Azure GPT-4o" value="gpt-4o"></el-option>
          <el-option
            label="official-deepseek-v3(model=deepseek-chat)"
            value="official-deepseek-v3"
          ></el-option>
          <el-option
            label="maas-deepseek-v3(model=DeepSeek-V3)"
            value="maas-deepseek-v3"
          ></el-option>
        </el-select>
      </el-form-item>

      <!-- Endpoint输入框 -->
      <el-form-item label="Endpoint:" prop="endpoint">
        <el-input
          v-model="dataForm.endpoint"
          placeholder="请输入Endpoint"
          style="width: 500px"
        ></el-input>
      </el-form-item>

      <!-- API Key输入框 -->
      <el-form-item label="API Key:" prop="apiKey">
        <el-input
          v-model="dataForm.apiKey"
          placeholder="请输入API Key"
          style="width: 500px"
        ></el-input>
      </el-form-item>

      <el-form-item label="Chunk Size:" prop="chunkSize">
        <el-input
          v-model="dataForm.chunkSize"
          type="number"
          min="100"
          max="120000"
          placeholder="请输入Chunk Size (最小100)"
          style="width: 500px"
        ></el-input>
        <el-tooltip
          content="当单个文章过长时会对文本进行切割，chunksize 代表文章切割段落的最大长度是多少token（token长度使用tiktoken gpt4o计算，如果使用其他模型请自行估算），当标注的文章较长时，chunksize在1000~4000范围较为合理！"
          placement="top"
          effect="dark"
        >
          <i
            class="el-icon-question"
            style="margin-left: 8px; color: #909399; cursor: help"
          ></i>
        </el-tooltip>
      </el-form-item>

      <!-- AI预标注批次 -->
      <el-form-item label="AI预标注批次:" prop="batchId">
        <el-select
          v-model="dataForm.batchId"
          placeholder="请先选择金标准来源后，再选择AI预标注批次"
          filterable
          clearable
          style="width: 500px"
          :disabled="aiBatchSelectDisabled"
          @change="handleAiPreAnnoChange"
        >
          <el-option
            v-for="item in aiBatchOptions"
            :key="item.batchId"
            :label="item.name"
            :value="item.batchId"
          ></el-option>
        </el-select>
      </el-form-item>

      <!-- AI预标注文书表格 -->
      <el-form-item label="预标注文书:" prop="aiArticles">
        <div
          class="table-container"
          :class="{ 'disabled-container': aiForm.tableDisabled }"
        >
          <!-- 搜索栏 -->
          <el-form
            :inline="true"
            :model="aiForm.search"
            class="demo-form-inline"
          >
            <el-form-item label="文书编号">
              <el-input
                v-model="aiForm.search.articleId"
                placeholder="输入文书编号进行搜索"
                clearable
                :disabled="aiForm.tableDisabled"
              ></el-input>
            </el-form-item>
            <el-form-item label="文书标题">
              <el-input
                v-model="aiForm.search.articleName"
                placeholder="输入文书标题进行搜索"
                clearable
                :disabled="aiForm.tableDisabled"
              ></el-input>
            </el-form-item>
            <el-form-item label="文书状态">
              <el-select
                v-model="aiForm.search.activeStep"
                placeholder="请选择状态"
                style="width: 200px"
                :disabled="aiForm.tableDisabled"
              >
                <el-option label="全部" value="all"></el-option>
                <el-option label="待标注" value="unmarked"></el-option>
                <el-option label="标注中" value="noting"></el-option>
                <el-option label="待审核" value="marked"></el-option>
                <el-option label="审核中" value="reviewing"></el-option>
                <el-option label="已验收" value="reviewed"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button
                @click="searchAiArticles"
                icon="el-icon-search"
                :disabled="aiForm.tableDisabled"
                >查询
              </el-button>
              <el-button
                @click="resetAiSearch"
                plain
                type="info"
                icon="el-icon-refresh-left"
                :disabled="aiForm.tableDisabled"
              >
                重置
              </el-button>
            </el-form-item>
            <el-checkbox
              v-model="dataForm.selectAllPage"
              @change="handleSelectAllPageChange"
              size="large"
            >
              全选所有页
            </el-checkbox>
          </el-form>

          <!-- 表格 -->
          <el-table
            ref="aiTable"
            v-loading="aiForm.loading"
            :data="aiForm.tableData"
            border
            @selection-change="aiSelectionChange"
            @sort-change="aiSortChange"
            style="width: 100%; border-radius: 8px"
            :header-row-style="{ height: '44px' }"
            :header-cell-style="{
              background: 'rgb(246 247 249)',
              padding: '0px'
            }"
            :row-key="(row) => row.articleId"
            max-height="500px"
          >
            <el-table-column
              :reserve-selection="true"
              type="selection"
              header-align="center"
              align="center"
              width="50"
              :selectable="
                () => {
                  return !dataForm.selectAllPage;
                }
              "
            ></el-table-column>
            <el-table-column
              width="200"
              prop="articleId"
              sortable="custom"
              :show-overflow-tooltip="true"
              label="文书编号"
            ></el-table-column>
            <el-table-column
              prop="articleName"
              sortable="custom"
              :show-overflow-tooltip="true"
              label="文书标题"
            ></el-table-column>
            <el-table-column
              width="100"
              prop="step"
              align="center"
              label="状态"
            >
              <template slot-scope="scope">
                <el-tag v-if="scope.row.step === 0" size="small" type="info"
                  >待标注
                </el-tag>
                <el-tag
                  v-else-if="scope.row.step === 1 || scope.row.step === 5"
                  size="small"
                  type="primary"
                  >标注中
                </el-tag>
                <el-tag
                  v-else-if="scope.row.step === 2 || scope.row.step === 6"
                  size="small"
                  type="warning"
                  >待审核
                </el-tag>
                <el-tag
                  v-else-if="scope.row.step === 3"
                  size="small"
                  type="danger"
                  >审核中
                </el-tag>
                <el-tag
                  v-else-if="scope.row.step === 4"
                  size="small"
                  type="success"
                  >已验收
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column
              width="160"
              prop="updateTime"
              label="更新时间"
              align="center"
            ></el-table-column>
            <template slot="empty">
              <div class="empty-text">
                {{ aiForm.batchId ? "暂无数据" : "请选择AI预标注批次" }}
              </div>
            </template>
          </el-table>

          <!-- 分页 -->
          <el-pagination
            @size-change="aiSizeChange"
            @current-change="aiCurrentChange"
            :current-page="aiForm.pageIndex"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="aiForm.pageSize"
            :total="aiForm.total"
            layout="total, sizes, prev, pager, next, jumper"
          ></el-pagination>
        </div>
      </el-form-item>

      <!-- 提交按钮 -->
      <el-form-item>
        <div class="form-buttons-center">
          <el-button type="primary" @click="confirmSubmit">确 认</el-button>
          <el-button @click="resetForm">重 置</el-button>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: "ai-pre-anno-add",
  data() {
    return {
      dataForm: {
        name: "",
        projectId: "",
        sourceBatchId: "",
        goldStandard: [],
        prompt: "",
        sourceArticleIds: [],
        batchId: "",
        articleIds: [],
        sourceSelectAllPage: false,
        selectAllPage: false,
        aiModel: "",
        endpoint: "",
        chunkSize: 1000,
        apiKey: ""
      },
      dataRule: {
        name: [
          { required: true, message: "任务名称不能为空", trigger: "blur" }
        ],
        goldStandard: [
          { required: true, message: "请选择金标准来源", trigger: "change" }
        ],
        prompt: [
          { required: true, message: "Prompt不能为空", trigger: "blur" }
        ],
        batchId: [
          { required: true, message: "请选择AI预标注批次", trigger: "change" }
        ],
        aiModel: [
          { required: true, message: "请选择AI模型", trigger: "change" }
        ],
        endpoint: [
          { required: true, message: "Endpoint不能为空", trigger: "blur" }
        ],
        apiKey: [
          { required: true, message: "API Key不能为空", trigger: "blur" }
        ],
        chunkSize: [
          { required: true, message: "Chunk Size不能为空", trigger: "blur" },
          {
            validator: (_, value, callback) => {
              if (!value) {
                callback(new Error("Chunk Size不能为空"));
                return;
              }
              const chunkSize = parseInt(value);
              if (isNaN(chunkSize) || chunkSize < 100) {
                callback(new Error("Chunk Size必须大于等于100"));
                return;
              }
              // 检查prompt token + chunksize是否超过120000
              if (this.tokenCount + chunkSize > 120000) {
                callback(
                  new Error(
                    `Prompt Token(${this.tokenCount}) + Chunk Size(${chunkSize}) 不能超过120000`
                  )
                );
                return;
              }
              callback();
            },
            trigger: "blur"
          }
        ]
      },
      // 级联选择器配置
      cascaderProps: {
        lazy: true,
        lazyLoad: this.lazyLoadOptions,
        value: "value",
        label: "label",
        children: "children"
      },

      // 项目选项
      entityLabelData: [],

      // 金标准相关参数整合
      goldForm: {
        batchId: undefined,
        tableDisabled: true,
        search: {
          articleId: "",
          articleName: "",
          activeStep: "all"
        },
        loading: false,
        tableData: [],
        pageIndex: 1,
        pageSize: 10,
        total: 0,
        orderBy: undefined,
        isAsc: undefined
      },

      // AI预标注相关参数整合
      aiForm: {
        batchId: undefined,
        tableDisabled: true,
        isSelectAllPage: false,
        search: {
          articleId: "",
          articleName: "",
          activeStep: "all"
        },
        loading: false,
        tableData: [],
        pageIndex: 1,
        pageSize: 10,
        total: 0,
        orderBy: undefined,
        isAsc: undefined
      },

      aiBatchSelectDisabled: true,
      aiBatchOptions: [],
      promptInputDisabled: true,
      genPromptBtnDisabled: false,
      tokenCount: 0,
      tokenCountTimer: null
    };
  },
  computed: {
    roleId() {
      return this.$store.state.user.roleId;
    }
  },
  watch: {
    "dataForm.prompt": {
      handler(newVal) {
        this.calculateTokenCount(newVal);
      },
      immediate: true
    }
  },
  created() {
    // 初始化时不再需要主动加载项目列表，改由级联选择器的lazyLoad处理
    // 禁用AI预标注批次选择框，直到金标准来源选择完成
    this.aiBatchSelectDisabled = true;
    // 默认禁用prompt输入框，直到生成prompt完成
    this.promptInputDisabled = true;
    // 生成prompt按钮初始状态不禁用(但受到sourceArticleIds长度的控制)
    this.genPromptBtnDisabled = false;
  },
  beforeDestroy() {
    // 清理定时器
    if (this.tokenCountTimer) {
      clearTimeout(this.tokenCountTimer);
    }
  },
  methods: {
    // 级联选择器懒加载方法
    lazyLoadOptions(node, resolve) {
      // 如果是根节点，加载项目列表（第一级）
      if (node.level === 0) {
        this.$http({
          url: this.$http.adornUrl("/project/list"),
          method: "get",
          params: this.$http.adornParams({
            page: 0,
            limit: 1000,
            roleId: this.$store.state.user.roleId
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            let options = data.page.list.map((it) => {
              return {
                value: it.projectId,
                label: it.name,
                leaf: false
              };
            });
            resolve(options);
          }
        });
      } else if (node.level === 1) {
        // 如果是项目节点，加载批次（第二级）
        const projectId = node.value;
        this.$http({
          url: this.$http.adornUrl("/batch/list"),
          method: "get",
          params: this.$http.adornParams({
            page: 1,
            limit: -1,
            projectId: projectId
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            let options = data.page.list.map((it) => {
              return {
                value: it.batchId,
                label: it.name,
                leaf: true
              };
            });
            resolve(options);
          }
        });
      }
    },

    // 金标准来源改变
    handleGoldStandardChange(value) {
      if (!value || value.length === 0) {
        // 如果清空选择，执行清空操作
        this.resetGoldStandardData();
        // 同时重置AI预标注相关数据
        this.resetAiPreAnnoData();
        return;
      }

      if (value.length === 2) {
        // 如果选择了第二级（批次），加载该批次下的文书
        this.goldForm.batchId = value[1];
        this.dataForm.projectId = value[0]; // 保存项目ID

        // 清除历史搜索条件
        this.goldForm.search = {
          articleId: "",
          articleName: ""
        };
        this.goldForm.pageIndex = 1;
        this.goldForm.pageSize = 10;
        this.goldForm.orderBy = undefined;
        this.goldForm.isAsc = undefined;

        // 加载表格数据
        this.loadGoldArticles();

        // 加载AI预标注批次选项
        this.loadAiBatchOptions(value[0]);
      }
    },

    // 重置金标准相关数据
    resetGoldStandardData() {
      // 禁用表格
      this.goldForm.tableDisabled = true;
      // 清空表格数据
      this.goldForm.tableData = [];
      // 清空批次ID
      this.goldForm.batchId = undefined;
      // 取消全选所有页
      this.dataForm.sourceSelectAllPage = false;
      // 清空选中的文书
      this.dataForm.sourceArticleIds = [];
      if (this.$refs.goldTable) {
        this.$refs.goldTable.clearSelection();
      }
    },

    // 重置AI预标注相关数据
    resetAiPreAnnoData() {
      // 禁用下拉框
      this.aiBatchSelectDisabled = true;
      // 清空下拉选项
      this.aiBatchOptions = [];
      // 清空选中的批次
      this.dataForm.batchId = "";
      // 重置表格相关数据
      this.resetAiTableData();
    },

    // 加载AI预标注批次选项
    loadAiBatchOptions(projectId) {
      this.aiBatchSelectDisabled = true;
      this.aiBatchOptions = [];
      this.dataForm.batchId = "";

      this.$http({
        url: this.$http.adornUrl("/batch/list"),
        method: "get",
        params: this.$http.adornParams({
          page: 1,
          limit: -1,
          projectId: projectId
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.aiBatchOptions = data.page.list;
          this.aiBatchSelectDisabled = false;
        }
      });
    },

    // 加载金标准文书
    loadGoldArticles() {
      this.goldForm.loading = true;

      // 解除表格禁用状态
      this.goldForm.tableDisabled = false;

      const params = {
        page: this.goldForm.pageIndex,
        limit: this.goldForm.pageSize,
        roleId: this.roleId,
        batchId: this.goldForm.batchId,
        activeStep: "reviewed",
        orderBy: this.goldForm.orderBy,
        isAsc: this.goldForm.isAsc,
        articleId: this.goldForm.search.articleId,
        articleName: this.goldForm.search.articleName
      };

      this.$http({
        url: this.$http.adornUrl("/batch/getArticleList"),
        method: "get",
        params: this.$http.adornParams(params)
      })
        .then(({ data }) => {
          if (data && data.code === 0) {
            let result = data.data;
            this.goldForm.tableData = result.listData.list || [];
            this.goldForm.total = result.listData.totalCount;
          } else {
            this.goldForm.tableData = [];
            this.goldForm.total = 0;
          }
        })
        .finally(() => {
          this.goldForm.loading = false;
        });
    },

    // AI预标注批次改变
    handleAiPreAnnoChange(value) {
      if (!value) {
        // 如果清空选择，清空AI预标注表格数据
        this.resetAiTableData();
        return;
      }

      // 加载选中批次下的文书
      this.aiForm.batchId = value;

      // 清除历史搜索条件
      this.aiForm.search = {
        articleId: "",
        articleName: ""
      };
      this.aiForm.pageIndex = 1;
      this.aiForm.pageSize = 10;
      this.aiForm.orderBy = undefined;
      this.aiForm.isAsc = undefined;

      // 加载表格数据
      this.loadAiArticles();
    },

    // 重置AI预标注表格数据（不包括批次下拉框）
    resetAiTableData() {
      // 禁用表格
      this.aiForm.tableDisabled = true;
      // 清空表格数据
      this.aiForm.tableData = [];
      // 清空批次ID
      this.aiForm.batchId = undefined;
      // 取消全选所有页
      this.dataForm.selectAllPage = false;
      // 清空选中的文书
      this.dataForm.articleIds = [];
      if (this.$refs.aiTable) {
        this.$refs.aiTable.clearSelection();
      }
    },

    // 加载AI预标注文书
    loadAiArticles() {
      this.aiForm.loading = true;

      // 解除表格禁用状态
      this.aiForm.tableDisabled = false;

      // 构建请求参数
      const params = {
        page: this.aiForm.pageIndex,
        limit: this.aiForm.pageSize,
        roleId: this.roleId,
        batchId: this.aiForm.batchId,
        orderBy: this.aiForm.orderBy,
        isAsc: this.aiForm.isAsc,
        articleId: this.aiForm.search.articleId,
        articleName: this.aiForm.search.articleName,
        activeStep: this.aiForm.search.activeStep
      };

      this.$http({
        url: this.$http.adornUrl("/batch/getArticleList"),
        method: "get",
        params: this.$http.adornParams(params)
      })
        .then(({ data }) => {
          if (data && data.code === 0) {
            let result = data.data;
            this.aiForm.tableData = result.listData.list || [];
            this.aiForm.total = result.listData.totalCount;
          } else {
            this.aiForm.tableData = [];
            this.aiForm.total = 0;
          }
        })
        .finally(() => {
          this.aiForm.loading = false;
        });
    },

    // 金标准文书相关方法
    searchGoldArticles() {
      this.goldForm.pageIndex = 1;
      this.loadGoldArticles();
    },
    resetGoldSearch() {
      this.goldForm.search = {
        articleId: "",
        articleName: ""
      };
      this.goldForm.pageIndex = 1;
      this.goldForm.orderBy = undefined;
      this.goldForm.isAsc = undefined;
      // 重新加载数据
      this.loadGoldArticles();
    },
    goldSizeChange(val) {
      this.goldForm.pageSize = val;
      this.goldForm.pageIndex = 1;
      this.loadGoldArticles();
    },
    goldCurrentChange(val) {
      this.goldForm.pageIndex = val;
      this.loadGoldArticles();
    },
    goldSortChange(column) {
      if (column.order === "descending") {
        this.goldForm.orderBy = column.prop;
        this.goldForm.isAsc = false;
      } else {
        this.goldForm.orderBy = column.prop;
        this.goldForm.isAsc = true;
      }
      // 什么排序都不选择，恢复默认
      if (column.order == null) {
        this.goldForm.orderBy = undefined;
        this.goldForm.isAsc = undefined;
      }
      this.goldForm.pageIndex = 1;
      this.loadGoldArticles();
    },
    goldSelectionChange(selection) {
      this.dataForm.sourceArticleIds = selection.map((x) => x.articleId);
    },

    // AI预标注文书相关方法
    searchAiArticles() {
      this.aiForm.pageIndex = 1;
      this.loadAiArticles();
    },
    resetAiSearch() {
      this.aiForm.search = {
        articleId: "",
        articleName: ""
      };
      this.aiForm.pageIndex = 1;
      this.aiForm.orderBy = undefined;
      this.aiForm.isAsc = undefined;
      // 重新加载数据
      this.loadAiArticles();
    },
    aiSizeChange(val) {
      this.aiForm.pageSize = val;
      this.aiForm.pageIndex = 1;
      this.loadAiArticles();
    },
    aiCurrentChange(val) {
      this.aiForm.pageIndex = val;
      this.loadAiArticles();
    },
    aiSortChange(column) {
      if (column.order === "descending") {
        this.aiForm.orderBy = column.prop;
        this.aiForm.isAsc = false;
      } else {
        this.aiForm.orderBy = column.prop;
        this.aiForm.isAsc = true;
      }
      // 什么排序都不选择，恢复默认
      if (column.order == null) {
        this.aiForm.orderBy = undefined;
        this.aiForm.isAsc = undefined;
      }
      this.aiForm.pageIndex = 1;
      this.loadAiArticles();
    },
    handleSourceSelectAllPageChange() {
      this.$refs.goldTable.clearSelection();
    },
    handleSelectAllPageChange() {
      this.$refs.aiTable.clearSelection();
    },
    aiSelectionChange(selection) {
      this.dataForm.articleIds = selection.map((it) => it.articleId);
    },
    // 确认弹窗
    confirmSubmit() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          // 提交前的验证
          if (
            !this.dataForm.sourceSelectAllPage &&
            this.dataForm.sourceArticleIds.length === 0
          ) {
            this.$message.error("请至少选择一篇金标准文献");
            return;
          }
          if (
            !this.dataForm.selectAllPage &&
            this.dataForm.articleIds.length === 0
          ) {
            this.$message.error("请至少选择一篇预标注文章");
            return;
          }

          this.$confirm("确认提交AI预标注任务?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          })
            .then(() => {
              // 显示全局loading
              const loading = this.$loading({
                lock: true,
                text: "提交中...",
                spinner: "el-icon-loading",
                background: "rgba(0, 0, 0, 0.7)"
              });

              // 设置项目ID和批次ID
              this.dataForm.projectId = this.dataForm.goldStandard[0];
              this.dataForm.sourceBatchId = this.dataForm.goldStandard[1];
              this.dataForm.activeStep = this.aiForm.activeStep;

              this.$http({
                url: this.$http.adornUrl("/aiPreAnnoTask/create"),
                method: "post",
                data: this.$http.adornData(this.dataForm)
              })
                .then(({ data }) => {
                  loading.close();
                  if (data && data.code === 0) {
                    this.$message({
                      message: "提交成功",
                      type: "success"
                    });
                    this.$router.push("/tool-ai-pre-anno");
                  }
                })
                .catch((error) => {
                  loading.close();
                  this.$message.error("提交失败，请重试");
                  console.error(error);
                });
            })
            .catch(() => {
              // 用户取消提交
            });
        }
      });
    },

    // 重置表单
    resetForm() {
      this.$refs.dataForm.resetFields();
      this.dataForm = {
        name: "",
        projectId: "",
        sourceBatchId: "",
        goldStandard: [],
        prompt: "",
        sourceArticleIds: [],
        batchId: "",
        articleIds: [],
        sourceSelectAllPage: false,
        selectAllPage: false,
        aiModel: "",
        endpoint: "",
        chunkSize: 1000,
        apiKey: ""
      };

      // 重置金标准相关数据
      this.resetGoldStandardData();
      // 重置AI预标注相关数据
      this.resetAiPreAnnoData();

      // 禁用prompt输入框
      this.promptInputDisabled = true;
      // 恢复生成prompt按钮状态
      this.genPromptBtnDisabled = false;
    },
    handleGenPrompt() {
      // 禁用按钮防止重复点击
      this.genPromptBtnDisabled = true;

      // 显示loading
      const loading = this.$loading({
        lock: true,
        text: "生成中...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)"
      });

      this.dataForm.projectId = this.dataForm.goldStandard[0];
      this.dataForm.sourceBatchId = this.dataForm.goldStandard[1];
      // 注意这里不需要从aiPreAnno获取batchId了，因为已经直接在dataForm.batchId中

      this.$http({
        url: this.$http.adornUrl("/aiPreAnnoTask/getCleanEntityAnnoData"),
        method: "post",
        data: this.$http.adornData(this.dataForm)
      })
        .then(({ data }) => {
          if (data && data.code === 0) {
            let entityToSample = data.entityToSample;
            let entityLabels = data.entityLabels;
            let entityDescMap = new Map();
            for (let label of entityLabels) {
              if (label.description) {
                entityDescMap.set(label.name, label.description);
              }
            }
            // for (let it of data.data) {
            //   for (let en of it.entities) {
            //     if (entityToSample[en.label]) {
            //       entityToSample[en.label] = entityToSample[en.label].add(
            //         en.content
            //       );
            //     } else {
            //       let set = new Set();
            //       set.add(en.content);
            //       entityToSample[en.label] = set;
            //     }
            //   }
            // }
            let promptArr = [];
            promptArr.push(
              "请你使用中文回答，你是一位出色的中文医学实体识别专家，目前需要对中文医疗文本进行实体标注，需要标注的文本在最后，下面是标注的要求："
            );
            promptArr.push("1. 下面是需要标注的实体名称以及正确的实体示例：");
            for (const item of entityToSample) {
              let key = item.key;
              let value = item.value;
              let line = `实体名称：${key}`;
              if (entityDescMap.has(key)) {
                line = line + `，实体描述：${entityDescMap.get(key)}`;
              }
              if (value && value.length > 0) {
                line =
                  line +
                  `，例如：${Array.from(value)
                    .slice(0, 10)
                    .map((x) => `"${x}"`)
                    .join(",")}`;
              }
              promptArr.push(line);
            }
            promptArr.push("2. 处理规则：");
            promptArr.push(
              "- 请以JSON 格式返回识别出的医学实体及相关信息，每个实体需包含其类别及具体文本内容，任何满足条件的内容一旦出现，即需要立刻进行标注。"
            );
            promptArr.push(
              "- 每次出现的实体都要标注，不论是否在前文中出现过。"
            );
            promptArr.push(
              "- 不要重复输出相同位置的标注结果（即，不要重复标注已经标注过的文本段）。"
            );
            promptArr.push(
              "- 返回的JSON结果需要保证能够直接被json.loads()正确加载，不需要任何多余的描述信息，如果没有识别到任何实体，则返回[]，这样确保能直接被json.loads()正确加载。"
            );
            promptArr.push("- 请确保标注准确，不遗漏任何实体。");
            promptArr.push("- 输出的JSON格式请参考下方示例输出结果。");
            let i = 3; // 题号从3开始
            let sampleIndex = 1;

            for (let it of data.data) {
              promptArr.push(`${i++}. 示例${sampleIndex}的输入文本：`);
              promptArr.push(`\`\`\`${it.content}\`\`\``);

              promptArr.push(`${i++}. 示例${sampleIndex}的输出结果：`);
              promptArr.push(`${JSON.stringify(it.entities)}`);

              sampleIndex++;
            }
            promptArr.push(`${i}. 现在，请你根据以上要求对下面的文本进行标注:`);
            this.dataForm.prompt = promptArr.join("\n");

            // 启用prompt输入框
            this.promptInputDisabled = false;

            // 显示成功消息
            this.$message({
              message: "Prompt生成成功",
              type: "success"
            });
          } else {
            this.$message.error(data.msg || "生成Prompt失败");
          }
        })
        .catch((err) => {
          console.error(err);
          this.$message.error("生成Prompt失败");
        })
        .finally(() => {
          // 关闭loading
          loading.close();
          // 启用按钮
          this.genPromptBtnDisabled = false;
        });
    },
    handleAiModelChange(value) {
      if (value === "gpt-4o") {
        this.dataForm.endpoint = "https://southgene-openai-5.openai.azure.com/";
      } else if (value === "official-deepseek-v3") {
        this.dataForm.endpoint = "https://api.deepseek.com";
      } else if (value === "maas-deepseek-v3") {
        this.dataForm.endpoint =
          "https://maas-cn-southwest-2.modelarts-maas.com/deepseek-v3/v1";
      } else {
        this.dataForm.endpoint = "";
      }
    },
    // 计算Token数量
    calculateTokenCount(text) {
      if (!text || text.trim() === "") {
        this.tokenCount = 0;
        return;
      }

      // 防抖处理，避免频繁调用接口
      if (this.tokenCountTimer) {
        clearTimeout(this.tokenCountTimer);
      }

      this.tokenCountTimer = setTimeout(() => {
        this.$http({
          url: this.$http.adornUrl("/aiPreAnnoTask/countTiktoken"),
          method: "post",
          data: text,
          headers: {
            "Content-Type": "text/plain"
          }
        })
          .then(({ data }) => {
            if (data && data.code === 0) {
              this.tokenCount = data.data || 0;
            } else {
              console.error("Token计算失败:", data.msg);
              this.tokenCount = 0;
            }
          })
          .catch((error) => {
            console.error("Token计算接口调用失败:", error);
            this.tokenCount = 0;
          });
      }, 500); // 500ms防抖
    }
  }
};
</script>

<style scoped>
.ai-pre-anno-add {
  padding: 20px;
}

.table-container {
  margin-bottom: 20px;
  border: 1px solid #ebeef5;
  padding: 15px;
  border-radius: 4px;
}

.disabled-container {
  background-color: #f5f7fa;
  cursor: not-allowed;
  opacity: 0.6;
}

.el-pagination {
  margin-top: 15px;
  text-align: right;
}

.empty-text {
  color: #909399;
  font-size: 14px;
  line-height: 60px;
  text-align: center;
}

.form-buttons-center {
  text-align: center;
}
</style>
