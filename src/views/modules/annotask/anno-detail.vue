<template>
  <div v-loading="loading">
    <div v-if="visible === ''">
      <anno-navbar />
      <comm-annotation
        ref="commAnno"
        :note-id="noteId"
        :task-id="taskId"
        :annotator-id="annotatorId"
        :init-anno-type-switch="annoTypeSwitch"
        :init-batch-switch="batchSwitch"
        @nextArticle="getNextTask"
      ></comm-annotation>
    </div>
    <div v-else-if="visible === '加载中...'" class="article-tip">
      <h2>{{ visible }}</h2>
    </div>
    <div v-else>
      <el-empty style="height: 500px" :description="visible">
        <el-button plain size="small" @click="goBack()" icon="el-icon-back"
          >返回
        </el-button>
      </el-empty>
    </div>
  </div>
</template>

<script>
import CommAnnotation from "@/views/modules/annotation/comm-annotation.vue";
import AnnoNavbar from "@/views/modules/annotask/anno-navbar.vue";
import { AnnoEventsBus } from "@/utils/bus";
import { sleep, trimStr } from "@/utils";

export default {
  name: "anno-detail",
  components: { CommAnnotation, AnnoNavbar },
  data() {
    return {
      loading: false,
      visible: "加载中...",
      noteId: undefined,
      batchId: undefined,
      taskId: 0,
      annotatorId: undefined, // 审核员状态下选择的标注员ID
      editable: false, // 是否可编辑
      annoTypeSwitch: false,
      batchSwitch: false
    };
  },
  computed: {
    roleId() {
      return this.$store.state.user.roleId;
    }
  },
  created() {
    this.$store.commit("anno/setNoteId", null);
    this.$store.commit("anno/setBatch", {});

    this.batchId = Number(this.$route.query.batchId);
    this.noteId = Number(this.$route.query.noteId);
    if (!this.$route.query.editable) {
      this.$route.query.editable = this.editable;
    }
    // this.editable = this.$route.query.editable + "";
    this.editable = trimStr(this.$route.query.editable) === "true";
    this.annotatorId = this.$route.query.annotatorId;

    // 检查是否为历史版本模式
    const historyMode = trimStr(this.$route.query.historyMode) === "true";
    const discussionId = this.$route.query.discussionId;

    if (historyMode && discussionId) {
      // 设置历史版本模式的状态
      this.$store.commit("anno/setHistoryMode", {
        enabled: true,
        discussionId: discussionId
      });
      // 历史版本模式强制只读
      this.editable = false;
    }
  },
  mounted() {
    AnnoEventsBus.$off();
    this.visible = "加载中...";
    this.getTaskId();
    this.setDialogVisible(true);
  },
  destroyed() {
    AnnoEventsBus.$off();
    this.$store.commit("anno/setBatch", {});
  },
  methods: {
    checkPermission(noteStep, taskStep) {
      let tempEditable = true;
      if (taskStep === null || taskStep === undefined) {
        tempEditable = false;
      }
      // 只有 标注员是标注中 和 审核员是审核中 内容才能编辑
      if (
        // 符合下面条件的可以编辑
        !(
          (this.roleId === this.$RoleEnum.annotator && // 角色是标注员且状态是标注中 和 被打回
            (taskStep === this.$NoteEnum.noting ||
              taskStep === this.$NoteEnum.repulse)) ||
          (this.roleId === this.$RoleEnum.auditor && // 角色是审核员且是审核中
            (taskStep === this.$NoteEnum.reviewing ||
              (noteStep === this.$NoteEnum.marked && // Note是已标注，且文书是已修正正
                taskStep === this.$NoteEnum.corrected)))
        )
      ) {
        tempEditable = false;
      }
      // 项目管理员、项目观察员 不能编辑
      if (
        this.roleId === this.$RoleEnum.projectAdmin ||
        this.roleId === this.$RoleEnum.projectWatcher
      ) {
        tempEditable = false;
      }
      if (!this.editable) {
        tempEditable = false;
      }
      this.$store.commit("anno/setEditable", tempEditable);
      this.$store.commit("anno/setEditableByRoleAndStep", tempEditable);
    },
    getTaskId() {
      let data = {
        editable: this.editable,
        noteId: this.noteId,
        roleId: this.roleId,
        annotatorId: this.annotatorId
      };
      this.loading = true;
      this.$http({
        url: this.$http.adornUrl("/task/findTaskByNoteId"),
        method: "post",
        data: this.$http.adornData(data)
      })
        .then(({ data }) => {
          if (data && data.code === 0) {
            const taskVo = data.data;
            const task = taskVo?.task;
            this.checkPermission(taskVo?.step, taskVo?.taskStep);
            // 如果是标注员或者审核员
            if (
              this.editable &&
              (this.roleId === this.$RoleEnum.annotator ||
                this.roleId === this.$RoleEnum.auditor)
            ) {
              // 如果没有新任务
              if (!task) {
                this.visible =
                  "暂无待" +
                  (this.roleId === this.$RoleEnum.auditor ? "审核" : "标注") +
                  "任务";
                return;
              }
              // 得到的新任务和想标注的文书不一致
              if (this.noteId !== task?.noteId) {
                this.$message({
                  type: "warning",
                  message: "当前文书已经被其他标注员拉取，自动为您分配下一篇!"
                });
                this.$router.replace({
                  path: "/annotask/detail",
                  query: {
                    batchId: this.batchId,
                    noteId: task.noteId,
                    annotatorId: this.annotatorId,
                    fromType: trimStr(this.$route.query.fromType)
                  }
                });
                window.location.reload();
                return;
              }
            }
            this.taskId = task?.taskId || 0;

            this.$store.commit("anno/setNoteId", {
              noteId: this.noteId,
              taskId: this.taskId
            });
            this.$store.commit("anno/setNavbar", {
              projectName: taskVo.projectName,
              hasSpecFile: taskVo.hasSpecFile,
              batchName: taskVo.batchName,
              articleId: taskVo.articleId,
              invalid: taskVo.invalid,
              step: taskVo.step,
              status: taskVo.status,
              taskStep: taskVo.taskStep,
              taskStatus:
                taskVo.taskStatus === undefined
                  ? taskVo.status
                  : taskVo.taskStatus,
              repulseMsg: taskVo.repulseMsg,
              editable: this.editable,
              documentSource: taskVo.documentSource,
              auditorId: task?.auditor || null
            });
            this.visible = "";
            this.$nextTick(() => {
              this.$refs.commAnno.getArticle();
            });
          } else {
            this.$message({
              message: data.msg,
              type: "error",
              duration: 10000
            });
          }
        })
        .finally(() => {
          this.loadEnd();
        });
    },
    getNextTask() {
      let data = {
        batchId: this.batchId,
        roleId: this.roleId,
        annotatorId: this.annotatorId
      };
      this.$http({
        url: this.$http.adornUrl("/task/assignTask"),
        method: "post",
        data: this.$http.adornData(data)
      }).then(({ data }) => {
        if (data && data.code === 0) {
          const task = data.data;
          if (!task) {
            this.visible =
              "暂无待" + (this.roleId === 3 ? "审核" : "标注") + "文献";
            document
              .querySelectorAll(".el-loading-mask")
              .forEach((node) => node.parentNode.removeChild(node));
            return;
          }
          this.$router.replace({
            path: "/annotask/detail",
            query: {
              batchId: this.batchId,
              noteId: task.noteId,
              editable: true,
              annotatorId: this.annotatorId,
              fromType: trimStr(this.$route.query.fromType)
            }
          });
          window.location.reload();
        } else {
          this.$message({
            message: data.msg,
            type: "error",
            duration: 10000
          });
        }
      });
    },
    loadEnd() {
      sleep(700).then(() => {
        this.loading = false;
      });
    },
    setDialogVisible(v) {
      this.$store.commit("anno/setDialogVisible", v);
    },
    goBack() {
      this.$router.push({
        path: "/annotask-list"
      });
    }
  }
};
</script>

<style scoped>
.article-tip {
  margin-top: 10%;
  color: gray;
  text-align: center;
}
</style>
