<template>
  <div>
    <el-row :gutter="20">
      <el-col :span="4">
        <el-row>
          <el-input
            class="search"
            v-model="filterText"
            prefix-icon="el-icon-search"
            placeholder="请输入项目或批次检索"
            clearable
          ></el-input>
          <h1></h1>
          <el-tree
            style="overflow-y: auto; max-height: 80vh"
            :data="treeData"
            default-expand-all
            :current-node-key="selected.batchId"
            highlight-current
            @node-click="handleNodeClick"
            :filter-node-method="filterNode"
            :indent="treeProps['indent']"
            node-key="id"
            ref="tree"
          >
            <template v-slot="{ node }">
              <element-tree-line
                :node="node"
                :showLabelLine="treeProps['showLabelLine']"
                :indent="treeProps['indent']"
              >
                <icon-svg
                  v-if="node.level === 1"
                  name="xiangmu"
                  style="margin-right: 3px"
                ></icon-svg>
                <span
                  v-if="node.label.length <= 15"
                  :style="{ color: node.level === 1 ? '#333333' : '#666666' }"
                  >{{ node.label }}</span
                >
                <el-tooltip v-else :content="node.label" placement="top">
                  <span
                    class="el-tree-node__label"
                    :style="{ color: node.level === 1 ? '#333333' : '#666666' }"
                    >{{ node.label }}</span
                  >
                </el-tooltip>
              </element-tree-line>
            </template>
          </el-tree>
        </el-row>
      </el-col>
      <el-col :span="20">
        <div v-if="annotatorList.length !== 0" style="margin-bottom: 20px">
          <el-radio-group
            @input="changeAnnotator()"
            v-model="dataForm.annotatorId"
            size="medium"
          >
            <el-radio-button
              v-for="(item, idx) in annotatorList"
              :key="'radio-' + idx"
              :label="item.userId"
              ><i class="el-icon-user"></i> {{ item.username }}
            </el-radio-button>
          </el-radio-group>
        </div>
        <el-row>
          <el-form :inline="true" :model="dataForm" ref="searchForm">
            <el-form-item label="文书编号" prop="articleId">
              <el-input
                style="width: 150px"
                v-model="dataForm.articleId"
                placeholder="文书编号"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item label="文书标题" prop="articleName">
              <el-input
                style="width: 150px"
                v-model="dataForm.articleName"
                placeholder="文书标题"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              label="正确率"
              prop="correctRate"
              v-if="selected.active === 'reviewed'"
            >
              <el-select
                placeholder="正确率"
                style="width: 100px"
                clearable
                v-model="dataForm.correctRate"
              >
                <el-option label="≥10" :value="10"></el-option>
                <el-option label="≥20" :value="20"></el-option>
                <el-option label="≥30" :value="30"></el-option>
                <el-option label="≥40" :value="40"></el-option>
                <el-option label="≥50" :value="50"></el-option>
                <el-option label="≥60" :value="60"></el-option>
                <el-option label="≥70" :value="70"></el-option>
                <el-option label="≥80" :value="80"></el-option>
                <el-option label="≥90" :value="90"></el-option>
                <el-option label="100" :value="100"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              v-if="isAuth('task:auditor:annotator:list')"
              label="审核员"
            >
              <el-select
                @change="getDataList"
                placeholder="审核员"
                style="width: 140px"
                clearable
                v-model="dataForm.auditorId"
              >
                <div
                  v-for="(value, key) in auditorList"
                  :key="'auditor-select-' + key"
                >
                  <el-option
                    :label="value.username"
                    :value="value.userId"
                  ></el-option>
                </div>
                <el-option label="自动审核" value="2"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="更新时间" prop="date">
              <el-date-picker
                v-model="dataForm.date"
                value-format="yyyy-MM-dd"
                type="daterange"
                style="width: 235px"
                range-separator="-"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button @click="searchData" icon="el-icon-search"
                >查询
              </el-button>
              <el-button
                @click="resetSearch"
                plain
                type="info"
                icon="el-icon-refresh-left"
                >重置
              </el-button>
            </el-form-item>
            <el-form-item style="float: right">
              <el-button
                v-if="roleId === this.$RoleEnum.annotator"
                @click="startTask"
                type="success"
                icon="el-icon-edit"
                >开始标注
              </el-button>
              <el-button
                v-if="roleId === this.$RoleEnum.annotator"
                plain
                @click="uploadPreAnno"
                type="warning"
                icon="el-icon-upload"
                >导入预标注数据
              </el-button>
              <router-link
                v-if="roleId === this.$RoleEnum.annotator"
                :to="'/project/verify/' + projectId"
                class="ml-3"
              >
                <el-button plain type="info" icon="el-icon-finished"
                  >数据验证
                </el-button>
              </router-link>
            </el-form-item>
          </el-form>
        </el-row>
        <el-row v-if="isAuth('task:auditor:annotator:list')">
          <el-tabs v-model="selected.active" @tab-click="handleClick">
            <el-tab-pane label="待审核" name="marked"></el-tab-pane>
            <el-tab-pane label="全部" name="all"></el-tab-pane>
            <el-tab-pane label="审核中" name="reviewing"></el-tab-pane>
            <el-tab-pane label="打回中" name="repulse"></el-tab-pane>
            <el-tab-pane label="已修正" name="corrected"></el-tab-pane>
            <el-tab-pane label="已验收" name="reviewed"></el-tab-pane>
          </el-tabs>
        </el-row>
        <el-row v-else>
          <el-tabs v-model="selected.active" @tab-click="handleClick">
            <template
              v-if="
                roleId === this.$RoleEnum.annotator &&
                dataForm.annotatorId === userId
              "
            >
              <el-tab-pane
                v-if="mode !== 1"
                label="待标注"
                name="unmarked"
              ></el-tab-pane>
              <el-tab-pane label="全部" name="all"></el-tab-pane>
              <el-tab-pane label="标注中" name="noting"></el-tab-pane>
              <el-tab-pane label="待审核" name="marked"></el-tab-pane>
              <el-tab-pane label="审核中" name="reviewing"></el-tab-pane>
              <el-tab-pane label="被打回" name="repulse"></el-tab-pane>
              <el-tab-pane label="已修正" name="corrected"></el-tab-pane>
            </template>
            <el-tab-pane label="已验收" name="reviewed"></el-tab-pane>
          </el-tabs>
        </el-row>
        <el-row>
          <el-table
            v-loading="loading"
            :data="tableData"
            style="width: 100%; border-radius: 8px"
            border
            @sort-change="sortChange"
            @selection-change="selectionChangeHandle"
            :header-row-style="{ height: '44px' }"
            :header-cell-style="{
              background: 'rgb(246 247 249)',
              padding: '0px'
            }"
          >
            <el-table-column
              type="selection"
              header-align="center"
              align="center"
              width="50"
            >
            </el-table-column>
            <el-table-column
              width="200"
              prop="articleId"
              sortable="custom"
              :show-overflow-tooltip="true"
              label="文书编号"
            ></el-table-column>
            <el-table-column
              prop="articleName"
              sortable="custom"
              :show-overflow-tooltip="true"
              label="文书标题"
            ></el-table-column>

            <el-table-column
              v-if="selected.active === 'reviewed'"
              prop="queries"
              sortable="custom"
              align="center"
              width="100"
              label="提问"
            ></el-table-column>

            <el-table-column
              v-if="selected.active === 'reviewed'"
              prop="correctRate"
              sortable="custom"
              align="center"
              width="100"
              label="正确率"
            ></el-table-column>
            <el-table-column
              width="100"
              prop="step"
              align="center"
              sortable="custom"
              label="状态"
            >
              <!--审核员列表-->
              <template
                v-slot="scope"
                v-if="isAuth('task:auditor:annotator:list')"
              >
                <el-tag v-if="scope.row.step === 2" size="small" type="info"
                  >待审核
                </el-tag>
                <el-tag
                  v-else-if="scope.row.step === 3"
                  size="small"
                  type="primary"
                  >审核中
                </el-tag>
                <el-tag
                  v-else-if="scope.row.step === 4"
                  size="small"
                  type="success"
                  >已验收
                </el-tag>
                <el-tag
                  v-else-if="scope.row.step === 5"
                  size="small"
                  type="danger"
                  >打回中
                </el-tag>
                <el-tag
                  v-else-if="scope.row.step === 6"
                  size="small"
                  type="warning"
                  >已修正
                </el-tag>
              </template>
              <!--标注员列表-->
              <template v-slot="scope" v-else>
                <el-tag v-if="scope.row.step === 0" size="small" type="info"
                  >待标注
                </el-tag>
                <el-tag
                  v-else-if="scope.row.step === 1"
                  size="small"
                  type="primary"
                  >标注中
                </el-tag>
                <el-tag
                  v-else-if="scope.row.step === 2"
                  size="small"
                  type="warning"
                  >待审核
                </el-tag>
                <el-tag
                  v-else-if="scope.row.step === 3"
                  size="small"
                  type="warning"
                  >审核中
                </el-tag>
                <el-tag
                  v-else-if="scope.row.step === 4"
                  size="small"
                  type="success"
                  >已验收
                </el-tag>
                <el-tag
                  v-else-if="scope.row.step === 5"
                  size="small"
                  type="danger"
                  >被打回
                </el-tag>
                <el-tag
                  v-else-if="scope.row.step === 6"
                  size="small"
                  type="warning"
                  >已修正
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column
              v-if="
                selected.active !== 'marked' &&
                roleId === this.$RoleEnum.auditor
              "
              :formatter="updateUsername"
              width="100"
              prop="auditorId"
              label="审核员"
              align="center"
            ></el-table-column>
            <el-table-column
              width="150"
              :formatter="updateDateTime"
              prop="updateTime"
              label="更新时间"
              sortable="custom"
              align="center"
            ></el-table-column>
            <el-table-column
              width="100"
              label="操作"
              align="left"
              v-if="freeAnnotation === 1 || selected.active !== 'unmarked'"
            >
              <template slot-scope="scope">
                <el-tooltip
                  v-if="!scope.row.editable"
                  content="查看"
                  placement="top"
                  effect="light"
                >
                  <i
                    class="el-icon-view icon-primary"
                    @click="toTaskDetail(false, scope.row.noteId)"
                  ></i>
                </el-tooltip>
                <el-tooltip
                  v-else
                  :content="roleText"
                  placement="top"
                  effect="light"
                >
                  <i
                    class="el-icon-edit-outline icon-primary"
                    @click="toTaskDetail(true, scope.row.noteId)"
                  ></i>
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>

          <div class="d-flex align-items-center mt-1">
            <div v-show="tableData.length > 0">
              <el-button
                v-if="
                  isAuth('task:batch:auditor') &&
                  (selected.active === 'reviewing' ||
                    selected.active === 'marked' ||
                    selected.active === 'corrected')
                "
                type="primary"
                size="small"
                icon="el-icon-edit"
                @click="batchReview"
                :disabled="dataListSelections.length <= 0"
                >批量审核
              </el-button>
            </div>
            <div class="ml-auto pb-1">
              <el-pagination
                @size-change="sizeChangeHandle"
                @current-change="currentChangeHandle"
                :current-page="pageIndex"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="pageSize"
                :total="totalPage"
                layout="total, sizes, prev, pager, next, jumper"
              >
              </el-pagination>
            </div>
          </div>
        </el-row>
      </el-col>
    </el-row>
    <!-- 上传预标注信息 -->
    <upload-pre-anno
      v-if="uploadPreAnnoVisible"
      ref="uploadPreAnno"
      @refreshDataList="getDataList"
    ></upload-pre-anno>
  </div>
</template>

<script>
import { FromTypeEnum, setPageIds } from "@/utils/pageIdUtil";
import UploadPreAnno from "@/views/modules/project/upload-pre-anno";

export default {
  data() {
    return {
      fromType: FromTypeEnum.anno_task_list,
      loading: false,
      uploadPreAnnoVisible: false,
      projectId: undefined,
      freeAnnotation: undefined,
      mode: undefined,
      filterText: "",
      annotatorList: [],
      auditorList: [],
      dataForm: {
        articleId: "",
        articleName: "",
        correctRate: undefined,
        date: [],
        annotatorId: undefined,
        auditorId: undefined,
        orderBy: undefined,
        isAsc: undefined
      },
      treeProps: {
        indent: 16,
        showLabelLine: true
      },
      treeData: [],
      selected: {
        batchId: undefined,
        active: undefined
      },
      tableData: [],
      dataListSelections: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      isAutoSwitching: true // 标识是否为自动切换（非用户手动点击）
    };
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    }
  },
  components: {
    UploadPreAnno
  },
  computed: {
    roleId() {
      return this.$store.state.user.roleId;
    },
    userId() {
      return this.$store.state.user.id;
    },
    roleText() {
      return this.roleId === this.$RoleEnum.annotator ? "标注" : "审核";
    }
  },
  created() {
    this.resetActive();
    this.init();
  },
  mounted() {
    this.init();
    this.initData();
  },
  methods: {
    // 预标注数据上传
    uploadPreAnno() {
      this.uploadPreAnnoVisible = true;
      this.$nextTick(() => {
        this.$refs.uploadPreAnno.init(this.projectId);
      });
    },
    initData: async function () {
      await this.getProject();
      await this.getAnnotatorList();
    },
    getProject: async function () {
      await this.$http({
        url: this.$http.adornUrl("/task/queryProjectList"),
        method: "get",
        params: this.$http.adornParams({
          roleId: this.roleId
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          const projectList = data.data || [];
          const outputArr = [];
          // 遍历原始数组
          projectList.forEach((item) => {
            const projectIndex = outputArr.findIndex(
              (obj) => obj.id === "project-" + item.projectId
            );
            if (projectIndex === -1) {
              // 如果项目 ID 还没出现过，就新建一个项目对象
              outputArr.push({
                id: "project-" + item.projectId,
                label: item.projectName,
                children: [
                  {
                    id: item.batchId,
                    label: item.batchName
                  }
                ]
              });
            } else {
              // 如果项目 ID 出现过，就把批次号添加到对应的项目对象中
              outputArr[projectIndex].children.push({
                id: item.batchId,
                label: item.batchName
              });
            }
          });
          this.treeData = outputArr;

          if (
            this.selected.batchId == null &&
            this.treeData != null &&
            this.treeData.length !== 0
          ) {
            this.selected.batchId = this.treeData[0].children[0].id;
            this.resetActive();
            this.$nextTick(() => {
              this.$refs.tree.setCurrentKey(this.selected.batchId);
            });
          }
        }
      });
    },
    getAnnotatorList: async function () {
      await this.$http({
        url: this.$http.adornUrl("/task/queryUserList"),
        method: "get",
        params: this.$http.adornParams({
          batchId: this.selected.batchId,
          roleId: this.$RoleEnum.annotator
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.annotatorList = data.data;
          if (this.annotatorList && this.annotatorList.length > 0) {
            // 保存当前选中的标注员ID，避免切换批次时丢失选择
            const currentAnnotatorId = this.dataForm.annotatorId;

            // 标注员则要等把自身排序到第一个之后再放前面
            if (this.roleId === this.$RoleEnum.annotator) {
              this.annotatorList = this.moveUserIdToFirst(
                this.annotatorList,
                this.userId
              );
              // 如果当前用户在新的标注员列表中，保持选中；否则选择第一个（自己）
              if (
                this.annotatorList.find(
                  (user) => user.userId === currentAnnotatorId
                )
              ) {
                this.dataForm.annotatorId = currentAnnotatorId;
              } else {
                this.dataForm.annotatorId = this.annotatorList[0].userId;
              }
            } else {
              // 审核员：如果之前选中的标注员在新列表中，保持选中；否则选择第一个
              if (
                currentAnnotatorId &&
                this.annotatorList.find(
                  (user) => user.userId === currentAnnotatorId
                )
              ) {
                this.dataForm.annotatorId = currentAnnotatorId;
              } else {
                this.dataForm.annotatorId = this.annotatorList[0].userId;
              }
            }
          }
          if (this.roleId === this.$RoleEnum.auditor) {
            // 如果是当前用户审核员时，移除他的标注
            const index = this.annotatorList.findIndex(
              (user) => user.userId === this.userId
            );
            if (index !== -1) {
              this.annotatorList.splice(index, 1);
            }
            this.getAuditorList();
          } else {
            this.isAutoSwitching = true; // 自动切换
            this.getDataList();
          }
        }
      });
    },
    getAuditorList: async function () {
      await this.$http({
        url: this.$http.adornUrl("/task/queryUserList"),
        method: "get",
        params: this.$http.adornParams({
          batchId: this.selected.batchId,
          roleId: this.$RoleEnum.auditor
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.auditorList = data.data;
          if (this.auditorList && this.auditorList.length > 0) {
            this.auditorList = this.moveUserIdToFirst(
              this.auditorList,
              this.userId
            );
            // if (this.dataForm.auditorId == null) {
            //   this.dataForm.auditorId = this.auditorList[0].userId;
            // }
            this.isAutoSwitching = true; // 自动切换
            this.getDataList();
          }
        }
      });
    },
    moveUserIdToFirst(userList, userId) {
      if (userId == null) {
        return userList;
      }
      const index = userList.findIndex((user) => user.userId === userId);
      if (index !== -1) {
        const [user] = userList.splice(index, 1);
        userList.unshift(user);
      }
      return userList;
    },
    toTaskDetail(editable, noteId) {
      this.$router.push({
        path: "/annotask/detail",
        query: {
          batchId: Number(this.selected.batchId),
          noteId: Number(noteId),
          editable: editable,
          annotatorId: this.dataForm.annotatorId,
          fromType: this.fromType
        }
      });
    },
    changeAnnotator() {
      // 清空查询表单
      this.$refs.searchForm.resetFields();
      // 重置标签页到默认状态
      this.resetActive();
      this.isAutoSwitching = true; // 标记为自动切换
      this.getDataList();
    },
    resetActive() {
      // 标注员默认选择"待标注"，审核员默认选择"待审核"
      if (this.roleId === this.$RoleEnum.annotator) {
        this.selected.active = "unmarked";
      } else if (this.roleId === this.$RoleEnum.auditor) {
        this.selected.active = "marked";
      } else {
        this.selected.active = "all";
      }

      // 如果标注员查看的不是自己的任务，则显示"已验收"
      if (
        this.roleId === this.$RoleEnum.annotator &&
        this.dataForm.annotatorId &&
        this.dataForm.annotatorId !== this.userId
      ) {
        this.selected.active = "reviewed";
      }

      this.dataForm.orderBy = undefined;
      this.dataForm.isAsc = undefined;
      this.isAutoSwitching = true; // 标记为自动切换
    },
    // 用户点击查询按钮
    searchData() {
      this.isAutoSwitching = false; // 用户操作
      this.getDataList();
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val;
      this.pageIndex = 1;
      this.isAutoSwitching = false; // 用户操作
      this.getDataList();
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val;
      this.isAutoSwitching = false; // 用户操作
      this.getDataList();
    },
    init() {
      const query = sessionStorage.getItem(
        this.userId + "-" + this.roleId + "-taskQueryList"
      );
      if (!query) {
        return;
      }
      const params = JSON.parse(query);
      this.pageIndex = params.page;
      this.pageSize = params.limit;
      this.dataForm.orderBy = params.orderBy;
      this.dataForm.isAsc = params.isAsc;
      this.selected.batchId = params.batchId;
      this.selected.active = params.activeStep;
      this.dataForm.annotatorId = params.annotatorId;
      this.dataForm.auditorId = params.auditorId;
      this.dataForm.articleId = params.articleId;
      this.dataForm.articleName = params.articleName;
      this.dataForm.correctRate = params.correctRate;
      if (params.startDate && params.endDate) {
        this.dataForm.date = [];
        this.dataForm.date[0] = params.startDate;
        this.dataForm.date[1] = params.endDate;
      }
    },
    // 查询表格
    getDataList() {
      if (!this.selected.batchId) {
        return;
      }
      this.loading = true;
      const params = {
        page: this.pageIndex,
        limit: this.pageSize,
        orderBy: this.dataForm.orderBy,
        isAsc: this.dataForm.isAsc,
        roleId: this.roleId,
        batchId: this.selected.batchId,
        annotatorId: this.dataForm.annotatorId,
        auditorId: this.dataForm.auditorId,
        activeStep: this.selected.active,
        articleId: this.dataForm.articleId,
        articleName: this.dataForm.articleName,
        correctRate: this.dataForm.correctRate,
        startDate:
          (this.dataForm?.date != null && this.dataForm?.date[0]) || undefined,
        endDate:
          (this.dataForm?.date != null && this.dataForm?.date[1]) || undefined
      };
      // 把用户的查询条件缓存，方便使用
      sessionStorage.setItem(
        this.userId + "-" + this.roleId + "-taskQueryList",
        JSON.stringify(params)
      );
      this.$http({
        url: this.$http.adornUrl("/task/queryTaskList"),
        method: "get",
        params: this.$http.adornParams(params)
      })
        .then(({ data }) => {
          let pageIdsData = null;
          if (data && data.code === 0) {
            let result = data.data;

            this.tableData = result.listData.list || [];
            this.totalPage = result.listData.totalCount;
            this.projectId = result.projectId;
            this.freeAnnotation = result.freeAnnotation;
            this.mode = result.mode;

            if (
              this.mode === 1 &&
              (!this.selected.active ||
                (this.roleId === this.$RoleEnum.annotator &&
                  this.selected.active === "unmarked"))
            ) {
              this.selected.active = "all";
            }

            // 自动切换逻辑：只在非用户手动切换时执行
            if (this.isAutoSwitching) {
              // 如果审核员在"待审核"标签页没有数据，自动切换到"全部"标签页
              if (
                this.roleId === this.$RoleEnum.auditor &&
                this.selected.active === "marked" &&
                this.totalPage === 0
              ) {
                this.selected.active = "all";
                // 重新获取数据
                this.getDataList();
                return;
              }

              // 如果标注员在"待标注"标签页没有数据，自动切换到"全部"标签页
              if (
                this.roleId === this.$RoleEnum.annotator &&
                this.selected.active === "unmarked" &&
                this.totalPage === 0
              ) {
                this.selected.active = "all";
                // 重新获取数据
                this.getDataList();
                return;
              }
            }

            pageIdsData = result.pageIdsVOList;
          } else if (data && data.code === 501) {
            sessionStorage.removeItem(
              this.userId + "-" + this.roleId + "-taskQueryList"
            );
            this.dataForm.annotatorId = undefined;
            this.resetForm();
            this.getProject();
          } else {
            this.dataList = [];
            this.totalPage = 0;
          }

          setPageIds(this.fromType, pageIdsData);
        })
        .catch(() => {
          setPageIds(this.fromType, null);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 批量重置
    batchReview() {
      const ids = this.dataListSelections.map((item) => {
        return item.noteId;
      });
      this.$confirm(`确定以当前用户为蓝本批量审核选中的所有文书?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          const loading = this.$loading({
            lock: true,
            text: "执行中，请稍等",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)"
          });
          this.$http({
            url: this.$http.adornUrl(
              "/task/batchReview/" + this.dataForm.annotatorId
            ),
            method: "post",
            data: this.$http.adornData(ids, false)
          })
            .then(({ data }) => {
              if (data && data.code === 0) {
                this.$message({
                  message: "操作成功",
                  type: "success",
                  duration: 3000
                });
              } else {
                this.$message.error(data.msg);
              }
              this.isAutoSwitching = false; // 批量审核后刷新
              this.getDataList();
            })
            .finally(() => {
              loading.close();
            });
        })
        .catch(() => {});
    },
    resetSearch() {
      this.dataForm.articleId = undefined;
      this.dataForm.articleName = undefined;
      this.dataForm.correctRate = undefined;
      this.dataForm.date = [];
      this.dataForm.orderBy = undefined;
      this.dataForm.isAsc = undefined;
      this.isAutoSwitching = false; // 用户操作
      this.getDataList();
    },
    // 过滤树形控件的值
    filterNode(value, data) {
      if (!value) return true;
      return data.label.toLowerCase().indexOf(value.toLowerCase()) !== -1;
    },
    // 树形控件node被点击
    handleNodeClick(data, node) {
      if (node.isLeaf) {
        this.selected.batchId = data.id;
        this.resetActive(); // resetActive 内部已设置 isAutoSwitching = true
        this.pageIndex = 1;
        this.getAnnotatorList();
      }
    },
    sortChange(column) {
      if (column.order === "descending") {
        this.dataForm.orderBy = column.prop;
        this.dataForm.isAsc = false;
      } else {
        this.dataForm.orderBy = column.prop;
        this.dataForm.isAsc = true;
      }
      // 什么排序都不选择，恢复默认
      if (column.order == null) {
        this.dataForm.orderBy = undefined;
        this.dataForm.isAsc = undefined;
      }
      this.pageIndex = 1;
      this.isAutoSwitching = false; // 用户操作
      this.getDataList();
    },
    // tabs 标签页被点击
    handleClick(tab) {
      this.selected.active = tab.name;
      this.pageIndex = 1;
      this.dataForm.orderBy = undefined;
      this.dataForm.isAsc = undefined;
      this.isAutoSwitching = false; // 用户手动切换
      this.getDataList();
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val;
    },
    // 定制化日期显示
    updateDateTime(row, column) {
      return this.$tools.updateDateTime(row, column);
    },
    updateUsername(row, column) {
      if (row.auditorId === 2) {
        return "自动审核";
      }
      const user = this.auditorList.find((u) => u.userId === row.auditorId);
      return user ? user.username : null;
    },
    resetForm() {
      this.dataForm = {
        articleId: "",
        articleName: "",
        correctRate: undefined,
        date: [],
        annotatorId: undefined,
        orderBy: undefined,
        isAsc: undefined
      };
      this.selected = {
        batchId: undefined,
        active: undefined
      };
      this.pageIndex = 1;
      this.pageSize = 10;
      this.totalPage = 0;
    },
    startTask() {
      let data = {
        batchId: this.selected.batchId,
        roleId: this.$RoleEnum.annotator,
        annotatorId: this.userId
      };
      this.$http({
        url: this.$http.adornUrl("/task/assignTask"),
        method: "post",
        data: this.$http.adornData(data)
      }).then(({ data }) => {
        if (data && data.code === 0) {
          const task = data.data;
          if (!task) {
            this.$message({
              message: "暂无可标注任务",
              type: "warning",
              duration: 3000
            });
            return;
          }
          this.$router.push({
            path: "/annotask/detail",
            query: {
              batchId: this.selected.batchId,
              noteId: task.noteId,
              editable: true,
              annotatorId: this.userId,
              fromType: "annoTaskList"
            }
          });
        } else {
          this.$message({
            message: data.msg,
            type: "error",
            duration: 10000
          });
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.el-radio-group {
  ::v-deep .el-radio-button__inner {
    padding: 10px;
  }
}

.search {
  margin-bottom: 10px;
}

.el-tree-node__label {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 15em;
}

::v-deep
  .el-tree--highlight-current
  .el-tree-node.is-current
  > .el-tree-node__content {
  border-radius: 4px;
  background-color: #f0f7ff;
}
</style>
