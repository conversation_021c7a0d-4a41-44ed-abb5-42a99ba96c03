<template>
  <nav class="site-navbar" :class="'site-navbar--' + navbarLayoutType">
    <div class="site-navbar__header">
      <h1 class="site-navbar__brand" @click="$router.push({ name: 'home' })">
        <img src="@/assets/img/logo.png" alt="" style="width: 43px" />
        <a class="site-navbar__brand-lg" href="javascript:0;"
          >自然语言标注系统</a
        >
      </h1>
    </div>

    <div class="site-navbar__body clearfix">
      <el-row>
        <el-col :span="14">
          <div style="display: flex; justify-content: space-between">
            <div>
              <el-descriptions
                :column="2"
                size="mini"
                labelClassName="label-name"
                contentClassName="label-content"
              >
                <el-descriptions-item label="项目名"
                  >{{ navbar.projectName }}
                </el-descriptions-item>
                <el-descriptions-item label="文书编号">
                  <descriptions-tooltip
                    :value="navbar.articleId"
                  ></descriptions-tooltip>
                  <el-tooltip
                    v-if="showReferencesLink"
                    style="margin-left: 5px"
                    effect="dark"
                    content="参考文献"
                    placement="right"
                  >
                    <el-link
                      :href="`https://idc.biosino.org/bfms-api/api/article/referencePDF/${encodeURIComponent(
                        navbar.articleId
                      )}`"
                      icon="el-icon-link"
                      target="_blank"
                    ></el-link>
                  </el-tooltip>
                </el-descriptions-item>
                <el-descriptions-item label="批次名"
                  >{{ navbar.batchName }}
                </el-descriptions-item>
                <el-descriptions-item
                  v-if="
                    roleId === this.$RoleEnum.annotator ||
                    roleId === this.$RoleEnum.auditor
                  "
                  style="margin-left: 3rem"
                  label="标注状态"
                >
                  {{ navbar.taskStatus }}
                  <el-tooltip
                    v-if="navbar.taskStep === 5 && navbar.repulseMsg"
                    class="item"
                    effect="light"
                    :content="navbar.repulseMsg"
                    placement="bottom"
                  >
                    <i class="el-icon-question icon-warning ml-2"> </i>
                  </el-tooltip>
                </el-descriptions-item>
                <el-descriptions-item
                  v-else
                  style="margin-left: 3rem"
                  label="标注状态"
                >
                  {{ navbar.status }}
                </el-descriptions-item>
              </el-descriptions>
            </div>

            <div
              v-if="pageData && pageData.length > 0"
              style="
                padding: 8px 12px 3px 5px;
                display: flex;
                justify-content: space-between;
              "
            >
              <div style="margin-right: 20px">
                <el-button
                  :disabled="currentIndex <= 0"
                  type="primary"
                  size="mini"
                  round
                  icon="el-icon-arrow-left"
                  @click.once="doChangePage(true)"
                >
                  上一页
                </el-button>
              </div>

              <div>
                <el-button
                  :disabled="currentIndex >= pageData.length - 1"
                  type="primary"
                  size="mini"
                  round
                  @click.once="doChangePage(false)"
                >
                  下一页
                  <i class="el-icon-arrow-right el-icon--right"></i>
                </el-button>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="10">
          <el-menu
            class="site-navbar__menu site-navbar__menu--right"
            mode="horizontal"
          >
            <el-menu-item index="1">
              <el-button
                type="warning"
                size="mini"
                icon="el-icon-connection"
                plain
                class="relation"
                @click="switchAnnoType"
                >{{ buttonName }}
              </el-button>
            </el-menu-item>
            <el-menu-item index="2">
              <el-popover
                placement="bottom"
                trigger="hover"
                popper-class="down-popover"
              >
                <el-button
                  type="info"
                  size="mini"
                  icon="el-icon-notebook-2"
                  class="help"
                  plain
                  @click="openMetadata"
                  style="padding: 8px"
                  >元数据
                </el-button>
                <el-button
                  style="padding: 0"
                  type="info"
                  size="mini"
                  class="help"
                  plain
                  @click="showHelpDialog"
                >
                  <span
                    style="
                      font-size: 12px;
                      padding: 9px 8px 7px 8px;
                      display: flex;
                      align-items: center;
                    "
                  >
                    <i class="el-icon-question" style="margin-right: 4px"></i>
                    标注规范
                  </span>
                </el-button>
                <el-button
                  v-if="navbar.hasSpecFile"
                  style="padding: 0"
                  type="info"
                  size="mini"
                  class="help"
                  plain
                >
                  <el-link
                    style="font-size: 12px; padding: 9px 8px 7px 8px"
                    :href="projectSpecificationDoc"
                    :underline="false"
                    target="_blank"
                    icon="el-icon-tickets"
                    >规范
                  </el-link>
                </el-button>
                <el-button
                  slot="reference"
                  type="info"
                  size="mini"
                  icon="el-icon-s-tools"
                  plain
                  >资源
                </el-button>
              </el-popover>
            </el-menu-item>
            <el-menu-item v-if="navbar.editable" index="3">
              <el-button
                v-if="
                  isAuth('task:detail:repulse') &&
                  (navbar.taskStep === 3 ||
                    (navbar.step === 2 && navbar.taskStep === 6))
                "
                type="danger"
                size="mini"
                plain
                icon="el-icon-close"
                class="discard"
                @click="repulse"
                >打回
              </el-button>
              <el-tooltip
                v-if="
                  (navbar.taskStep === 1 || navbar.taskStep === 5) &&
                  navbar.invalid === 1 &&
                  isAuth('task:detail:invalid')
                "
                class="item"
                effect="dark"
                content="当前文书已被废弃，点击恢复"
                placement="bottom"
              >
                <el-button
                  type="success"
                  size="mini"
                  icon="el-icon-refresh-left"
                  plain
                  @click="invalidTask(0)"
                  >恢复
                </el-button>
              </el-tooltip>
              <el-tooltip
                v-if="
                  (navbar.taskStep === 1 || navbar.taskStep === 5) &&
                  navbar.invalid === 0 &&
                  isAuth('task:detail:invalid')
                "
                class="item"
                effect="dark"
                content="点击废弃当前文书（文书无标注价值）"
                placement="bottom"
              >
                <el-button
                  type="danger"
                  size="mini"
                  icon="el-icon-delete-solid"
                  plain
                  @click="invalidTask(1)"
                  class="discard"
                  >废弃
                </el-button>
              </el-tooltip>

              <el-tooltip
                v-if="navbar.taskStep === 1 || navbar.taskStep === 3"
                class="item"
                effect="dark"
                :content="
                  '放弃' +
                  (roleId === this.$RoleEnum.annotator ? '标注' : '审核') +
                  '当前文书'
                "
                placement="bottom"
              >
                <el-button
                  type="warning"
                  size="mini"
                  icon="el-icon-s-release"
                  plain
                  @click="cancelTask"
                  class="discard"
                  >取消
                </el-button>
              </el-tooltip>
              <el-tooltip
                v-if="
                  (navbar.taskStep === 1 || navbar.taskStep === 5) &&
                  isAuth('task:detail:annSubmit')
                "
                class="item"
                effect="dark"
                content="完成标注当前文书"
                placement="bottom"
              >
                <el-button
                  type="success"
                  size="mini"
                  @click="submitTask(false)"
                  icon="el-icon-s-claim"
                  plain
                  >提交
                </el-button>
              </el-tooltip>
              <el-tooltip
                v-if="
                  (navbar.taskStep === 1 || navbar.taskStep === 5) &&
                  isAuth('task:detail:annSubmit')
                "
                class="item"
                effect="dark"
                content="完成标注当前文书，并继续标注下一篇"
                placement="bottom"
              >
                <el-button
                  type="success"
                  size="mini"
                  @click="submitTask(true)"
                  icon="el-icon-check"
                  plain
                  >继续
                </el-button>
              </el-tooltip>
              <el-tooltip
                v-if="
                  (navbar.taskStep === 3 ||
                    (navbar.step === 2 && navbar.taskStep === 6)) &&
                  isAuth('task:detail:auditorSubmit')
                "
                class="item"
                effect="dark"
                content="完成审核当前文书"
                placement="bottom"
              >
                <el-button
                  type="success"
                  size="mini"
                  @click="submitTask(false)"
                  icon="el-icon-s-claim"
                  plain
                  >提交
                </el-button>
              </el-tooltip>
              <el-tooltip
                v-if="
                  (navbar.taskStep === 3 ||
                    (navbar.step === 2 && navbar.taskStep === 6)) &&
                  isAuth('task:detail:auditorSubmit')
                "
                class="item"
                effect="dark"
                content="完成审核当前文书，并继续审核下一篇"
                placement="bottom"
              >
                <el-button
                  v-if="
                    (navbar.taskStep === 3 ||
                      (navbar.step === 2 && navbar.taskStep === 6)) &&
                    isAuth('task:detail:auditorSubmit')
                  "
                  type="success"
                  size="mini"
                  @click="submitTask(true)"
                  icon="el-icon-check"
                  plain
                  >继续
                </el-button>
              </el-tooltip>
            </el-menu-item>
            <el-menu-item v-if="!navbar.editable" index="3">
              <el-button
                v-if="navbar.taskStep === 2 && isAuth('task:batch:reAnnotator')"
                type="warning"
                size="mini"
                plain
                @click="rework"
                >重新标注
              </el-button>
              <el-button
                v-if="
                  navbar.taskStep === 4 &&
                  isAuth('task:batch:reAuditor') &&
                  auditorId !== 2
                "
                type="warning"
                size="mini"
                @click="rework"
                plain
                >重新审核
              </el-button>
              <!--重新审核被自动审核的-->
              <el-tooltip
                v-if="!navbar.editable && navbar.step === 4 && auditorId === 2"
                class="item"
                effect="dark"
                content="强制人工审核该自动审核的文书"
                placement="bottom"
              >
                <el-button type="warning" size="mini" @click="rework" plain
                  >人工审核
                </el-button>
              </el-tooltip>
            </el-menu-item>
            <el-menu-item index="4">
              <el-button
                type="info"
                plain
                size="mini"
                @click="goBack()"
                icon="el-icon-arrow-left"
                >返回
              </el-button>
            </el-menu-item>
          </el-menu>
        </el-col>
      </el-row>
    </div>

    <!-- 帮助弹窗 -->
    <el-dialog
      :visible.sync="helpDialogVisible"
      width="80%"
      :modal="true"
      :modal-append-to-body="true"
      :append-to-body="true"
      top="30px"
    >
      <div
        v-loading="helpLoading"
        style="min-height: 200px; max-height: 95vh; overflow-y: auto"
      >
        <div
          v-if="!helpLoading && helpContent"
          style="max-height: 90vh; overflow-y: auto"
        >
          <mavon-editor
            v-model="helpContent"
            :subfield="false"
            :defaultOpen="'preview'"
            :toolbarsFlag="false"
            :editable="false"
            :scrollStyle="true"
            :ishljs="true"
            :boxShadow="false"
            :toolbars="{
              fullscreen: true
            }"
            style="background: white; z-index: 1; height: 85vh"
          />
        </div>
        <div
          v-else-if="!helpLoading && !helpContent"
          style="text-align: center; color: #999; padding: 50px"
        >
          暂无帮助文档
        </div>
      </div>
    </el-dialog>
  </nav>
</template>

<script>
import { AnnoEventsBus } from "@/utils/bus";
import DescriptionsTooltip from "@/components/discription";
import { trimStr } from "@/utils";
import { getPageIds } from "@/utils/pageIdUtil";
import { mavonEditor } from "mavon-editor";

export default {
  data() {
    return {
      batchId: null,
      buttonName: "关系标注",
      currentIndex: -1,
      pageData: [],
      helpDialogVisible: false,
      helpContent: "",
      helpLoading: false
    };
  },
  created() {
    this.batchId = this.$route.query.batchId;
  },
  components: {
    DescriptionsTooltip,
    mavonEditor
  },
  computed: {
    editable() {
      return this.$store.state.anno.editable;
    },
    projectSpecificationDoc() {
      const projectName = this.$store.state.anno.navbar.projectName;
      return `${
        process.env.VUE_APP_BASE_API
      }/file/project/docView/${encodeURIComponent(projectName)}?projectId=${
        this.$store.state.anno.annotation.projectId
      }`;
    },
    navbarLayoutType: {
      get() {
        return this.$store.state.common.navbarLayoutType;
      }
    },
    roleId() {
      return this.$store.state.user.roleId;
    },
    auditorId() {
      return Number(this.$route.query.auditorId);
    },
    navbar() {
      return this.$store.state.anno.navbar;
    },
    showReferencesLink() {
      const documentSource = this.$store.state.anno.navbar.documentSource;
      return (
        documentSource &&
        (documentSource.toUpperCase() === "TIKA" ||
          documentSource.toUpperCase() === "XML")
      );
    }
  },
  mounted() {
    this.initPageInfo();
  },
  methods: {
    initPageInfo() {
      this.pageData = [];
      if (!this.editable && !this.navbar.editable) {
        const fromType = trimStr(this.$route.query.fromType);
        const batchId = Number(this.$route.query.batchId);
        const noteId = Number(this.$route.query.noteId);
        const pageIds = getPageIds(fromType);

        if (pageIds && pageIds.length > 0) {
          const len = pageIds.length;
          for (let i = 0; i < len; i++) {
            let item = pageIds[i];
            if (batchId === item.batchId && noteId === item.noteId) {
              this.currentIndex = i;
              break;
            }
          }

          if (this.currentIndex > -1) {
            this.pageData.push(...pageIds);
          }
        }
      }
    },
    doChangePage(pre) {
      const path = trimStr(this.$route.path);
      const query = { ...this.$route.query };
      let item = null;
      if (pre) {
        item = this.pageData[this.currentIndex - 1];
      } else {
        item = this.pageData[this.currentIndex + 1];
      }
      if (item) {
        query.batchId = item.batchId;
        query.noteId = item.noteId;

        this.$router.replace({
          path: path,
          query: query
        });
        window.location.reload();
      }
    },
    goBack() {
      // 检查URL参数中是否包含discussionId
      const discussionId = this.$route.query.discussionId;

      if (discussionId) {
        // 如果包含discussionId参数，返回首页
        this.$router.push({
          name: "home"
        });
      } else if (
        this.roleId === this.$RoleEnum.annotator ||
        this.roleId === this.$RoleEnum.auditor
      ) {
        this.$router.push({
          path: "/annotask-list"
        });
      } else {
        this.$router.go(-1);
      }
    },
    /**
     * 打回
     */
    repulse() {
      if (this.roleId !== this.$RoleEnum.auditor) {
        return;
      }
      AnnoEventsBus.$emit("repulseTask");
    },
    /**
     * 元数据
     */
    openMetadata() {
      AnnoEventsBus.$emit("openMetadata");
    },
    // 切换标注类型
    switchAnnoType() {
      if (this.buttonName === "实体标注") {
        this.buttonName = "关系标注";
      } else {
        this.buttonName = "实体标注";
      }
      AnnoEventsBus.$emit("switchAnnoType", this.buttonName);
    },
    submitTask(nextArticle) {
      AnnoEventsBus.$emit("submitTask", nextArticle);
    },
    invalidTask(invalidStatus) {
      AnnoEventsBus.$emit("invalidTask", invalidStatus);
    },
    cancelTask() {
      AnnoEventsBus.$emit("cancelTask");
    },
    rework() {
      AnnoEventsBus.$emit("reworkTask");
    },
    // 显示帮助弹窗
    showHelpDialog() {
      this.helpDialogVisible = true;
      this.helpLoading = true;
      this.helpContent = "";

      // 获取项目ID
      const projectId = this.$store.state.anno.annotation.projectId;

      if (!projectId) {
        this.helpLoading = false;
        this.$message.warning("无法获取项目信息");
        return;
      }

      this.$http({
        url: this.$http.adornUrl("/label/entity/getCombinedRules"),
        method: "get",
        params: this.$http.adornParams({
          projectId: projectId
        })
      })
        .then(({ data }) => {
          this.helpLoading = false;
          if (data && data.code === 0 && data.data && data.data.content) {
            this.helpContent = data.data.content;
          } else {
            this.helpContent = "";
          }
        })
        .catch((error) => {
          this.helpContent = "";
          console.error("获取帮助文档失败:", error);
        })
        .finally(() => {
          this.helpLoading = false;
        });
    }
  }
};
</script>

<style scoped lang="scss">
.el-link.is-underline:hover:after {
  border-bottom: none !important;
}

.el-link:hover {
  color: #fff;
}

::v-deep .el-descriptions__body .el-descriptions__table {
  tbody {
    tr td:first-child {
      display: inline-block;
      margin-right: 40px;
    }
  }

  width: auto;
}

.site-navbar__menu .el-menu-item {
  padding-right: 5px;
}

.el-menu {
  .el-button--mini {
    padding: 7px;
    border-radius: 4px;
  }

  ::v-deep.el-menu-item {
    .relation {
      i {
        color: #c27e3a;
      }
    }

    .discard {
      i {
        color: #f56c6c;
      }
    }

    .submit {
      i {
        color: #409eff;
      }
    }

    i {
      margin-right: 0;
      width: auto;
      font-size: 12px;
    }
  }

  ::v-deep.el-button--mini:hover {
    i {
      color: #fff;
    }
  }
}

::v-deep.el-button--mini:focus {
  i {
    color: #fff !important;
  }
}

::v-deep .el-menu-item [class^="el-icon-"] {
  margin-right: 0;
}

::v-deep .el-descriptions--mini:not(.is-bordered) .el-descriptions-item__cell {
  padding-bottom: 3px;
}

.el-menu-item > span {
  height: auto !important;
  line-height: 4px !important;
}
</style>
<style>
.down-popover {
  display: flex;
}

.label-name {
  font-family: Myriad Pro, Arial, PingFang SC, Hiragino Sans GB, Microsoft YaHei,
    sans-serif;
  /*font-weight: bold;*/
  color: #323e3f;
  font-size: 14px;
}

.label-content {
  color: gray;
  font-size: 14px;
}

.el-descriptions__body .el-descriptions__table {
  white-space: nowrap;
}

.el-descriptions-item__content {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 8px;
}
</style>
