<template>
  <div>
    <div class="back-head">
      <a class="go-back" href="javascript:0;" @click="$router.go(-1)">
        <i class="el-icon-arrow-left"></i>返回
      </a>
      关系标签
    </div>
    <relation-label :project_id="project_id"></relation-label>
  </div>
</template>

<script scoped>
import RelationLabel from "./relation-label/list";

export default {
  name: "private-relation",
  props: {},
  components: { RelationLabel },
  computed: {
    project_id: function () {
      return this.$route.query.projectId;
    }
  },
  data() {
    return {};
  },
  methods: {},
  mounted() {},
  created() {}
};
</script>

<style scoped></style>
