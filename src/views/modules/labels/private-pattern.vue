<template>
  <div>
    <div class="back-head">
      <a
        class="go-back"
        href="javascript:0;"
        @click="
          () => {
            this.$router.go(-1);
          }
        "
        ><i class="el-icon-arrow-left"></i>返回</a
      >
      关系模板
    </div>
    <relation-pattern :project_id="project_id"></relation-pattern>
  </div>
</template>

<script scoped>
import RelationPattern from "./relation-pattern/list";

export default {
  props: {},
  components: { RelationPattern },
  data() {
    return {};
  },
  computed: {
    project_id: function () {
      return this.$route.query.projectId;
    }
  },
  methods: {},
  mounted() {},
  created() {}
};
</script>

<style scoped></style>
