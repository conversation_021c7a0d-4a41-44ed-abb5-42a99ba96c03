<template>
  <div>
    <div class="back-head">
      <a class="go-back" href="javascript:0;" @click="$router.go(-1)">
        <i class="el-icon-arrow-left"></i>返回
      </a>
      UMLS Concept消歧数据集
    </div>
    <umls-concept :project_id="project_id"></umls-concept>
  </div>
</template>

<script scoped>
import UmlsConcept from "./umls-concept/list";

export default {
  name: "private-umls",
  props: {},
  components: { UmlsConcept },
  computed: {
    project_id: function () {
      return this.$route.query.projectId;
    }
  },
  data() {
    return {};
  },
  methods: {},
  mounted() {},
  created() {}
};
</script>

<style scoped></style>
