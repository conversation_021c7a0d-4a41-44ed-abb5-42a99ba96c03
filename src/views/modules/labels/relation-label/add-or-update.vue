<template>
  <el-dialog
    v-loading="loading"
    :title="!dataForm.id ? '新增' : '编辑'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="80px"
    >
      <el-form-item label="标签名字" prop="name">
        <el-input v-model="dataForm.name" placeholder="标签名字"></el-input>
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input v-model="dataForm.description" placeholder="描述"></el-input>
      </el-form-item>
      <el-form-item label="特殊标签" prop="special">
        <el-radio-group v-model="dataForm.special">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    const validateName = (rule, value, callback) => {
      if (value.trim().toUpperCase() !== this.beforeName.trim().toUpperCase()) {
        this.$http({
          url: this.$http.adornUrl("/labels/relation/validateName"),
          method: "get",
          params: this.$http.adornParams({
            name: this.dataForm.name,
            projectId: this.dataForm.projectId
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            if (data.data) {
              callback(new Error("该标签名称已存在"));
            } else {
              callback();
            }
          }
        });
      } else {
        callback();
      }
    };
    return {
      loading: false,
      visible: false,
      dataForm: {
        id: 0,
        special: 0,
        name: "",
        description: "",
        status: 1,
        deleted: 0,
        projectId: ""
      },
      beforeName: "",
      dataRule: {
        name: [
          { required: true, message: "标签名字不能为空", trigger: "blur" },
          { validator: validateName, trigger: "blur" }
        ]
      }
    };
  },
  methods: {
    init(id, projectId) {
      this.dataForm.id = id || 0;
      this.visible = true;
      this.dataForm.projectId = projectId;
      this.$nextTick(() => {
        this.$refs.dataForm.resetFields();
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/labels/relation/info/${this.dataForm.id}`
            ),
            method: "get",
            params: this.$http.adornParams()
          }).then(({ data }) => {
            if (data && data.code === 0 && data.data) {
              this.dataForm.special = data.data.special;
              this.dataForm.name = data.data.name;
              this.dataForm.description = data.data.description;
              this.dataForm.status = data.data.status;
              this.dataForm.projectId = data.data.projectId;
              this.beforeName = data.data.name;
            }
          });
        }
      });
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          this.$http({
            url: this.$http.adornUrl(
              `/labels/relation/${!this.dataForm.id ? "save" : "update"}`
            ),
            method: "post",
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              special: this.dataForm.special,
              name: this.dataForm.name,
              status: this.dataForm.status,
              delete: this.dataForm.deleted,
              description: this.dataForm.description,
              projectId: this.dataForm.projectId
            })
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: "操作成功",
                type: "success",
                duration: 1500
              });
              this.loading = false;
              this.visible = false;
              this.$emit("refreshDataList");
            } else {
              this.$message.error(data.msg);
            }
          });
        }
      });
    }
  }
};
</script>
