<template>
  <div class="mod-config">
    <div class="d-flex d-space-between">
      <el-form
        :inline="true"
        :model="dataForm"
        @keyup.enter.native="getDataList()"
      >
        <el-form-item label="名称">
          <el-input
            class="input-width"
            v-model="dataForm.name"
            placeholder="名称"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item v-show="project_id !== 0" label="状态">
          <el-select
            class="status-width"
            v-model="dataForm.status"
            placeholder="状态"
            clearable
          >
            <el-option label="启用" value="1"></el-option>
            <el-option label="禁用" value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="创建人" v-show="project_id !== 0">
          <el-select class="status-width" v-model="dataForm.userId" clearable>
            <div v-for="(value, key) in userList" :key="'list1' + key">
              <el-option :label="value" :value="key"></el-option>
            </div>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            icon="el-icon-search"
            @click="
              () => {
                this.pageIndex = 1;
                getDataList();
              }
            "
            >查询
          </el-button>
          <el-button
            icon="el-icon-plus"
            v-if="editable"
            type="primary"
            @click="addOrUpdateHandle()"
            >新增
          </el-button>
        </el-form-item>
      </el-form>
      <div v-if="editable">
        <el-button
          type="primary"
          size="small"
          @click="bulkUpload"
          icon="el-icon-upload2"
          >导入
        </el-button>
        <el-button
          type="primary"
          size="small"
          @click="handleExport"
          icon="el-icon-download"
          >导出
        </el-button>
      </div>
    </div>

    <upload-relation
      v-if="loadFileVisible"
      ref="loadFile"
      @refreshDataList="getDataList"
    ></upload-relation>

    <el-table
      :height="tableH"
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%"
    >
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50"
      >
      </el-table-column>
      <el-table-column
        width="50"
        prop="id"
        header-align="center"
        align="center"
        label="ID"
      >
      </el-table-column>
      <el-table-column
        prop="name"
        header-align="center"
        align="center"
        label="标签名称"
      >
      </el-table-column>
      <el-table-column
        prop="description"
        header-align="center"
        align="center"
        label="描述"
      >
      </el-table-column>
      <el-table-column
        prop="special"
        header-align="center"
        align="center"
        width="80"
        label="特殊标签"
      >
        <template slot-scope="scope">
          <el-tag v-if="scope.row.special === 1" size="small" type="danger"
            >是
          </el-tag>
          <el-tag v-if="scope.row.special === 0" size="small">否</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        v-if="project_id !== 0"
        prop="userId"
        header-align="center"
        align="center"
        width="150"
        label="创建人"
      >
        <template slot-scope="scope">
          <div>{{ userMap.get(scope.row.userId + "") }}</div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="project_id !== 0"
        prop="createTime"
        header-align="center"
        align="center"
        width="180"
        label="创建时间"
      >
      </el-table-column>
      <el-table-column
        v-if="project_id !== 0"
        width="70"
        prop="status"
        header-align="center"
        align="center"
        label="状态"
      >
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            active-color="#13ce66"
            inactive-color="#ff4949"
            :active-value="1"
            :inactive-value="0"
            :disabled="!editable"
            @change="changeStatus(scope.row)"
          >
          </el-switch>
          <!--          <el-tag v-if="scope.row.status === 1" size="small">启用</el-tag>-->
          <!--          <el-tag v-if="scope.row.status === 0" size="small" type="danger">禁用</el-tag>-->
        </template>
      </el-table-column>
      <el-table-column
        v-if="editable"
        header-align="center"
        align="center"
        width="100"
        label="操作"
      >
        <template slot-scope="scope">
          <!--          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>-->
          <!--          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>-->
          <el-tooltip content="编辑" placement="top" effect="light">
            <i
              class="el-icon-edit icon-primary"
              @click="addOrUpdateHandle(scope.row.id)"
            ></i>
          </el-tooltip>
          <el-tooltip content="删除" placement="top" effect="light">
            <i
              class="el-icon-delete icon-red"
              @click="deleteHandle(scope.row.id)"
            ></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <div class="d-flex align-items-center mt-1" v-show="dataList.length > 0">
      <div>
        <el-button
          v-if="editable"
          type="success"
          @click="submitChange(1)"
          size="small"
          icon="el-icon-check"
          :disabled="dataListSelections.length <= 0"
          >批量启用
        </el-button>
        <el-button
          v-if="editable"
          type="warning"
          size="small"
          @click="submitChange(0)"
          icon="el-icon-close"
          :disabled="dataListSelections.length <= 0"
          >批量禁用
        </el-button>
        <el-button
          v-if="editable"
          type="danger"
          size="small"
          @click="deleteHandle()"
          icon="el-icon-delete"
          :disabled="dataListSelections.length <= 0"
          >批量删除
        </el-button>
      </div>
      <div class="ml-auto pb-1">
        <el-pagination
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
          :current-page="pageIndex"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper"
        >
        </el-pagination>
      </div>
    </div>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    ></add-or-update>
  </div>
</template>

<script>
import AddOrUpdate from "./add-or-update";
import UploadRelation from "./upload-relation";
import * as XLSX from "xlsx";

export default {
  name: "relation-label",
  data() {
    return {
      tableH: undefined,
      userList: [],
      dataForm: {
        code: "",
        name: "",
        status: "",
        projectId: "",
        special: "",
        userId: ""
      },
      loadFileVisible: false,
      dataList: [],
      pageIndex: 1,
      pageSize: 20,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false
    };
  },
  props: ["project_id"],
  components: {
    AddOrUpdate,
    UploadRelation
  },
  mounted() {
    this.getDataList();
    this.tableH = document.documentElement.clientHeight - 290;
    window.onresize = () => {
      return (() => {
        this.tableH = document.documentElement.clientHeight - 290;
      })();
    };
  },
  computed: {
    userMap: function () {
      const tempMap = new Map();
      for (const k of Object.keys(this.userList)) {
        tempMap.set(k, this.userList[k]);
      }
      return tempMap;
    },
    editable: function () {
      return this.isAuth("project:editable");
    }
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataForm.projectId = this.project_id;
      this.dataListLoading = true;
      this.$http({
        url: this.$http.adornUrl("/labels/relation/list"),
        method: "get",
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          userId: this.dataForm.userId,
          name: this.dataForm.name,
          status: this.dataForm.status,
          projectId: this.dataForm.projectId
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list || [];
          this.totalPage = data.page.totalCount;
          if (data.userList) {
            this.userList = data.userList;
          } else {
            this.userList = [];
          }
        } else {
          this.dataList = [];
          this.totalPage = 0;
        }
        this.dataListLoading = false;
      });
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val;
      this.pageIndex = 1;
      this.getDataList();
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val;
      this.getDataList();
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val;
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true;
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id, this.project_id);
      });
    },
    // 批量上传
    bulkUpload() {
      this.loadFileVisible = true;
      this.$nextTick(() => {
        this.$refs.loadFile.init(this.dataForm.projectId);
      });
    },
    gotoSchema() {
      this.$router.push({
        path: "/labels/relation/private-pattern",
        query: { project_id: this.project_id }
      });
    },
    changeStatus(row) {
      this.$http({
        url: this.$http.adornUrl("/labels/relation/update"),
        method: "post",
        data: this.$http.adornData({
          id: row.id,
          status: row.status
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.$message({
            message: "操作成功",
            type: "success",
            duration: 1500
          });
          this.visible = false;
          this.$emit("refreshDataList");
        } else {
          this.$message.error(data.msg);
          this.getDataList();
        }
      });
    },
    // 删除
    deleteHandle(id) {
      const ids = id
        ? [id]
        : this.dataListSelections.map((item) => {
            return item.id;
          });
      this.$confirm(`确定进行[${id ? "删除" : "批量删除"}]操作?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.$http({
            url: this.$http.adornUrl("/labels/relation/delete"),
            method: "post",
            data: this.$http.adornData(ids, false)
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: "操作成功",
                type: "success",
                duration: 1500
              });
              this.getDataList();
            } else {
              const map = JSON.parse(data.msg);
              const msg = map.map((x) => x.name);
              this.$message.error(msg + "已被使用,不能删除!");
              this.getDataList();
            }
          });
        })
        .catch(() => {});
    },
    handleExport() {
      this.$confirm(`确定进行导出全部关系标签?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.$http({
            url: this.$http.adornUrl(
              `/labels/relation/exportByProjectId/${this.project_id}`
            ),
            method: "get"
          })
            .then(({ data }) => {
              if (data && data.code === 0) {
                let downloadList = data.data;
                if (!data.data || downloadList.length <= 0) {
                  this.$message.info("没有数据导出");
                } else {
                  const headers = ["标签名称", "标签描述", "是否特殊标签"];
                  const fields = ["name", "description", "special"];
                  const wb = XLSX.utils.book_new();
                  // 生成 Worksheet 对象
                  const ws = XLSX.utils.json_to_sheet(downloadList, {
                    header: fields
                  });
                  XLSX.utils.sheet_add_aoa(ws, [headers], { origin: "A1" });
                  // 添加 Worksheet 到 Workbook
                  XLSX.utils.book_append_sheet(wb, ws, "Sheet1");
                  // 将 Workbook 导出为 Excel 文件
                  XLSX.writeFile(wb, `${this.project_id}-关系标签.xlsx`);
                }
              }
            })
            .catch((err) => {
              this.$message.error(err);
            });
        })
        .catch(() => {});
    },
    submitChange(status) {
      const ids = this.dataListSelections.map((item) => {
        return item.id;
      });
      this.$confirm(
        `确定进行【${status === 1 ? "批量启用" : "批量禁用"}】操作?`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      )
        .then(() => {
          this.dataListLoading = true;
          this.$http({
            url: this.$http.adornUrl(`/labels/relation/status/${status}`),
            method: "post",
            data: this.$http.adornData(ids, false)
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: "操作成功",
                type: "success",
                duration: 3000
              });
            } else {
              const map = JSON.parse(data.msg);
              const msg = map.map((x) => x.name);
              this.$message.error(msg + "已被使用,不能禁用!");
              this.getDataList();
            }
            this.dataListLoading = false;
            this.getDataList();
          });
        })
        .catch(() => {});
    }
  }
};
</script>
