<template>
  <el-dialog
    v-loading="loading"
    :title="title"
    :close-on-click-modal="false"
    :visible.sync="visible"
    :width="'70%'"
  >
    <el-row v-show="editable" type="flex" justify="space-between">
      <el-form
        :inline="true"
        ref="dataForm"
        :model="dataForm"
        :rules="dataRule"
        style="display: flex"
      >
        <el-form-item label="属性名" prop="name">
          <el-input
            v-model="dataForm.name"
            placeholder="属性名"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="属性字段" prop="field">
          <el-input
            v-model="dataForm.field"
            placeholder="属性字段"
            clearable
          ></el-input>
        </el-form-item>

        <el-form-item label="排序" prop="orderNumber">
          <el-input-number
            v-model="dataForm.orderNumber"
            :controls="false"
            placeholder="序号"
            clearable
          ></el-input-number>
        </el-form-item>

        <el-form-item>
          <el-button
            icon="el-icon-plus"
            v-if="editable"
            type="primary"
            @click="addAttribute()"
            >新增
          </el-button>
        </el-form-item>
      </el-form>
    </el-row>
    <!--列表-->
    <el-table
      :data="tableData.slice((page - 1) * size, page * size)"
      border
      :row-style="{ height: '30px' }"
      style="width: 100%"
      type="expand"
    >
      <el-table-column
        label="属性名"
        prop="name"
        header-align="center"
        align="center"
      >
      </el-table-column>
      <el-table-column
        align="center"
        label="属性字段"
        prop="field"
      ></el-table-column>

      <el-table-column
        align="center"
        label="排序"
        prop="orderNumber"
      ></el-table-column>

      <el-table-column
        align="center"
        v-if="editable"
        label="操作"
        prop="alteration"
      >
        <template slot-scope="scope">
          <el-tooltip content="修改" placement="top" effect="light">
            <i
              class="el-icon-edit icon-primary"
              @click="showEditDialog(scope.row)"
            ></i>
          </el-tooltip>
          <el-tooltip content="删除" placement="top" effect="light">
            <i
              class="el-icon-delete icon-red"
              @click="deleteAttribute(scope.row.id)"
            ></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination
        @size-change="sizeChange"
        @current-change="currentChange"
        :current-page="page"
        :page-size="size"
        :page-sizes="pageSizes"
        current-page.sync="1"
        :total="total"
        layout="total, prev, pager, next"
      >
      </el-pagination>
    </div>
    <el-dialog
      title="编辑属性标签"
      :visible.sync="editVisible"
      append-to-body
      width="40%"
      :close-on-click-modal="false"
      @close="closeEditDialog"
    >
      <el-form :model="dataForm" :rules="dataRule" ref="attrForm">
        <el-form-item label="属性名" prop="name">
          <el-input
            v-model="dataForm.name"
            placeholder="属性名"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="属性字段" prop="field">
          <el-input
            v-model="dataForm.field"
            placeholder="属性字段"
            clearable
          ></el-input>
        </el-form-item>

        <el-form-item label="排序" prop="orderNumber">
          <el-input-number
            v-model="dataForm.orderNumber"
            :controls="false"
            placeholder="序号"
          ></el-input-number>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeEditDialog">取消</el-button>
        <el-button type="primary" @click="updateAttribute()">确定</el-button>
      </div>
    </el-dialog>
  </el-dialog>
</template>
<script>
export default {
  name: "add-attribute",
  data() {
    const validateName = (rule, value, callback) => {
      this.$http({
        url: this.$http.adornUrl("/label/attribute/existByNameInProject"),
        method: "get",
        params: this.$http.adornParams({
          id: this.dataForm.id,
          name: this.dataForm.name,
          entityLabelId: this.entityLabelId
        })
      }).then(({ data }) => {
        if (data.data) {
          callback(new Error("该属性名已存在"));
        } else {
          callback();
        }
      });
    };
    const validateField = (rule, value, callback) => {
      this.$http({
        url: this.$http.adornUrl("/label/attribute/existByFieldInProject"),
        method: "get",
        params: this.$http.adornParams({
          id: this.dataForm.id,
          field: this.dataForm.field,
          entityLabelId: this.entityLabelId
        })
      }).then(({ data }) => {
        if (data.data) {
          callback(new Error("该属性字段已存在"));
        } else {
          callback();
        }
      });
    };

    const validateOderNum = (rule, value, callback) => {
      if (value === "" || value === undefined || value === null) {
        callback();
      } else {
        let num = Number(value);
        if (isNaN(num)) {
          callback(new Error("非法的数字"));
        } else if (num < 1 || num > 2000) {
          callback(new Error("序号超出范围1~2000"));
        } else {
          callback();
        }
      }
    };
    return {
      tableData: [],
      dataForm: {
        name: "",
        field: "",
        orderNumber: undefined
      },
      editVisible: false,
      entityLabelId: "",
      visible: false,
      loading: false,
      page: 1,
      size: 7,
      total: 0,
      pageSizes: [10, 20, 50, 100], //可选择的一页多少条
      title: "属性管理",
      dataRule: {
        name: [
          {
            required: true,
            message: "属性名不能为空",
            trigger: "blur"
          },
          { validator: validateName, trigger: "blur" }
        ],
        field: [
          {
            required: true,
            message: "属性字段不能为空",
            trigger: "blur"
          },
          {
            required: true,
            pattern: /^(?![0-9_])[a-zA-Z0-9_]+$/,
            message: "英文、下划线、数字，不能划线开头",
            trigger: "blur",
            style: {
              fontSize: "10 px",
              width: "100px"
            }
          },
          { validator: validateField, trigger: "blur" }
        ],
        orderNumber: [{ validator: validateOderNum, trigger: "blur" }]
      }
    };
  },
  computed: {
    editable: function () {
      return this.isAuth("project:editable");
    }
  },
  mounted() {},
  methods: {
    init(id) {
      this.visible = true;
      this.entityLabelId = id;
      this.getDataTable(id);
    },
    initNumber(n) {
      if (n !== null && n !== undefined) {
        return Number(n);
      } else {
        return undefined;
      }
    },
    showEditDialog(row) {
      this.editVisible = true;
      this.dataForm.id = row.id;
      this.dataForm.name = row.name;
      this.dataForm.field = row.field;
      this.dataForm.orderNumber = this.initNumber(row.orderNumber);
    },
    closeEditDialog() {
      this.editVisible = false;
      this.$refs.dataForm.resetFields();
    },
    updateAttribute() {
      this.$refs.attrForm.validate((valid) => {
        if (!valid) {
          return;
        }
        const loading = this.$loading({
          lock: true
        });
        this.$http({
          url: this.$http.adornUrl(`/label/attribute/save`),
          method: "post",
          data: this.$http.adornData({
            id: this.dataForm.id,
            name: this.dataForm.name,
            field: this.dataForm.field,
            orderNumber: this.dataForm.orderNumber,
            entityLabelId: this.entityLabelId
          })
        })
          .then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: "编辑成功",
                type: "success"
              });
              this.getDataTable(this.entityLabelId);
              this.$refs.dataForm.resetFields(); //重置表单数据，清除校验信息
              this.editVisible = false;
            } else {
              this.$message.error(data.msg);
              this.editVisible = false;
            }
          })
          .finally(() => {
            loading.close();
          });
      });
    },
    addAttribute() {
      this.$refs.dataForm.validate((valid) => {
        if (!valid) {
          return;
        }
        this.loading = true;
        this.$http({
          url: this.$http.adornUrl(`/label/attribute/save`),
          method: "post",
          data: this.$http.adornData({
            id: null,
            name: this.dataForm.name,
            field: this.dataForm.field,
            status: this.dataForm.status,
            orderNumber: this.dataForm.orderNumber,
            entityLabelId: this.entityLabelId
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.getDataTable(this.entityLabelId);
            this.$refs.dataForm.resetFields(); //重置表单数据，清除校验信息
            this.loading = false;
          } else {
            this.$message.error(data.msg);
            this.loading = false;
          }
        });
      });
    },
    deleteAttribute(id) {
      this.$confirm(`确定进行删除操作?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.loading = true;
          this.$http({
            url: this.$http.adornUrl("/label/attribute/delete"),
            method: "post",
            data: this.$http.adornData(id, false)
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: "操作成功",
                type: "success",
                duration: 1500
              });
            }
            this.loading = false;
            this.getDataTable(this.entityLabelId);
          });
        })
        .catch(() => {});
    },
    getDataTable(id) {
      this.page = 1;
      this.$http({
        url: this.$http.adornUrl(`/label/attribute/findAllByEntityLabelId`),
        method: "get",
        params: this.$http.adornParams(
          {
            eneityLabelId: id
          },
          false
        )
      }).then((data) => {
        this.$refs.dataForm.resetFields(); //重置表单数据，清除校验信息
        this.tableData = data.data.data || [];
        this.total = this.tableData.length;
      });
    },

    //page改变时的回调函数，参数为当前页码
    currentChange(val) {
      this.page = val;
    },
    //size改变时回调的函数，参数为当前的size
    sizeChange(val) {
      this.size = val;
      this.page = 1;
    }
  }
};
</script>
<style scoped>
::v-deep .el-form-item--medium .el-form-item__content {
  width: 70%;
}
</style>
