<template>
  <el-dialog
    v-loading="loading"
    :title="!dataForm.id ? '新增' : '编辑'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="80px"
    >
      <el-form-item label="名称" prop="name">
        <el-select
          v-model="dataForm.name"
          v-loadMore="handleScdpLabelLoadMore"
          filterable
          remote
          reserve-keyword
          placeholder="请选择或输入标签名称"
          class="w-100"
          clearable
          :remote-method="remoteScdpLabelSelect"
          :loading="scdpLabelSelectLoading"
          popper-class="scdp-label-select"
          :popper-class-name="'scdp-label-select'"
          @visible-change="handleScdpLabelVisibleChange"
          style="width: 100%"
        >
          <el-option
            v-for="item in scdpLabelOptions"
            :key="item.labelCode + '-' + item.labelVersion"
            :label="item.labelName"
            :value="item.labelName"
          >
            <div style="display: flex; justify-content: space-between">
              <span>{{ item.labelName }}</span>
              <span style="color: #8492a6; font-size: 13px">{{
                item.labelCode
              }}</span>
            </div>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input v-model="dataForm.description" placeholder="描述"></el-input>
      </el-form-item>
      <el-form-item label="颜色" prop="color">
        <div style="display: flex">
          <el-color-picker
            v-model="dataForm.color"
            :predefine="predefineColors"
          ></el-color-picker>
          <span style="margin-left: 10px">{{ dataForm.color }}</span>
        </div>
      </el-form-item>
      <el-form-item label="序号" prop="orderNum">
        <el-input-number
          :max="999"
          :min="1"
          v-model="dataForm.orderNum"
          placeholder="排序字段"
        ></el-input-number>
      </el-form-item>
      <el-form-item label="是否消歧" prop="disambiguate" size="mini">
        <el-radio-group v-model="dataForm.disambiguate">
          <el-radio :label="false">否</el-radio>
          <el-radio :label="true">是</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="状态" prop="status" size="mini">
        <el-radio-group v-model="dataForm.status">
          <el-radio :label="1">正常</el-radio>
          <el-radio :label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    const validateName = (rule, value, callback) => {
      if (value.trim() !== this.beforeName.trim()) {
        this.$http({
          url: this.$http.adornUrl("/label/entity/existByName"),
          method: "get",
          params: this.$http.adornParams({
            name: this.dataForm.name,
            projectId: this.dataForm.projectId
          })
        }).then(({ data }) => {
          if (data.data) {
            callback(new Error("该标签名称已存在"));
          } else {
            callback();
          }
        });
      } else {
        callback();
      }
    };
    return {
      loading: false,
      visible: false,
      title: "新增标签",
      dataForm: {
        id: "",
        name: "",
        description: "",
        color: "#FF4500",
        orderNum: 1,
        disambiguate: false,
        status: 1,
        projectId: ""
      },
      beforeName: "",
      dataList: [],
      // SCDP标签选择器相关数据
      scdpLabelOptions: [],
      scdpLabelSelectLoading: false,
      scdpLabelSelectQueryParams: {
        name: "",
        page: 1,
        limit: 200
      },
      dataRule: {
        color: [
          {
            required: true,
            message: "颜色不能为空",
            trigger: "blur"
          }
        ],
        name: [
          {
            required: true,
            message: "标签名称不能为空",
            trigger: "blur"
          },
          { validator: validateName, trigger: "blur" }
        ]
      },
      // 颜色选择器的默认颜色
      predefineColors: [
        "#89C5F6",
        "#8DFFFF",
        "#8FF2CE",
        "#A4D8C2",
        "#C2EB7C",
        "#DAABEF",
        "#ED8C8C",
        "#F2D643",
        "#F5E8C8",
        "#FE8463"
      ]
    };
  },
  computed: {},
  methods: {
    // 处理SCDP标签选择器加载更多
    handleScdpLabelLoadMore() {
      this.scdpLabelSelectQueryParams.page += 1;
      this.remoteScdpLabelSelect(this.scdpLabelSelectQueryParams.name);
    },
    // 远程搜索SCDP标签
    remoteScdpLabelSelect(queryString) {
      this.scdpLabelSelectLoading = true;
      this.scdpLabelSelectQueryParams.name = queryString || "";

      this.debouncedRemoteScdpLabelSelect();
    },
    // 防抖的远程搜索SCDP标签
    debouncedRemoteScdpLabelSelect() {
      if (!this._debouncedSearch) {
        this._debouncedSearch = this.$_.debounce(() => {
          this.searchScdpLabels();
        }, 300);
      }
      this._debouncedSearch();
    },
    // 实际的搜索方法
    searchScdpLabels() {
      this.$http({
        url: this.$http.adornUrl("/label/entity/searchScdpLabel"),
        method: "get",
        params: this.$http.adornParams({
          name: this.scdpLabelSelectQueryParams.name,
          page: this.scdpLabelSelectQueryParams.page,
          limit: this.scdpLabelSelectQueryParams.limit
        })
      })
        .then(({ data }) => {
          if (data && data.code === 0) {
            if (this.scdpLabelSelectQueryParams.page === 1) {
              this.scdpLabelOptions = [];
            }
            this.scdpLabelOptions.push(...(data.data || []));
          } else {
            this.$message.error(data.msg || "获取SCDP标签选项失败");
          }
        })
        .catch((error) => {
          console.error("获取SCDP标签选项失败:", error);
          this.$message.error("获取SCDP标签选项失败");
        })
        .finally(() => {
          this.scdpLabelSelectLoading = false;
        });
    },
    // 处理SCDP标签选择器显示状态变化
    handleScdpLabelVisibleChange(visible) {
      if (visible) {
        // 重置分页参数
        this.scdpLabelSelectQueryParams.page = 1;
        this.scdpLabelSelectQueryParams.name = "";
        // 初始加载数据
        this.remoteScdpLabelSelect("");
      }
    },
    // 新增、编辑
    init(id, projectId, color = this.predefineColors[0]) {
      this.dataForm.id = id || 0;
      this.dataForm.projectId = projectId;
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.dataForm.resetFields();
        this.dataForm.color = color;
        if (this.dataForm.id) {
          this.$refs.dataForm.resetFields();
          this.dataForm.color = color;
          this.$http({
            url: this.$http.adornUrl(`/label/entity/info/${this.dataForm.id}`),
            method: "get",
            params: this.$http.adornParams()
          }).then(({ data }) => {
            if (data && data.code === 0 && data.data) {
              this.dataForm = data.data;
              this.beforeName = data.data.name;
            }
          });
        }
      });
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          this.$http({
            url: this.$http.adornUrl(
              `/label/entity/${!this.dataForm.id ? "save" : "update"}`
            ),
            method: "post",
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              color: this.dataForm.color,
              orderNum: this.dataForm.orderNum,
              name: this.dataForm.name,
              description: this.dataForm.description || undefined,
              status: this.dataForm.status,
              disambiguate: this.dataForm.disambiguate,
              projectId: this.dataForm.projectId
            })
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: "操作成功",
                type: "success",
                duration: 1500
              });
              this.visible = false;
              this.$emit("refreshDataList");
              this.loading = false;
            } else {
              this.$message.error(data.msg);
              this.loading = false;
            }
          });
        }
      });
    }
  }
};
</script>

<style scoped>
.w-100 {
  width: 100%;
}
</style>
