<template>
  <el-dialog
    v-loading="loading"
    :title="!dataForm.id ? '新增' : '编辑'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="80px"
    >
      <el-form-item label="名称" prop="name">
        <el-input v-model="dataForm.name" placeholder="名称"></el-input>
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input v-model="dataForm.description" placeholder="描述"></el-input>
      </el-form-item>
      <el-form-item label="颜色" prop="color">
        <div style="display: flex">
          <el-color-picker
            v-model="dataForm.color"
            :predefine="predefineColors"
          ></el-color-picker>
          <span style="margin-left: 10px">{{ dataForm.color }}</span>
        </div>
      </el-form-item>
      <el-form-item label="序号" prop="orderNum">
        <el-input-number
          :max="999"
          :min="1"
          v-model="dataForm.orderNum"
          placeholder="排序字段"
        ></el-input-number>
      </el-form-item>
      <el-form-item label="是否消歧" prop="disambiguate" size="mini">
        <el-radio-group v-model="dataForm.disambiguate">
          <el-radio :label="false">否</el-radio>
          <el-radio :label="true">是</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="状态" prop="status" size="mini">
        <el-radio-group v-model="dataForm.status">
          <el-radio :label="1">正常</el-radio>
          <el-radio :label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    const validateName = (rule, value, callback) => {
      if (value.trim() !== this.beforeName.trim()) {
        this.$http({
          url: this.$http.adornUrl("/label/entity/existByName"),
          method: "get",
          params: this.$http.adornParams({
            name: this.dataForm.name,
            projectId: this.dataForm.projectId
          })
        }).then(({ data }) => {
          if (data.data) {
            callback(new Error("该标签名称已存在"));
          } else {
            callback();
          }
        });
      } else {
        callback();
      }
    };
    return {
      loading: false,
      visible: false,
      title: "新增标签",
      dataForm: {
        id: "",
        name: "",
        description: "",
        color: "#FF4500",
        orderNum: 1,
        disambiguate: false,
        status: 1,
        projectId: ""
      },
      beforeName: "",
      dataList: [],
      dataRule: {
        color: [
          {
            required: true,
            message: "颜色不能为空",
            trigger: "blur"
          }
        ],
        name: [
          {
            required: true,
            message: "标签名称不能为空",
            trigger: "blur"
          },
          { validator: validateName, trigger: "blur" }
        ]
      },
      // 颜色选择器的默认颜色
      predefineColors: [
        "#89C5F6",
        "#8DFFFF",
        "#8FF2CE",
        "#A4D8C2",
        "#C2EB7C",
        "#DAABEF",
        "#ED8C8C",
        "#F2D643",
        "#F5E8C8",
        "#FE8463"
      ]
    };
  },
  computed: {},
  methods: {
    // 新增、编辑
    init(id, projectId, color = this.predefineColors[0]) {
      this.dataForm.id = id || 0;
      this.dataForm.projectId = projectId;
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.dataForm.resetFields();
        this.dataForm.color = color;
        if (this.dataForm.id) {
          this.$refs.dataForm.resetFields();
          this.dataForm.color = color;
          this.$http({
            url: this.$http.adornUrl(`/label/entity/info/${this.dataForm.id}`),
            method: "get",
            params: this.$http.adornParams()
          }).then(({ data }) => {
            if (data && data.code === 0 && data.data) {
              this.dataForm = data.data;
              this.beforeName = data.data.name;
            }
          });
        }
      });
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          this.$http({
            url: this.$http.adornUrl(
              `/label/entity/${!this.dataForm.id ? "save" : "update"}`
            ),
            method: "post",
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              color: this.dataForm.color,
              orderNum: this.dataForm.orderNum,
              name: this.dataForm.name,
              description: this.dataForm.description || undefined,
              status: this.dataForm.status,
              disambiguate: this.dataForm.disambiguate,
              projectId: this.dataForm.projectId
            })
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: "操作成功",
                type: "success",
                duration: 1500
              });
              this.visible = false;
              this.$emit("refreshDataList");
              this.loading = false;
            } else {
              this.$message.error(data.msg);
              this.loading = false;
            }
          });
        }
      });
    }
  }
};
</script>
