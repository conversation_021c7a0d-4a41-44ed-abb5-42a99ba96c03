<template>
  <el-dialog
    v-loading="loading"
    :title="title"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form ref="dataForm" :inline="true" :model="dataForm">
      <el-form-item label="属性名" prop="attrLabelId">
        <el-select filterable v-model="dataForm.attrLabelId" clearable>
          <div v-for="(value, key) in attrLabelList" :key="'list1' + key">
            <el-option :label="value" :value="key"></el-option>
          </div>
        </el-select>
      </el-form-item>
      <el-form-item label="标注人" prop="userId">
        <el-select filterable v-model="dataForm.userId" clearable>
          <div v-for="(value, key) in userList" :key="'list2' + key">
            <el-option :label="value" :value="key"></el-option>
          </div>
        </el-select>
      </el-form-item>
    </el-form>
    <el-table
      :data="filteredList"
      border
      v-loading="dataListLoading"
      style="width: 100%"
      :default-sort="{ prop: 'createTime', order: 'descending' }"
    >
      <el-table-column
        prop="attrLabelId"
        sortable
        header-align="center"
        align="center"
        label="属性名"
      >
        <template v-slot="scope">
          <div>
            {{ attrLabelMap.get(scope.row.attrLabelId) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="content"
        header-align="center"
        align="center"
        label="属性值"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
      <el-table-column
        prop="attributeId"
        sortable
        width="150"
        header-align="center"
        align="center"
        label="属性类型"
      >
        <template v-slot="scope">
          <div>
            {{ scope.row.attributeId ? "标注属性" : "自填属性" }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        width="130"
        header-align="center"
        align="center"
        label="标注人"
        :show-overflow-tooltip="true"
      >
        <template v-slot="scope">
          <div>
            {{
              scope.row.annotatorId
                ? userMap.get(scope.row.annotatorId)
                : userMap.get(scope.row.auditorId)
            }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        width="200"
        prop="createTime"
        header-align="center"
        sortable
        align="center"
        label="标注时间"
      >
      </el-table-column>
    </el-table>
  </el-dialog>
</template>

<script>
export default {
  name: "attr-list",
  data() {
    return {
      dataForm: {
        attrLabelId: null,
        userId: null
      },
      loading: false,
      visible: false,
      title: "属性详情",
      dataList: [],
      userList: [],
      attrLabelList: [],
      dataListLoading: false
    };
  },
  computed: {
    userMap: function () {
      const tempMap = new Map();
      for (const k of Object.keys(this.userList)) {
        tempMap.set(Number.parseInt(k), this.userList[k]);
      }
      return tempMap;
    },
    attrLabelMap: function () {
      const tempMap = new Map();
      for (const k of Object.keys(this.attrLabelList)) {
        tempMap.set(Number.parseInt(k), this.attrLabelList[k]);
      }
      return tempMap;
    },
    filteredList: function () {
      let arr = this.dataList;
      if (this.dataForm.attrLabelId) {
        arr = arr.filter(
          (item) =>
            item.attrLabelId === Number.parseInt(this.dataForm.attrLabelId)
        );
      }
      if (this.dataForm.userId) {
        arr = arr.filter(
          (item) =>
            item.annotatorId === Number.parseInt(this.dataForm.userId) ||
            item.auditorId === Number.parseInt(this.dataForm.userId)
        );
      }
      return arr;
    }
  },
  methods: {
    init(entityId) {
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.dataForm.resetFields();
        this.dataListLoading = true;
        this.$http({
          url: this.$http.adornUrl(`/attr/findAttrListByEntityId`),
          method: "get",
          params: this.$http.adornParams({
            entityId: entityId
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.dataList = data.data.list || [];
            this.userList = data.data.userMap || [];
            this.attrLabelList = data.data.attrLabelMap || [];
          }
          this.dataListLoading = false;
        });
      });
    }
  }
};
</script>

<style scoped></style>
