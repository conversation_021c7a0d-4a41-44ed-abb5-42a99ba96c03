<template>
  <el-dialog
    title="配置标签规则"
    :visible.sync="visible"
    width="80%"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="label-rules-container">
      <!-- Markdown 编辑器 -->
      <div class="editor-container">
        <mavon-editor
          ref="md"
          v-model="markdownContent"
          :toolbars="toolbars"
          :ishljs="true"
          @change="handleMarkdownChange"
          style="height: 60vh"
        />
      </div>

      <!-- 标签状态提示 -->
      <div class="label-status-container">
        <el-card shadow="never">
          <div slot="header" class="clearfix">
            <span>标签配置状态</span>
          </div>
          <div class="status-content">
            <div class="status-section">
              <h4>已配置标签 ({{ configuredLabels.length }})</h4>
              <el-tag
                v-for="label in configuredLabels"
                :key="label"
                type="success"
                size="small"
                class="label-tag"
              >
                {{ label }}
              </el-tag>
              <span v-if="configuredLabels.length === 0" class="empty-text"
                >暂无</span
              >
            </div>
            <div class="status-section">
              <h4>未配置标签 ({{ unconfiguredLabels.length }})</h4>
              <el-tag
                v-for="label in unconfiguredLabels"
                :key="label"
                type="warning"
                size="small"
                class="label-tag"
              >
                {{ label }}
              </el-tag>
              <span v-if="unconfiguredLabels.length === 0" class="empty-text"
                >暂无</span
              >
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSave" :loading="saving"
        >保存</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import { mavonEditor } from "mavon-editor";
import "mavon-editor/dist/css/index.css";

export default {
  name: "LabelRulesConfig",
  components: {
    mavonEditor
  },
  props: {
    projectId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      visible: false,
      saving: false,
      markdownContent: "",
      allLabels: [], // 所有标签列表
      configuredLabels: [], // 已配置的标签
      unconfiguredLabels: [], // 未配置的标签
      toolbars: {
        bold: true,
        italic: true,
        header: true,
        underline: true,
        strikethrough: true,
        mark: true,
        superscript: true,
        subscript: true,
        quote: true,
        ol: true,
        ul: true,
        link: true,
        imagelink: true,
        code: true,
        table: true,
        fullscreen: true,
        readmodel: true,
        htmlcode: true,
        help: true,
        undo: true,
        redo: true,
        trash: true,
        save: false,
        navigation: true,
        alignleft: true,
        aligncenter: true,
        alignright: true,
        subfield: true,
        preview: true
      }
    };
  },
  methods: {
    async init() {
      this.visible = true;
      await this.loadLabels();
      await this.loadExistingRules();
    },

    // 加载所有标签
    async loadLabels() {
      try {
        // 获取所有标签（实体标签和属性标签）
        const response = await this.$http({
          url: this.$http.adornUrl("/label/entity/getAllLabelsWithAttributes"),
          method: "get",
          params: this.$http.adornParams({
            projectId: this.projectId
          })
        });

        const allLabelsData = response.data.data || [];

        // 提取所有标签名称，去重
        const labelNames = new Set();
        allLabelsData.forEach((label) => {
          labelNames.add(label.name);
        });

        this.allLabels = Array.from(labelNames);

        this.updateLabelStatus();
      } catch (error) {
        this.$message.error("加载标签失败");
        console.error(error);
      }
    },

    // 加载现有规则
    async loadExistingRules() {
      try {
        const response = await this.$http({
          url: this.$http.adornUrl("/label/entity/getLabelRules"),
          method: "get",
          params: this.$http.adornParams({
            projectId: this.projectId
          })
        });

        if (response.data.code === 0 && response.data.data) {
          this.markdownContent =
            response.data.data.content || this.getDefaultTemplate();
        } else {
          this.markdownContent = this.getDefaultTemplate();
        }
        this.updateLabelStatus();
      } catch (error) {
        this.markdownContent = this.getDefaultTemplate();
        this.updateLabelStatus();
      }
    },

    // 获取默认模板
    getDefaultTemplate() {
      if (this.allLabels.length === 0) {
        return `# 标注规范

## 总则
请在此处填写标注的总体规则和要求。

`;
      }

      const labelTemplates = this.allLabels
        .map((label) => {
          return `## ${label}
### 规则1
- 正例:
    - 示例一
    - 示例二
- 反例:
    - 示例一
    - 示例二

### 规则2
- 正例:
    - 示例一
    - 示例二
- 反例:
    - 示例一
    - 示例二

`;
        })
        .join("");

      return `# 标注规范

## 总则
请在此处填写标注的总体规则和要求。

${labelTemplates}`;
    },

    // Markdown 内容变化处理
    handleMarkdownChange(value, render) {
      this.updateLabelStatus();
    },

    // 更新标签状态
    updateLabelStatus() {
      const configuredSet = new Set();

      // 解析 markdown 内容，提取已配置的标签
      const lines = this.markdownContent.split("\n");
      for (const line of lines) {
        const match = line.match(/^##\s+(.+)$/);
        if (match) {
          const labelName = match[1].trim();
          if (this.allLabels.includes(labelName)) {
            configuredSet.add(labelName);
          }
        }
      }

      this.configuredLabels = Array.from(configuredSet);
      this.unconfiguredLabels = this.allLabels.filter(
        (label) => !configuredSet.has(label)
      );
    },

    // 验证 Markdown 格式
    validateMarkdown() {
      const lines = this.markdownContent.split("\n");
      const errors = [];

      let hasTitle = false;
      let hasGeneralRules = false;
      let currentLabel = null;
      let currentRule = null;
      let currentSection = null; // 'positive' 或 'negative'
      let labelRules = {}; // 存储每个标签的规则信息

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();

        // 检查标题
        if (line.match(/^#\s+标注规范$/)) {
          hasTitle = true;
          continue;
        }

        // 检查总则
        if (line.match(/^##\s+总则$/)) {
          hasGeneralRules = true;
          currentLabel = null;
          currentRule = null;
          currentSection = null;
          continue;
        }

        // 检查标签标题
        const labelMatch = line.match(/^##\s+(.+)$/);
        if (labelMatch) {
          const labelName = labelMatch[1].trim();
          if (labelName !== "总则") {
            if (!this.allLabels.includes(labelName)) {
              errors.push({
                message: `标签 "${labelName}" 不存在于当前项目中`
              });
            }
            currentLabel = labelName;
            currentRule = null;
            currentSection = null;
            if (!labelRules[labelName]) {
              labelRules[labelName] = [];
            }
          }
          continue;
        }

        // 检查规则标题
        const ruleMatch = line.match(/^###\s+(.+)$/);
        if (ruleMatch) {
          if (!currentLabel) {
            errors.push({
              message: "规则必须在标签下定义"
            });
          } else {
            const ruleName = ruleMatch[1].trim();
            currentRule = {
              name: ruleName,
              hasPositive: false,
              hasNegative: false,
              positiveExamples: [],
              negativeExamples: []
            };
            labelRules[currentLabel].push(currentRule);
            currentSection = null;
          }
          continue;
        }

        // 检查正例标识
        if (line.match(/^-\s*正例\s*:?\s*$/)) {
          if (!currentRule) {
            errors.push({
              message: "正例必须在规则下定义"
            });
          } else {
            currentRule.hasPositive = true;
            currentSection = "positive";
          }
          continue;
        }

        // 检查反例标识
        if (line.match(/^-\s*反例\s*:?\s*$/)) {
          if (!currentRule) {
            errors.push({
              message: "反例必须在规则下定义"
            });
          } else {
            currentRule.hasNegative = true;
            currentSection = "negative";
          }
          continue;
        }

        // 检查示例
        const exampleMatch = line.match(/^\s*-\s+(.+)$/);
        if (exampleMatch && currentRule && currentSection) {
          const example = exampleMatch[1].trim();
          if (example && example !== "示例一" && example !== "示例二") {
            if (currentSection === "positive") {
              currentRule.positiveExamples.push(example);
            } else if (currentSection === "negative") {
              currentRule.negativeExamples.push(example);
            }
          }
        }
      }

      // 基本格式检查
      if (!hasTitle) {
        errors.push({
          message: "缺少主标题：# 标注规范"
        });
      }

      if (!hasGeneralRules) {
        errors.push({
          message: "缺少：## 总则  部分"
        });
      }

      // 检查已配置的标签规则
      for (const label of Object.keys(labelRules)) {
        if (labelRules[label] && labelRules[label].length > 0) {
          // 检查每个规则是否完整
          for (const rule of labelRules[label]) {
            // 检查规则名称是否是详细内容（不能是"规则1"、"规则2"这样的占位符）
            if (rule.name.match(/^规则\d+$/)) {
              errors.push({
                message: `标签 "${label}" 的规则名称 "${rule.name}" 不够详细，请使用具体的规则描述`,
                label: label,
                rule: rule.name
              });
              continue;
            }

            if (!rule.hasPositive && !rule.hasNegative) {
              errors.push({
                message: `标签 "${label}" 的规则 "${rule.name}" 缺少正例或反例`,
                label: label,
                rule: rule.name
              });
              continue;
            }

            if (rule.hasPositive && rule.positiveExamples.length === 0) {
              errors.push({
                message: `标签 "${label}" 的规则 "${rule.name}" 的正例缺少具体示例`,
                label: label,
                rule: rule.name
              });
            }

            if (rule.hasNegative && rule.negativeExamples.length === 0) {
              errors.push({
                message: `标签 "${label}" 的规则 "${rule.name}" 的反例缺少具体示例`,
                label: label,
                rule: rule.name
              });
            }
          }
        }
      }

      return errors;
    },

    // 保存规则
    async handleSave() {
      // 验证格式
      const errors = this.validateMarkdown();
      if (errors.length > 0) {
        this.showValidationErrors(errors);
        return;
      }

      this.saving = true;
      try {
        await this.$http({
          url: this.$http.adornUrl("/label/entity/saveLabelRules"),
          method: "post",
          data: this.$http.adornData({
            projectId: this.projectId,
            content: this.markdownContent
          })
        });

        this.$message.success("保存成功");
        this.handleClose();
      } catch (error) {
        this.$message.error("保存失败");
        console.error(error);
      } finally {
        this.saving = false;
      }
    },

    // 显示验证错误
    showValidationErrors(errors) {
      // 创建表格样式的错误信息
      let errorHtml = `
        <div style="max-height: 500px; overflow-y: auto;">
          <table style="width: 100%; border-collapse: collapse; font-size: 13px;">
            <thead>
              <tr style="background-color: #f5f5f5;">
                <th style="border: 1px solid #ddd; padding: 10px; text-align: left; width: 120px;">标签</th>
                <th style="border: 1px solid #ddd; padding: 10px; text-align: left; width: 120px;">规则</th>
                <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">错误信息</th>
              </tr>
            </thead>
            <tbody>
      `;

      errors.forEach((error, index) => {
        const labelText = error.label || "-";
        const ruleText = error.rule || "-";

        errorHtml += `
          <tr style="${index % 2 === 0 ? "background-color: #fafafa;" : ""}">
            <td style="border: 1px solid #ddd; padding: 8px; word-break: break-word;">${labelText}</td>
            <td style="border: 1px solid #ddd; padding: 8px; word-break: break-word;">${ruleText}</td>
            <td style="border: 1px solid #ddd; padding: 8px; word-break: break-word; line-height: 1.4;">
              ${error.message}
            </td>
          </tr>
        `;
      });

      errorHtml += `
            </tbody>
          </table>
        </div>
      `;

      this.$alert(errorHtml, "标签规则验证失败", {
        dangerouslyUseHTMLString: true,
        type: "error",
        customClass: "validation-error-dialog"
      });
    },

    // 关闭对话框
    handleClose() {
      this.visible = false;
      this.markdownContent = "";
      this.allLabels = [];
      this.configuredLabels = [];
      this.unconfiguredLabels = [];
    }
  }
};
</script>

<style>
.validation-error-dialog {
  width: 1000px !important;
}
</style>
<style scoped>
.label-rules-container {
  display: flex;
  gap: 20px;
}

.editor-container {
  flex: 2;
}

.label-status-container {
  flex: 1;
  min-width: 300px;
}

.status-content {
  max-height: 450px;
  overflow-y: auto;
}

.status-section {
  margin-bottom: 20px;
}

.status-section h4 {
  margin: 0 0 10px 0;
  color: #606266;
  font-size: 14px;
}

.label-tag {
  margin: 2px 4px 2px 0;
}

.empty-text {
  color: #909399;
  font-size: 12px;
}

.dialog-footer {
  text-align: right;
}

/* 验证错误对话框样式 */
.validation-error-dialog .el-message-box__content {
  padding: 10px 20px !important;
}

.validation-error-dialog .el-message-box__message {
  margin: 0 !important;
}

.validation-error-dialog table {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;
}

.validation-error-dialog th {
  font-weight: 600;
  color: #303133;
}

.validation-error-dialog td {
  color: #606266;
}
</style>
