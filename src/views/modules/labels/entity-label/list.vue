<template>
  <!--查询栏-->
  <div class="mod-config" ref="labelRef">
    <div class="d-flex d-space-between">
      <el-form
        :inline="true"
        :model="dataForm"
        @keyup.enter.native="filterData()"
      >
        <el-form-item label="名称">
          <el-input
            v-model="searchName"
            placeholder="名称"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            class="status-width"
            v-model="searchStatus"
            placeholder="状态"
            clearable
          >
            <el-option label="启用" :value="1"></el-option>
            <el-option label="禁用" :value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button icon="el-icon-search" @click="filterData()"
            >查询
          </el-button>
          <el-button
            icon="el-icon-plus"
            v-if="editable"
            type="primary"
            @click="addOrUpdateHandle()"
            >新增
          </el-button>
        </el-form-item>
      </el-form>
      <div v-if="editable">
        <el-button
          type="primary"
          size="small"
          @click="configLabelRules"
          icon="el-icon-setting"
          >配置标签规则
        </el-button>
        <el-button
          type="primary"
          size="small"
          @click="bulkUpload"
          icon="el-icon-upload2"
          >导入
        </el-button>
        <el-button
          type="primary"
          size="small"
          @click="handleExport"
          icon="el-icon-download"
          >导出
        </el-button>
      </div>
    </div>

    <!--列表-->
    <el-table
      :data="tableData"
      border
      :row-style="{ height: '30px' }"
      style="width: 100%"
      @selection-change="selectionChangeHandle"
    >
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50"
      >
      </el-table-column>
      <el-table-column
        sortable
        label="ID"
        prop="id"
        width="100"
        header-align="center"
        align="center"
      >
      </el-table-column>
      <el-table-column
        sortable
        label="标签名"
        prop="name"
        width="200"
        header-align="center"
        align="center"
      >
      </el-table-column>
      <el-table-column
        label="标签描述"
        prop="description"
        :show-overflow-tooltip="true"
      ></el-table-column>
      <el-table-column
        sortable
        label="消歧"
        prop="disambiguate"
        align="center"
        width="100"
      >
        <template v-if="project_id !== 0" slot-scope="scope">
          <el-tag v-if="scope.row.disambiguate === true" size="small"
            >是
          </el-tag>
          <el-tag
            v-if="scope.row.disambiguate === false"
            size="small"
            type="info"
            >否
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        sortable
        label="颜色"
        prop="color"
        align="center"
        width="150"
      >
        <template v-slot="scope">
          <div
            :style="[
              { borderRadius: 10 + 'px' },
              { color: '#FFF' },
              { backgroundColor: scope.row.color + '!important' }
            ]"
          >
            {{ scope.row.color }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="orderNum"
        header-align="center"
        align="center"
        width="80"
        label="排序"
      >
      </el-table-column>
      <el-table-column label="状态" prop="status" align="center" width="100">
        <template v-if="project_id !== 0" slot-scope="scope">
          <el-tag v-if="scope.row.status === 1" size="small">启用</el-tag>
          <el-tag v-if="scope.row.status === 0" size="small" type="danger"
            >禁用
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="150">
        <template slot-scope="scope">
          <el-tooltip content="属性管理" placement="top" effect="light">
            <i class="pointer" @click="addAttributeHandle(scope.row.id)">
              <icon-svg
                :name="'add'"
                class="icon-inner icon-primary"
              ></icon-svg>
            </i>
          </el-tooltip>
          <el-tooltip content="标签使用情况" placement="top" effect="light">
            <i
              class="el-icon-view icon-primary icon-inner ml-1"
              @click="toEntityList(scope.row.id)"
            ></i>
          </el-tooltip>
          <el-tooltip
            v-if="editable"
            content="编辑"
            placement="top"
            effect="light"
          >
            <i
              class="el-icon-edit icon-primary"
              @click="addOrUpdateHandle(scope.row.id)"
            ></i>
          </el-tooltip>
          <el-tooltip
            v-if="editable"
            content="删除"
            placement="top"
            effect="light"
          >
            <i
              class="el-icon-delete icon-red"
              @click="deleteHandle(scope.row.id)"
            ></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <div class="mt-2" v-show="tableData.length > 0">
      <el-button
        class="p-8"
        v-if="editable"
        type="success"
        @click="submitChange(1)"
        size="small"
        icon="el-icon-check"
        :disabled="dataListSelections.length <= 0"
        >批量启用
      </el-button>
      <el-button
        class="p-8"
        v-if="editable"
        type="warning"
        size="small"
        @click="submitChange(0)"
        icon="el-icon-close"
        :disabled="dataListSelections.length <= 0"
        >批量禁用
      </el-button>
      <el-button
        class="p-8"
        v-if="editable"
        type="danger"
        size="small"
        @click="deleteHandle()"
        icon="el-icon-delete"
        :disabled="dataListSelections.length <= 0"
        >批量删除
      </el-button>
    </div>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    ></add-or-update>
    <add-attribute v-if="addAttributeVisible" ref="addAttribute">
    </add-attribute>
    <upload-entity-label
      v-if="loadFileVisible"
      ref="uploadFile"
      @refreshDataList="getDataList"
    ></upload-entity-label>
    <label-rules-config
      v-if="labelRulesVisible"
      ref="labelRulesConfig"
      :project-id="project_id"
    ></label-rules-config>
  </div>
</template>

<script>
import AddOrUpdate from "./add-or-update";
import AddAttribute from "./add-attribute";
import UploadEntityLabel from "./upload-entity-label";
import LabelRulesConfig from "./label-rules-config";
import * as XLSX from "xlsx";

export default {
  dataForm: {
    name: "",
    color: "",
    status: "",
    description: "",
    projectId: ""
  },
  name: "entity-label",
  data() {
    return {
      expands: undefined,
      tableH: undefined,
      dataForm: {
        name: "",
        color: "",
        status: "",
        description: "",
        projectId: ""
      },
      colors: [
        "#89C5F6",
        "#8DFFFF",
        "#8FF2CE",
        "#A4D8C2",
        "#C2EB7C",
        "#DAABEF",
        "#ED8C8C",
        "#F2D643",
        "#F5E8C8",
        "#FE8463"
      ],
      searchStatus: null,
      searchName: "",
      tableData: [],
      userData: [],
      pageIndex: 1,
      pageSize: 10000,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      loadFileVisible: false,
      addOrUpdateVisible: false,
      addAttributeVisible: false,
      labelRulesVisible: false,
      flag: false,
      dataListSelectedIds: []
    };
  },
  props: ["project_id"],
  components: {
    AddOrUpdate,
    AddAttribute,
    UploadEntityLabel,
    LabelRulesConfig
  },
  mounted() {
    this.getDataList();
  },
  computed: {
    editable: function () {
      return this.isAuth("project:editable");
    },
    usedColors: function () {
      if (this.userData.length > 0) {
        return this.$_.uniq(this.userData.map((item) => item.color));
      } else {
        return [];
      }
    },
    selectedIds: function () {
      return this.dataListSelections.map((item) => {
        return item.id;
      });
    }
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.$http({
        url: this.$http.adornUrl(`/label/entity/list`),
        method: "get",
        params: this.$http.adornParams(
          {
            projectId: this.project_id
          },
          false
        )
      }).then((data) => {
        if (data.data.code === 0 && data.data.data) {
          this.tableData = data.data.data;
          this.userData = data.data.data;
        } else {
          this.tableData = [];
          this.userData = [];
        }
        this.filterData();
      });
    },
    filterData() {
      this.tableData = this.userData;
      if (this.searchName !== "") {
        this.tableData = this.userData.filter(
          (item) =>
            item.name
              .toUpperCase()
              .indexOf(this.searchName?.trim().toUpperCase()) !== -1
        );
      }
      if (this.searchStatus !== null && this.searchStatus !== "") {
        this.tableData = this.userData.filter(
          (item) => item.status === this.searchStatus
        );
      }
    },
    // 查看属性使用情况
    toEntityList(id) {
      this.$router.push({
        path: "/project/private-entity/entity-list",
        query: { projectId: this.project_id, labelId: id }
      });
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val;
    },
    // 导出
    handleExport() {
      this.$confirm(`确定进行导出全部实体标签?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.$http({
            url: this.$http.adornUrl(
              `/label/entity/exportByProjectId/${this.project_id}`
            ),
            method: "get"
          })
            .then(({ data }) => {
              if (data && data.code === 0) {
                let downloadList = data.data;
                if (!data.data || downloadList.length <= 0) {
                  this.$message.info("没有数据导出");
                } else {
                  const headers = [
                    "标签名",
                    "标签说明",
                    "消歧",
                    "颜色",
                    "属性"
                  ];
                  const fields = [
                    "name",
                    "description",
                    "disambiguate",
                    "color",
                    "attr"
                  ];
                  const wb = XLSX.utils.book_new();
                  // 生成 Worksheet 对象
                  const ws = XLSX.utils.json_to_sheet(downloadList, {
                    header: fields
                  });
                  XLSX.utils.sheet_add_aoa(ws, [headers], { origin: "A1" });
                  // 添加 Worksheet 到 Workbook
                  XLSX.utils.book_append_sheet(wb, ws, "Sheet1");
                  // 将 Workbook 导出为 Excel 文件
                  XLSX.writeFile(wb, `${this.project_id}-实体标签.xlsx`);
                }
              }
            })
            .catch((err) => {
              this.$message.error(err);
            });
        })
        .catch(() => {});
    },
    // 标签新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true;
      this.$nextTick(() => {
        let diffArr = this.$_.difference(this.colors, this.usedColors);
        if (diffArr.length > 0) {
          this.$refs.addOrUpdate.init(id, this.project_id, diffArr[0]);
        } else {
          this.$refs.addOrUpdate.init(id, this.project_id);
        }
      });
    },
    //属性新增
    addAttributeHandle(id) {
      this.addAttributeVisible = this;
      this.$nextTick(() => {
        this.$refs.addAttribute.init(id);
      });
    },
    // 删除
    deleteHandle(id) {
      const ids = this.selectedIds;
      this.$confirm(`确定进行【${id ? "删除" : "批量删除"}】操作?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.$http({
            url: this.$http.adornUrl("/label/entity/delete"),
            method: "post",
            data: this.$http.adornData(id ? [id] : ids, false)
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: "操作成功",
                type: "success",
                duration: 3000
              });
            } else {
              this.$message.error(data.msg);
            }
            this.getDataList();
            this.dataListLoading = false;
          });
        })
        .catch(() => {});
    },
    // 批量上传
    bulkUpload() {
      this.loadFileVisible = true;
      this.$nextTick(() => {
        this.$refs.uploadFile.init(this.project_id);
      });
    },
    // 配置标签规则
    configLabelRules() {
      this.labelRulesVisible = true;
      this.$nextTick(() => {
        this.$refs.labelRulesConfig.init();
      });
    },

    // 批量改变状态
    submitChange(id) {
      const ids = this.selectedIds;
      this.$confirm(
        `确定进行【${id === 1 ? "批量启用" : "批量禁用"}】操作?`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      )
        .then(() => {
          this.dataListLoading = true;
          this.$http({
            url: this.$http.adornUrl(`/label/entity/status/${id}`),
            method: "post",
            data: this.$http.adornData(ids, false)
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: "操作成功",
                type: "success",
                duration: 3000
              });
            } else {
              this.$message.error(data.msg);
            }
            this.getDataList();
            this.dataListLoading = false;
          });
        })
        .catch(() => {});
    }
  }
};
</script>
<style lang="scss">
.el-table__expanded-cell {
  padding: 0 !important;
}

.el-table .tableRowClassName {
  background: #ecf5ff;
}

.tableBox {
  th {
    padding: 0 !important;
    height: 27px;
    line-height: 27px;
  }

  td {
    padding: 0 !important;
    height: 27px;
    line-height: 27px;
  }
}

.tagBox {
  margin: 0px 5px 10px 0px;
}

.spanBox {
  margin: 0px 5px 10px 0px;
}
</style>
