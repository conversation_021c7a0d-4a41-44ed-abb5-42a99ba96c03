<template>
  <div>
    <el-row>
      <el-col :span="4">
        <div class="back-head">
          <a class="go-back" href="javascript:0;" @click="$router.go(-1)">
            <i class="el-icon-arrow-left"></i>返回
          </a>
          实体标注列表
        </div>
      </el-col>
    </el-row>
    <div class="mt-20 info">
      <div style="width: 287px">
        <span class="label-name">标签名:</span>
        <span class="label-content">{{ entityLabel.name }}</span>
      </div>
      <div class="info-item" style="width: 273px">
        <span class="label-name">标签颜色:</span>
        <span class="label-content"
          ><span
            :style="[
              { borderRadius: 12 + 'px' },
              { color: '#FFF' },
              { padding: 4 + 'px' },
              { fontSize: 12 + 'px' },
              { backgroundColor: entityLabel.color + '!important' }
            ]"
          >
            {{ entityLabel.color }}
          </span></span
        >
      </div>
      <div>
        <span class="label-name">标签描述:</span>
        <span class="label-content">{{ entityLabel.description }}</span>
      </div>
    </div>
    <el-form
      :inline="true"
      :model="dataForm"
      @keyup.enter.native="getDataList()"
    >
      <el-form-item label="文书编号" prop="articleId">
        <el-input
          class="article-number"
          v-model="dataForm.articleId"
          placeholder="文书编号"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="标注人" prop="userId">
        <el-select filterable v-model="dataForm.userId" clearable>
          <div v-for="(value, key) in userList" :key="'list1' + key">
            <el-option :label="value" :value="key"></el-option>
          </div>
        </el-select>
      </el-form-item>
      <el-form-item label="标注时间" prop="date">
        <el-date-picker
          v-model="dataForm.date"
          end-placeholder="结束时间"
          range-separator="-"
          start-placeholder="开始时间"
          type="daterange"
          value-format="yyyy-MM-dd"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button
          icon="el-icon-search"
          @click="
            () => {
              this.pageIndex = 1;
              getDataList();
            }
          "
          >查询
        </el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      style="width: 100%"
      :header-row-style="{ height: '44px' }"
      :header-cell-style="{
        background: 'rgb(246 247 249)',
        padding: '0px'
      }"
      @sort-change="sortTable"
    >
      <el-table-column
        width="250"
        min-width="150"
        prop="articleId"
        sortable
        header-align="center"
        align="center"
        label="文书编号"
        :show-overflow-tooltip="true"
      >
        <template v-slot="scope">
          <div
            style="color: #3474fe; text-decoration: none; cursor: pointer"
            @click="toTaskDetail(scope.row.batchId, scope.row.noteId)"
          >
            {{ scope.row.articleId }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="content"
        header-align="center"
        align="center"
        label="标注文本"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
      <el-table-column
        width="130"
        prop="userId"
        header-align="center"
        align="center"
        :show-overflow-tooltip="true"
        label="标注人"
      >
        <template v-slot="scope">
          <div>{{ userMap.get(scope.row.userId + "") }}</div>
        </template>
      </el-table-column>
      <el-table-column
        width="200"
        prop="createTime"
        header-align="center"
        sortable
        align="center"
        label="标注时间"
      >
      </el-table-column>

      <!--<el-table-column
        prop="attrContent"
        header-align="center"
        :sortable="false"
        align="center"
        label="属性详情"
        :show-overflow-tooltip="true"
      >
      </el-table-column>-->

      <el-table-column
        header-align="center"
        align="center"
        width="150"
        label="属性数量"
      >
        <template v-slot="scope">
          <!--<el-tooltip
            v-if="scope.row.attrCount > 0"
            content="点击查看属性详情"
            placement="top"
            effect="light"
          >
            <div
              @click="showAttrList(scope.row.id)"
              style="color: #3474fe; text-decoration: none; cursor: pointer"
            >
              {{ scope.row.attrCount }}
            </div>
          </el-tooltip>-->

          <!--hover-->
          <el-popover
            v-if="scope.row.attrCount > 0"
            placement="left"
            :width="popoverWith"
            trigger="hover"
          >
            <div v-loading="!!attrTipLoadings[scope.$index]">
              <div v-html="attrTipContents[scope.$index]"></div>
            </div>

            <span
              @mouseenter="showAttrListTip(scope.row.id, scope.$index)"
              @click="showAttrList(scope.row.id)"
              slot="reference"
              style="color: #3474fe; text-decoration: none; cursor: pointer"
            >
              {{ scope.row.attrCount }}</span
            >
          </el-popover>

          <div v-else>
            {{ scope.row.attrCount }}
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    >
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <attr-list v-if="attrListVisible" ref="attrList"></attr-list>
  </div>
</template>

<script>
import AttrList from "@/views/modules/labels/entity-label/attr-list";
import { Loading } from "element-ui";
import { FromTypeEnum, setPageIds } from "@/utils/pageIdUtil";
import { trimStr } from "@/utils";

export default {
  name: "entity-list",
  data() {
    return {
      fromType: FromTypeEnum.entity_list,
      userList: [],
      dataForm: {
        articleId: null,
        userId: null,
        date: []
      },
      entityLabel: {},
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      orderBy: "createTime",
      isAsc: false,
      dataListLoading: false,
      dataListSelections: [],
      attrListVisible: false,
      allTipLoading: [],
      allContents: [],
      popoverWith: 320
    };
  },
  components: { AttrList },
  computed: {
    projectId: function () {
      return this.$route.query.projectId;
    },
    labelId: function () {
      return this.$route.query.labelId;
    },
    userMap: function () {
      const tempMap = new Map();
      for (const k of Object.keys(this.userList)) {
        tempMap.set(k, this.userList[k]);
      }
      return tempMap;
    },
    attrTipContents: function () {
      return [...this.allContents];
    },
    attrTipLoadings: function () {
      return this.allTipLoading;
    }
  },
  mounted() {
    this.initUserAndLabel();
    this.getDataList();
  },
  methods: {
    initUserAndLabel() {
      const loadingInstance = Loading.service();
      this.dataListLoading = true;
      return this.$http({
        url: this.$http.adornUrl("/entity/userAndLabelInfo"),
        method: "get",
        params: this.$http.adornParams({
          labelId: this.labelId
        })
      })
        .then(({ data }) => {
          if (data && data.code === 0) {
            if (data.userList) {
              this.userList = data.userList;
            } else {
              this.userList = [];
            }
            if (data.label) {
              this.entityLabel = data.label;
            } else {
              this.entityLabel = {};
            }
          }

          this.dataListLoading = false;
          loadingInstance.close();
        })
        .catch((err) => {
          this.dataListLoading = false;
          loadingInstance.close();
          this.$message.error(err);
        });
    },
    initAttrTipContents(init) {
      let contents = [];
      let allTipLoading = [];
      if (this.dataList) {
        let length = this.dataList.length;
        for (let i = 0; i < length; i++) {
          let str = trimStr(this.dataList[i].attrTipContent);
          if (str) {
            contents.push(str);
          } else {
            contents.push(
              `<div data-is-empty="1" style="min-width: 120px;"> &nbsp;</div>`
            );
          }

          if (init) {
            this.dataList[i].loadAttrTipFlag = true;
            allTipLoading.push(true);
          } else {
            allTipLoading.push(!!this.dataList[i].loadAttrTipFlag);
          }
        }
      }
      this.allContents = contents;
      this.allTipLoading = allTipLoading;
    },
    // 获取数据列表
    getDataList() {
      const loadingInstance = Loading.service();
      this.dataListLoading = true;
      this.$http({
        url: this.$http.adornUrl("/entity/list"),
        method: "get",
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          orderBy: this.orderBy,
          isAsc: this.isAsc,
          projectId: this.projectId,
          labelId: this.labelId,
          articleId: this.dataForm.articleId,
          userId: this.dataForm.userId,
          startDate:
            (this.dataForm?.date != null && this.dataForm?.date[0]) ||
            undefined,
          endDate:
            (this.dataForm?.date != null && this.dataForm?.date[1]) || undefined
        })
      })
        .then(({ data }) => {
          let pageIdsData = null;
          if (data && data.code === 0) {
            let pageData = data.page;
            pageIdsData = pageData.pageIdsVOList;

            this.dataList = pageData.listData.content || [];
            this.totalPage = pageData.listData.totalElements;
          } else {
            this.dataList = [];
            this.totalPage = 0;
          }

          this.initAttrTipContents(true);

          // setPageIds(this.fromType, pageIdsData);
          // 此页面打开的标注页不要上/下页翻页操作
          setPageIds(this.fromType, null);

          this.dataListLoading = false;
          loadingInstance.close();

          this.loadAttrInfo();
        })
        .catch((err) => {
          setPageIds(this.fromType, null);
          this.dataListLoading = false;
          loadingInstance.close();
          this.$message.error(err);
        });
    },
    loadAttrInfo() {
      let dataList = this.dataList;
      if (dataList && dataList.length > 0) {
        for (let i = 0; i < dataList.length; i++) {
          let item = dataList[i];
          this.showAttrListTip(item.id, i);
        }
      }
    },
    // 显示属性弹窗
    showAttrListTip(entityId, index) {
      let row = this.dataList[index];
      if (!row) {
        return;
      }
      if (
        row.attrTipContent &&
        !row.attrTipContent.includes(`data-is-empty="1"`)
      ) {
        return;
      }

      this.allTipLoading[index] = true;
      row.loadAttrTipFlag = true;

      this.$nextTick(() => {
        this.$http({
          url: this.$http.adornUrl(`/attr/findAttrListByEntityId`),
          method: "get",
          params: this.$http.adornParams({
            entityId: entityId
          })
        })
          .then(({ data }) => {
            row.loadAttrTipFlag = false;

            let attrLabelMapData = null;
            if (data && data.code === 0) {
              const attrList = data.data.list;
              attrLabelMapData = data.data.attrLabelMap;
              row.attrTipContent = this.initAttrTipContent(
                attrList,
                attrLabelMapData
              );
            }
            this.initAttrTipContents();

            row.attrCount = attrLabelMapData
              ? Object.keys(attrLabelMapData).length
              : 0;
          })
          .catch(() => {
            row.loadAttrTipFlag = false;
            this.initAttrTipContents();
          });
      });
    },
    initAttrTipContent(attrList, attrLabelMapData) {
      const idContentMap = new Map();
      attrList.forEach((item) => {
        let attrLabelId = item.attrLabelId + "";
        let contents = idContentMap.get(attrLabelId);
        if (!contents) {
          contents = [];
        }
        contents.push(item.content);
        idContentMap.set(attrLabelId, contents);
      });

      let rows = [];
      for (let key in attrLabelMapData) {
        let value = attrLabelMapData[key];
        if (value) {
          let contents = idContentMap.get(key) || [];
          let txt = `${value}: ${contents.join("； ")}`;
          rows.push(
            `<span style="max-width: ${
              this.popoverWith - 20
            }px;display: inline-block;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;" title="${txt}">${txt}</span>`
          );
        }
      }

      return `${rows.join("<br>")}`;
    },
    showAttrList(id) {
      this.attrListVisible = true;
      this.$nextTick(() => {
        this.$refs.attrList.init(id);
      });
    },
    // 排序
    sortTable(sort) {
      this.orderBy = sort.prop;
      this.isAsc = sort.order === "ascending";
      this.getDataList();
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val;
      this.pageIndex = 1;
      this.getDataList();
    },
    toTaskDetail(batchId, noteId) {
      let routeUrl = this.$router.resolve({
        path: "/annotask/detail",
        query: {
          batchId: Number(batchId),
          noteId: Number(noteId),
          fromType: this.fromType
        }
      });

      window.open(routeUrl.href, "_blank");
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val;
      this.getDataList();
    }
  }
};
</script>
<style lang="scss" scoped>
.article-number {
  ::v-deep.el-input__inner {
    width: 209px;
  }
}

.el-select {
  ::v-deep.el-input__inner {
    width: 209px;
  }
}

.info {
  display: flex;

  > div {
    display: flex;
    align-items: center;
  }
}

.label-name {
  color: #606266;
  font-weight: 600;
}
</style>
