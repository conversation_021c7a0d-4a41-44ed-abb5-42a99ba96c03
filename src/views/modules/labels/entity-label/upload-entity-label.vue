<template>
  <el-dialog title="提示" :visible.sync="dialogVisible" width="20%">
    <el-upload
      ref="upload"
      :http-request="upload"
      :limit="1"
      :multiple="false"
      :show-file-list="true"
      accept="application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      action=""
      class="upload-content"
      style="text-align: center"
    >
      <el-button style="width: 200px" type="primary">点击上传</el-button>
      <div slot="tip" class="el-upload__tip">
        <p>只能上传 <strong>Excel</strong> 文件，且不超过10M</p>
      </div>
    </el-upload>

    <div style="text-align: center">
      <a
        href="javascript:void(0);"
        @click.prevent="downloadTemplate"
        type="primary"
        ><i class="el-icon-download"></i>下载模板</a
      >
    </div>
  </el-dialog>
</template>

<script>
import { Loading } from "element-ui";

export default {
  name: "upload-entity-label",
  data() {
    return {
      projectId: 0,
      dialogVisible: false
    };
  },
  watch: {
    dialogVisible: function () {
      if (!this.dialogVisible) {
        this.$refs.upload.clearFiles();
      }
    }
  },
  methods: {
    init(projectId) {
      this.dialogVisible = true;
      this.projectId = projectId;
      if (!this.projectId) {
        this.dialogVisible = false;
      }
    },
    downloadTemplate() {
      this.$http({
        url: this.$http.adornUrl("/file/entityLabelTemplate"),
        method: "get",
        responseType: "blob"
      })
        .then((resp) => {
          if (resp.headers["content-type"] !== "application/octet-stream") {
            const err = "模板下载失败";
            throw err;
          }
          const url = window.URL.createObjectURL(new Blob([resp.data]));
          const link = document.createElement("a");
          const filename = decodeURI(resp.headers.filename);
          link.href = url;
          link.download = filename;
          link.click();
        })
        .catch((err) => {
          this.$message.error(err);
        });
    },
    upload(params) {
      this.$refs.upload.clearFiles();
      const file = params.file;
      if (
        (!file.name.endsWith(".xlsx") && !file.name.endsWith(".xls")) ||
        file.size / 1024 / 1024 > 10
      ) {
        this.$message.error({
          dangerouslyUseHTMLString: true,
          message: "只能上传 <strong>Excel</strong> 文件，且不超过10M"
        });
        params.onError();
        return;
      }
      const loadingInstance = Loading.service();

      const formData = new FormData();
      formData.append("file", file);
      formData.append("projectId", this.projectId);
      this.$http({
        url: this.$http.adornUrl("/label/entity/importData"),
        method: "post",
        timeout: 0,
        headers: { "Content-Type": "multipart/form-data;" },
        data: formData
      }).then(
        ({ data }) => {
          if (data.data && Object.keys(data.data).length > 0) {
            let htmlArr = Object.keys(data.data).map((key) => {
              return `<div><strong>第${key}行错误：</strong>${data.data[
                key
              ].join("；")}</div>`;
            });
            this.$alert(`${htmlArr.join("")}`, "文件内容错误", {
              dangerouslyUseHTMLString: true
            });
          } else {
            this.dialogVisible = false;
          }
          loadingInstance.close();
          if (data.code === 0) {
            this.$emit("refreshDataList");
          } else {
            this.$message.error(data.msg);
          }
          params.onSuccess();
        },
        () => {
          this.dialogVisible = false;
          console.error("访问后端连接失败");
        }
      );
    }
  }
};
</script>

<style scoped></style>
