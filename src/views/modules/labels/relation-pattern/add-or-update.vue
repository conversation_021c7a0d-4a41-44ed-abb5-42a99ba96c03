<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="80px"
    >
      <el-form-item label="名称" prop="name">
        <el-input v-model="dataForm.name" placeholder="关系模板名称"></el-input>
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input v-model="dataForm.description" placeholder="描述"></el-input>
      </el-form-item>
      <el-form-item label="模板图片">
        <el-upload
          ref="upload"
          :http-request="uploadImage"
          :limit="1"
          :multiple="false"
          :show-file-list="true"
          accept="image/gif,image/jpeg,image/png"
          action=""
          class="upload-content"
        >
          <el-button size="mini" type="primary">点击上传</el-button>
        </el-upload>
        <img width="300" v-if="dataForm.image" :src="dataForm.image" />
      </el-form-item>
      <el-form-item label="序号" prop="orderNum">
        <el-input-number
          :max="10"
          :min="1"
          v-model="dataForm.orderNum"
          placeholder="排序字段"
        ></el-input-number>
      </el-form-item>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { Loading } from "element-ui";

export default {
  data() {
    const validateExist = (rule, value, callback) => {
      if (value.trim() !== this.beforeName.trim()) {
        this.$http({
          url: this.$http.adornUrl("/labels/relation/pattern/existByName"),
          method: "get",
          params: this.$http.adornParams({
            name: this.dataForm.name,
            projectId: this.dataForm.projectId
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            if (data.data) {
              callback(new Error("该名称已存在"));
            } else {
              callback();
            }
          }
        });
      } else {
        callback();
      }
    };
    return {
      visible: false,
      dataForm: {
        id: 0,
        name: "",
        description: "",
        schema: "",
        status: "",
        image: "",
        orderNum: "",
        projectId: "",
        createTime: "",
        deleted: ""
      },
      beforeName: "",
      dataRule: {
        name: [
          { required: true, message: "关系模板名称不能为空", trigger: "blur" },
          { validator: validateExist, trigger: "blur" }
        ]
      }
    };
  },
  methods: {
    init(id, projectId) {
      this.dataForm.id = id || 0;
      this.dataForm.projectId = projectId;
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.dataForm.resetFields();
        this.dataForm.image = undefined;
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/labels/relation/pattern/info/${this.dataForm.id}`
            ),
            method: "get",
            params: this.$http.adornParams()
          }).then(({ data }) => {
            if (data && data.code === 0 && data.data) {
              this.dataForm.name = data.data.name;
              this.dataForm.description = data.data.description;
              this.dataForm.status = data.data.status;
              this.dataForm.image = data.data.image;
              this.dataForm.orderNum = data.data.orderNum;
              this.beforeName = data.data.name;
            }
          });
        }
      });
    },
    // 上传模板图片
    uploadImage(params) {
      this.$refs.upload.clearFiles();
      const file = params.file;
      if (
        (!file.name.endsWith(".gif") &&
          !file.name.endsWith(".pjp") &&
          !file.name.endsWith(".jpg") &&
          !file.name.endsWith(".pjpeg") &&
          !file.name.endsWith(".jpeg") &&
          !file.name.endsWith(".png") &&
          !file.name.endsWith(".jfif")) ||
        file.size / 1024 > 100
      ) {
        this.$message.error({
          dangerouslyUseHTMLString: true,
          message: "只能上传 <strong>图片</strong> ，且不超过100KB"
        });
        params.onError();
        return;
      }
      const loadingInstance = Loading.service();
      const formData = new FormData();
      formData.append("file", file);
      formData.append("id", this.dataForm.id);
      this.$http({
        url: this.$http.adornUrl("/labels/relation/pattern/saveImage"),
        method: "post",
        timeout: 0,
        headers: { "Content-Type": "multipart/form-data;" },
        data: formData
      }).then(({ data }) => {
        loadingInstance.close();
        this.dialogVisible = false;
        if (data.code === 0) {
          this.dataForm.image = data.data;
          this.$message.success("上传成功");
        } else {
          this.$message.error(data.msg);
        }
        this.$emit("refreshDataList");
        params.onSuccess();
      });
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(
              `/labels/relation/pattern/${
                !this.dataForm.id ? "save" : "update"
              }`
            ),
            method: "post",
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              name: this.dataForm.name,
              description: this.dataForm.description,
              schema: this.dataForm.schema,
              status: this.dataForm.status,
              image: this.dataForm.image,
              orderNum: this.dataForm.orderNum,
              projectId: this.dataForm.projectId,
              createTime: this.dataForm.createTime,
              deleted: this.dataForm.deleted
            })
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: "操作成功",
                type: "success",
                duration: 1500,
                onClose: () => {
                  this.visible = false;
                  this.$emit("refreshDataList");
                }
              });
            } else {
              this.$message.error(data.msg);
            }
          });
        }
      });
    }
  }
};
</script>
