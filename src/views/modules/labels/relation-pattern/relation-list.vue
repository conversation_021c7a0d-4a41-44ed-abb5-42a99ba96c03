<template>
  <div>
    <el-row>
      <el-col :span="4">
        <div class="back-head">
          <a class="go-back" href="javascript:0;" @click="$router.go(-1)"
            ><i class="el-icon-arrow-left"></i>返回</a
          >
          关系标注列表
        </div>
      </el-col>
    </el-row>
    <div class="mt-20 info">
      <div style="width: 287px">
        <span class="label-name">模板名:</span>
        <span class="label-content">{{ pattern.name }}</span>
      </div>
      <div style="width: 273px">
        <span class="label-name">关系模板图:</span>
        <span class="label-content"
          ><el-popover placement="bottom" width="300" trigger="click">
            <p
              align="center"
              v-if="pattern.image === null || pattern.image === undefined"
            >
              暂无数据
            </p>
            <img v-else width="300" :src="pattern.image" />
            <el-button type="info" plain size="mini" slot="reference"
              >Pattern</el-button
            >
          </el-popover></span
        >
      </div>
      <div>
        <span class="label-name">模板描述:</span>
        <span class="label-content">{{ pattern.description }}</span>
      </div>
    </div>
    <el-form
      :inline="true"
      :model="dataForm"
      @keyup.enter.native="getDataList()"
    >
      <el-form-item label="文书编号" prop="articleId">
        <el-input
          class="article-number"
          v-model="dataForm.articleId"
          placeholder="文书编号"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="标注人" prop="userId">
        <el-select filterable v-model="dataForm.userId" clearable>
          <div v-for="(value, key) in userList" :key="'list1' + key">
            <el-option :label="value" :value="key"></el-option>
          </div>
        </el-select>
      </el-form-item>
      <el-form-item label="标注时间" prop="date">
        <el-date-picker
          v-model="dataForm.date"
          end-placeholder="结束时间"
          range-separator="-"
          start-placeholder="开始时间"
          type="daterange"
          value-format="yyyy-MM-dd"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button
          icon="el-icon-search"
          @click="
            () => {
              this.pageIndex = 1;
              getDataList();
            }
          "
          >查询
        </el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      border
      v-loading="dataListLoading"
      style="width: 100%"
      @sort-change="sortTable"
      :span-method="objectSpanMethod"
    >
      <el-table-column
        prop="articleId"
        width="250"
        min-width="150"
        header-align="center"
        align="center"
        label="文书编号"
      >
        <template v-slot="scope">
          <div
            style="color: #3474fe; text-decoration: none; cursor: pointer"
            @click="toTaskDetail(scope.row.batchId, scope.row.noteId)"
          >
            {{ scope.row.articleId }}
          </div>
        </template>
      </el-table-column>

      <el-table-column
        prop="order"
        width="100"
        header-align="center"
        align="center"
        label="三元组序号"
      ></el-table-column>
      <el-table-column
        prop="subject"
        header-align="center"
        align="center"
        label="主语"
        :show-overflow-tooltip="true"
      >
        <template v-slot="scope">
          <el-tag v-if="scope.row.subjectIsForeign" size="small"
            >{{ scope.row.subject | parserWJ }}
          </el-tag>
          <div v-else>
            {{ scope.row.subject | parseLang }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="relation"
        width="150"
        header-align="center"
        align="center"
        label="关系"
      ></el-table-column>
      <el-table-column
        prop="objects"
        header-align="center"
        align="center"
        label="宾语"
        :show-overflow-tooltip="true"
      >
        <template v-slot="scope">
          <el-tag v-if="scope.row.objectsIsForeign" size="small"
            >{{ scope.row.objects | parserWJ }}
          </el-tag>
          <div v-else>
            {{ scope.row.objects | parseLang }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        width="100"
        prop="negation"
        header-align="center"
        align="center"
        label="否定"
      >
        <template v-slot="scope">
          {{ scope.row.negation ? "是" : "" }}
        </template>
      </el-table-column>
      <el-table-column
        width="250"
        prop="annotations"
        header-align="center"
        align="center"
        label="关系批注"
      >
        <template v-slot="scope">
          {{ scope.row.annotations ? scope.row.annotations.join(",") : "" }}
        </template>
      </el-table-column>
      <el-table-column
        prop="userId"
        width="130"
        header-align="center"
        align="center"
        label="标注人"
        :show-overflow-tooltip="true"
      >
        <template v-slot="scope">
          <div>{{ userMap.get(scope.row.userId) }}</div>
        </template>
      </el-table-column>
      <el-table-column
        width="200"
        prop="createTime"
        header-align="center"
        align="center"
        label="标注时间"
      ></el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    >
    </el-pagination>
  </div>
</template>

<script>
import { Loading } from "element-ui";
import { FromTypeEnum, setPageIds } from "@/utils/pageIdUtil";

export default {
  name: "relation-list",
  data() {
    return {
      fromType: FromTypeEnum.relation_list,
      userList: [],
      dataForm: {
        articleId: null,
        userId: null,
        date: []
      },
      pattern: {},
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      orderBy: "createTime",
      isAsc: false,
      dataListLoading: false,
      dataListSelections: [],
      attrListVisible: false,
      spanIndex: []
    };
  },
  filters: {
    parseLang: function (val) {
      let str = "";
      val.forEach((it) => {
        str += `[ ${it} ]`;
      });
      return str;
    },
    parserWJ: function (val) {
      if (!isNaN(val[0])) {
        return `引用：${val[0]}`;
      }
      let split = val[0].split("#");
      if (val[0].indexOf("#") !== -1) {
        if (split[1] === "subject") {
          return `引用：${split[0]}的主语`;
        } else if (split[1] === "objects") {
          return `引用：${split[0]}的宾语`;
        }
      }
    }
  },
  computed: {
    projectId: function () {
      return this.$route.query.projectId;
    },
    labelId: function () {
      return this.$route.query.labelId;
    },
    userMap: function () {
      const tempMap = new Map();
      for (const k of Object.keys(this.userList)) {
        tempMap.set(Number.parseInt(k), this.userList[k]);
      }
      return tempMap;
    },
    tableData: function () {
      let arr = [];
      if (this.dataList.length > 0) {
        for (const v of this.dataList) {
          for (let it of v.items) {
            let obj = {};
            obj["articleId"] = v.articleId;
            obj["batchId"] = v.batchId;
            obj["noteId"] = v.noteId;
            obj["createTime"] = v.createTime;
            obj["userId"] = v.userId;
            obj["order"] = it.order;
            obj["subject"] = it.subject;
            obj["negation"] = it.negation;
            obj["annotations"] = it.annotations;
            obj["relation"] = it.relation;
            obj["objects"] = it.objects;
            obj["subjectIsForeign"] = it.subjectIsForeign;
            obj["objectsIsForeign"] = it.objectsIsForeign;
            arr.push(obj);
          }
        }
      }
      return arr;
    },
    getDistinctIndexes: function () {
      const result = [0];
      if (this.dataList.length > 0) {
        for (let i = 0; i < this.dataList.length; i++) {
          result.push(
            this.dataList[i].items.length + result[result.length - 1]
          );
        }
      }
      return result;
    }
  },
  mounted() {
    this.getDataList();
  },
  methods: {
    // 获取数据列表
    getDataList() {
      const loadingInstance = Loading.service();
      this.dataListLoading = true;
      this.$http({
        url: this.$http.adornUrl("/relation/list"),
        method: "get",
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          orderBy: this.orderBy,
          isAsc: this.isAsc,
          projectId: this.projectId,
          labelId: this.labelId,
          articleId: this.dataForm.articleId,
          userId: this.dataForm.userId,
          startDate:
            (this.dataForm?.date != null && this.dataForm?.date[0]) ||
            undefined,
          endDate:
            (this.dataForm?.date != null && this.dataForm?.date[1]) || undefined
        })
      })
        .then(({ data }) => {
          let pageIdsData = null;
          if (data && data.code === 0) {
            let result = data.page;
            pageIdsData = result.pageIdsVOList;

            this.dataList = result.listData.content || [];
            this.totalPage = result.listData.totalElements;
            if (data.userList) {
              this.userList = data.userList;
            } else {
              this.userList = [];
            }
            if (data.label) {
              this.pattern = data.label;
            } else {
              this.pattern = {};
            }
          } else {
            this.dataList = [];
            this.totalPage = 0;
          }

          setPageIds(this.fromType, pageIdsData);

          this.dataListLoading = false;
          loadingInstance.close();
        })
        .catch((err) => {
          setPageIds(this.fromType, null);
          this.dataListLoading = false;
          loadingInstance.close();
          this.$message.error(err);
        });
    },
    // 排序
    sortTable(sort) {
      this.orderBy = sort.prop;
      this.isAsc = sort.order === "ascending";
      this.getDataList();
    },
    toTaskDetail(batchId, noteId) {
      let routeUrl = this.$router.resolve({
        path: "/annotask/detail",
        query: {
          batchId: Number(batchId),
          noteId: Number(noteId),
          fromType: this.fromType
        }
      });
      window.open(routeUrl.href, "_blank");
    },
    // 查看属性使用情况
    toRelationList(id) {
      this.$router.push({
        path: "/project/private-pattern/relation-list",
        query: { projectId: this.project_id, labelId: id }
      });
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val;
      this.pageIndex = 1;
      this.getDataList();
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val;
      this.getDataList();
    },
    isForeign(val) {
      let str = val.join("");
      return str.indexOf("#") !== -1 || !isNaN(str);
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0 || columnIndex === 8 || columnIndex === 7) {
        let index = this.getDistinctIndexes.indexOf(rowIndex);
        if (index !== -1) {
          return {
            rowspan:
              this.getDistinctIndexes[index + 1] -
              this.getDistinctIndexes[index],
            colspan: 1
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0
          };
        }
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.article-number {
  ::v-deep.el-input__inner {
    width: 209px;
  }
}
.el-select {
  ::v-deep.el-input__inner {
    width: 209px;
  }
}
.info {
  display: flex;
  > div {
    display: flex;
    align-items: center;
  }
}
.label-name {
  color: #606266;
  font-weight: 600;
}
</style>
