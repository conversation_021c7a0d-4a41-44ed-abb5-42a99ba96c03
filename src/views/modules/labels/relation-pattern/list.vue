<template>
  <div class="mod-config">
    <el-form
      :inline="true"
      :model="dataForm"
      @keyup.enter.native="getDataList()"
    >
      <el-form-item label="名称">
        <el-input
          class="input-width"
          v-model="dataForm.name"
          placeholder="名称"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="描述">
        <el-input
          class="input-width"
          v-model="dataForm.description"
          placeholder="名称"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item v-if="project_id !== 0" label="状态">
        <el-select
          class="status-width"
          v-model="dataForm.status"
          placeholder="状态"
          clearable
        >
          <el-option label="启用" value="1"> </el-option>
          <el-option label="禁用" value="0"> </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          icon="el-icon-search"
          @click="
            () => {
              this.pageIndex = 1;
              getDataList();
            }
          "
          >查询</el-button
        >
        <el-button
          icon="el-icon-plus"
          v-if="editable"
          type="primary"
          @click="addOrUpdateHandle()"
          >新增</el-button
        >
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%"
    >
      <el-table-column
        prop="id"
        header-align="center"
        align="center"
        width="50"
        label="ID"
      >
      </el-table-column>
      <el-table-column
        prop="name"
        header-align="center"
        align="center"
        label="名称"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
      <el-table-column
        prop="description"
        header-align="center"
        align="center"
        label="描述"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
      <el-table-column
        prop="image"
        header-align="center"
        align="center"
        width="100"
        label="关系模板图"
      >
        <template slot-scope="scope">
          <el-popover placement="left" width="300" trigger="click">
            <p
              align="center"
              v-if="scope.row.image === null || scope.row.image === undefined"
            >
              暂无数据
            </p>
            <img v-else width="300" :src="scope.row.image" />
            <el-button type="info" plain size="mini" slot="reference"
              >Pattern</el-button
            >
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column
        v-if="project_id !== 0"
        prop="status"
        header-align="center"
        align="center"
        width="100"
        label="状态"
      >
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            active-color="#13ce66"
            inactive-color="#ff4949"
            :active-value="1"
            :inactive-value="0"
            :disabled="!editable"
            @change="changeStatus(scope.row)"
          >
          </el-switch>
        </template>
      </el-table-column>
      <el-table-column
        prop="orderNum"
        header-align="center"
        align="center"
        width="80"
        label="序号"
      >
      </el-table-column>
      <el-table-column
        prop="createTime"
        header-align="center"
        align="center"
        width="160"
        label="创建时间"
      >
        <template slot-scope="scope">
          <div>{{ scope.row.createTime | dateFrm }}</div>
        </template>
      </el-table-column>
      <el-table-column
        header-align="center"
        align="center"
        width="150"
        label="操作"
      >
        <template slot-scope="scope">
          <el-tooltip content="编辑模板" placement="top" effect="light">
            <i
              class="el-icon-tickets icon-primary"
              @click="schemaEditor(scope.row.id)"
            ></i>
          </el-tooltip>
          <el-tooltip content="标签使用情况" placement="top" effect="light">
            <i
              class="el-icon-view icon-primary icon-inner ml-1"
              @click="toRelationList(scope.row.id)"
            ></i>
          </el-tooltip>
          <el-tooltip
            v-if="editable"
            content="编辑"
            placement="top"
            effect="light"
          >
            <i
              class="el-icon-edit icon-primary"
              @click="addOrUpdateHandle(scope.row.id)"
            ></i>
          </el-tooltip>
          <el-tooltip
            v-if="editable"
            content="删除"
            placement="top"
            effect="light"
          >
            <i
              class="el-icon-delete icon-red"
              @click="deleteHandle(scope.row.id)"
            ></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    >
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    ></add-or-update>
  </div>
</template>

<script>
import AddOrUpdate from "./add-or-update";
import moment from "moment";

export default {
  name: "relation-pattern",
  data() {
    return {
      dataForm: {
        name: "",
        description: "",
        status: ""
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      visible: false
    };
  },
  filters: {
    dateFrm: function (value) {
      return moment(value).format("YYYY-MM-DD HH:mm:ss");
    }
  },
  components: {
    AddOrUpdate
  },
  computed: {
    editable: function () {
      return this.isAuth("project:editable");
    }
  },
  props: ["project_id"],
  mounted() {
    this.getDataList();
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataForm.projectId = this.project_id;
      this.$http({
        url: this.$http.adornUrl("/labels/relation/pattern/list"),
        method: "get",
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          name: this.dataForm.name,
          description: this.dataForm.description,
          status: this.dataForm.status,
          projectId: this.dataForm.projectId
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list;
          this.totalPage = data.page.totalCount;
        } else {
          this.dataList = [];
          this.totalPage = 0;
        }
        this.dataListLoading = false;
      });
    },
    changeStatus(row) {
      this.$http({
        url: this.$http.adornUrl("/labels/relation/pattern/update"),
        method: "post",
        data: this.$http.adornData({
          id: row.id,
          status: row.status
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.$message({
            message: "操作成功",
            type: "success",
            duration: 1500
          });
          this.visible = false;
          this.$emit("refreshDataList");
        } else {
          this.$message.error(data.msg);
          this.getDataList();
        }
      });
    },
    schemaEditor(id) {
      this.$router.push({
        path: "/labels/relation/schema",
        query: { id: id, projectId: this.project_id }
      });
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val;
      this.pageIndex = 1;
      this.getDataList();
    },
    // 查看属性使用情况
    toRelationList(id) {
      this.$router.push({
        path: "/project/private-pattern/relation-list",
        query: { projectId: this.project_id, labelId: id }
      });
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val;
      this.getDataList();
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val;
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true;
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id, this.project_id);
      });
    },
    // 删除
    deleteHandle(id) {
      const ids = id
        ? [id]
        : this.dataListSelections.map((item) => {
            return item.id;
          });
      this.$confirm(`确定进行[${id ? "删除" : "批量删除"}]操作?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl("/labels/relation/pattern/delete"),
          method: "post",
          data: this.$http.adornData(ids, false)
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: "操作成功",
              type: "success",
              duration: 1500,
              onClose: () => {
                this.getDataList();
              }
            });
          } else {
            this.$message.error(data.msg);
          }
        });
      });
    }
  }
};
</script>
