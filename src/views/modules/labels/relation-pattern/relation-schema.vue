<template>
  <div>
    <div class="back-head">
      <a class="go-back" href="javascript:0;" @click="goBack">
        <i class="el-icon-arrow-left"></i>返回
      </a>
      模板列表
    </div>
    <el-row :gutter="4">
      <el-col :span="8">
        <div>
          <vue-json-editor
            v-model="pattern"
            :style="{ height: fullHeight + 'px' }"
            :mode="'code'"
            :show-btns="true"
            lang="zh"
            @json-change="onJsonChange"
            @json-save="onJsonSave"
            @has-error="onError"
          ></vue-json-editor>
        </div>
      </el-col>
      <el-col :span="16">
        <el-card v-if="flag" :style="{ height: fullHeight + 'px' }">
          <div v-for="(it, index) in pattern" :key="'rs-pat' + index">
            <el-row
              :gutter="4"
              align="center"
              class="relation-card-input"
              type="flex"
              justify="start"
            >
              <el-col :lg="1" :span="2">
                <span v-if="it.order" class="radio-number"
                  ><span>{{ it.order }}</span></span
                >
                <span v-if="it.required" class="required">*</span>
              </el-col>
              <!--主语-->
              <el-col :span="6">
                <div>
                  <el-input
                    v-if="it && it.subject && it.subject.foreign"
                    :placeholder="holder(it.subject.foreign)"
                    disabled
                    readonly
                  ></el-input>
                  <el-select
                    v-model="templateData[index].subject"
                    v-else-if="it && it.subject && it.subject.multiple"
                    size="medium"
                    class="multiple-choice"
                    multiple
                    collapse-tags
                    default-first-option
                    :placeholder="it.subject.name"
                  >
                    <el-option
                      v-for="item in templateData[index].subOptions"
                      :key="item.id"
                      :label="item.text"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>
                  <el-input
                    v-else-if="it && it.subject && !it.subject.multiple"
                    v-model="templateData[index].subOptions[0].text"
                    :placeholder="it.subject.name"
                    readonly
                  >
                  </el-input>
                </div>
              </el-col>
              <!--关系-->
              <el-col :span="4">
                <el-select
                  v-if="templateData[index]"
                  v-model="templateData[index].relation"
                  clearable
                  filterable
                  placeholder="关系"
                >
                  <el-option
                    v-for="item in relationOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
              </el-col>
              <!--宾语-->
              <el-col :span="6">
                <div>
                  <el-input
                    v-if="it && it.objects && it.objects.foreign"
                    :placeholder="holder(it.objects.foreign)"
                    disabled
                    readonly
                  ></el-input>
                  <el-select
                    v-model="templateData[index].objects"
                    v-else-if="it.objects && it.objects.multiple"
                    size="medium"
                    class="multiple-choice"
                    multiple
                    collapse-tags
                    default-first-option
                    :placeholder="it.objects.name"
                  >
                    <el-option
                      v-for="item in templateData[index].objOptions"
                      :key="item.id"
                      :label="item.text"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>
                  <el-input
                    v-else-if="it && it.objects && !it.objects.multiple"
                    class="single-choice"
                    v-model="templateData[index].objOptions[0].text"
                    :placeholder="it.objects.name"
                    readonly
                  >
                  </el-input>
                </div>
              </el-col>
              <el-col class="center" :span="2">
                <div>
                  <el-checkbox label="否定"></el-checkbox>
                </div>
              </el-col>
              <el-col :span="4">
                <el-select
                  v-model="annotationValue"
                  multiple
                  filterable
                  default-first-option
                  placeholder="批注信息"
                >
                  <el-option
                    v-for="item in it.annotationOptions"
                    :key="item"
                    :label="item"
                    :value="item"
                  >
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import vueJsonEditor from "vue-json-tool";

export default {
  data() {
    return {
      fullHeight: document.documentElement.clientHeight - 200,
      templateData: [],
      annotationOptions: ["无", "轻", "中", "重"],
      annotationValue: null,
      relationOptions: [
        {
          id: 1,
          name: "example1"
        },
        {
          id: 2,
          name: "example2"
        },
        {
          id: 3,
          name: "example3"
        }
      ],
      tempData: "",
      pattern: [],
      flag: false
    };
  },
  components: {
    vueJsonEditor
  },
  mounted() {
    window.onresize = () => {
      return (() => {
        this.fullHeight = document.documentElement.clientHeight - 200;
      })();
    };
    // 获取模板数据
    this.init(this.id);
  },
  computed: {
    id: function () {
      return this.$route.query.id;
    },
    projectId: function () {
      return this.$route.query.projectId;
    }
  },
  /* watch: {
      pattern: {
        handler(old, now) {
          if (now || now === []) {
            return
          }
          this.generateDefaultData(now)
        },
        deep: true
      }
    }, */
  methods: {
    init(id) {
      this.$http({
        url: this.$http.adornUrl(`/labels/relation/pattern/info/${id}`),
        method: "get",
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 0 && data.data) {
          this.pattern = JSON.parse(data.data.schemaData);
          this.generateDefaultData(this.pattern);
        }
      });
    },
    holder(value) {
      if ((value + "").indexOf("#") !== -1) {
        const split = value.split("#");
        let name;
        if (split[1] === "subject") {
          name = "(" + split[0] + ")" + this.pattern[split[0] - 1].subject.name;
        }
        if (split[1] === "objects") {
          name = "(" + split[0] + ")" + this.pattern[split[0] - 1].objects.name;
        }
        return name;
      }
      return "(" + value + ")";
    },
    onJsonChange(value) {
      this.generateDefaultData(value);
    },
    onJsonSave(value) {
      if (!this.isAuth("project:editable")) {
        this.$message.warning("当前无权限编辑");
        return;
      }
      this.$confirm("确定保存并退出?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.$http({
            url: this.$http.adornUrl("/labels/relation/pattern/update"),
            method: "post",
            data: this.$http.adornData(
              {
                id: this.id,
                schemaData: JSON.stringify(this.pattern),
                projectId: this.projectId
              },
              false
            )
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: "操作成功",
                type: "success",
                duration: 1500
              });
              this.$router.go(-1);
            }
          });
        })
        .catch(() => {});
    },
    onError(value) {},
    goBack() {
      this.$confirm("确认取消编辑结果并返回?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.$router.go(-1);
        })
        .catch(() => {});
    },
    // 根据schema生成对应提交的模板数据
    generateDefaultData(pattern) {
      this.flag = false;
      this.templateData = [];
      const example = {
        id: "",
        subject: [],
        subOptions: [{ id: "", text: "" }],
        relation: "",
        objects: [],
        objOptions: [{ id: "", text: "" }],
        group: "",
        order: ""
      };
      pattern.sort(function (a, b) {
        return a.order - b.order;
      });
      for (let i = 0; i < pattern.length; i++) {
        // 将第一个空的模板数据放入
        this.templateData.push(example);
      }
      this.flag = true;
    }
  }
};
</script>

<style lang="scss">
@import "../../annotation/components/relation/comm/pattern";

.jsoneditor-vue {
  height: 100%;
}

div.jsoneditor {
  border: 1px solid #3474fe;
}

div.jsoneditor-menu {
  background-color: #3474fe;
}

button.json-save-btn {
  background-color: #13ce66 !important;
}

.jsoneditor-poweredBy {
  display: none;
}

.center {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
