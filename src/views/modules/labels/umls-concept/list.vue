<template>
  <div class="mod-config">
    <div class="d-flex d-space-between">
      <el-form
        :inline="true"
        :model="dataForm"
        @keyup.enter.native="getDataList()"
      >
        <el-form-item>
          <el-input
            class="input-width"
            v-model="dataForm.conceptId"
            placeholder="Concept Id"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-input
            class="input-width"
            v-model="dataForm.conceptName"
            placeholder="Concept Name"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-input
            class="input-width"
            v-model="dataForm.preferredName"
            placeholder="Preferred Name"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item v-show="project_id !== 0">
          <el-select
            class="status-width"
            v-model="dataForm.status"
            placeholder="状态"
            clearable
          >
            <el-option label="启用" value="1"> </el-option>
            <el-option label="禁用" value="0"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            icon="el-icon-search"
            @click="
              () => {
                this.pageIndex = 1;
                getDataList();
              }
            "
            >查询</el-button
          >
          <el-button
            icon="el-icon-plus"
            v-if="editable"
            type="primary"
            @click="addOrUpdateHandle()"
            >新增</el-button
          >
        </el-form-item>
      </el-form>
      <div v-if="editable">
        <el-button
          type="primary"
          size="small"
          @click="bulkUpload"
          icon="el-icon-upload2"
          >导入
        </el-button>
        <el-button
          type="primary"
          size="small"
          @click="handleExport"
          icon="el-icon-download"
          >导出
        </el-button>
      </div>
    </div>
    <el-table
      :height="tableH"
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%"
    >
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50"
      >
      </el-table-column>
      <el-table-column
        prop="conceptId"
        header-align="center"
        align="center"
        show-overflow-tooltip
        label="Concept Id"
      >
      </el-table-column>
      <el-table-column
        prop="conceptName"
        header-align="center"
        align="center"
        show-overflow-tooltip
        label="Concept Name"
      >
      </el-table-column>
      <el-table-column
        prop="preferredName"
        header-align="center"
        align="center"
        show-overflow-tooltip
        label="Preferred Name"
      >
      </el-table-column>
      <el-table-column
        prop="semanticTypes"
        header-align="center"
        align="center"
        show-overflow-tooltip
        label="Semantic Types"
      >
      </el-table-column>
      <el-table-column
        v-if="project_id !== 0"
        prop="creater"
        header-align="center"
        align="center"
        label="创建者"
      >
        <template slot-scope="scope">
          <div>{{ userMap.get(scope.row.creater + "") }}</div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="project_id !== 0"
        prop="createTime"
        header-align="center"
        align="center"
        width="180"
        label="创建时间"
      >
      </el-table-column>
      <el-table-column
        v-if="project_id !== 0"
        width="80"
        prop="status"
        header-align="center"
        align="center"
        label="状态"
      >
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            active-color="#13ce66"
            inactive-color="#ff4949"
            :active-value="1"
            :inactive-value="0"
            :disabled="!editable"
            @change="changeStatus(scope.row)"
          >
          </el-switch>
        </template>
      </el-table-column>
      <el-table-column
        v-if="editable"
        header-align="center"
        align="center"
        width="100"
        label="操作"
      >
        <template slot-scope="scope">
          <el-tooltip content="编辑" placement="top" effect="light">
            <i
              class="el-icon-edit icon-primary"
              @click="addOrUpdateHandle(scope.row.id)"
            ></i>
          </el-tooltip>
          <el-tooltip content="删除" placement="top" effect="light">
            <i
              class="el-icon-delete icon-red"
              @click="deleteHandle(scope.row.id)"
            ></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <div class="d-flex align-items-center mt-1" v-show="dataList.length > 0">
      <div>
        <el-button
          v-if="editable"
          type="success"
          @click="submitChange(1)"
          size="small"
          icon="el-icon-check"
          :disabled="dataListSelections.length <= 0"
          >批量启用
        </el-button>
        <el-button
          v-if="editable"
          type="warning"
          size="small"
          @click="submitChange(0)"
          icon="el-icon-close"
          :disabled="dataListSelections.length <= 0"
          >批量禁用
        </el-button>
        <el-button
          v-if="editable"
          type="danger"
          size="small"
          @click="deleteHandle()"
          icon="el-icon-delete"
          :disabled="dataListSelections.length <= 0"
          >批量删除
        </el-button>
      </div>
      <div class="ml-auto pb-1">
        <el-pagination
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
          :current-page="pageIndex"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper"
        >
        </el-pagination>
      </div>
    </div>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    ></add-or-update>

    <upload-umls
      v-if="loadFileVisible"
      ref="loadFile"
      @refreshDataList="getDataList"
    ></upload-umls>
  </div>
</template>

<script>
import AddOrUpdate from "./add-or-update";
import UploadUmls from "./upload-umls";
import * as XLSX from "xlsx";

export default {
  data() {
    return {
      dataForm: {
        conceptId: "",
        conceptName: "",
        preferredName: "",
        status: "",
        creater: ""
      },
      loadFileVisible: false,
      tableH: undefined,
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false
    };
  },
  props: ["project_id"],
  components: {
    AddOrUpdate,
    UploadUmls
  },
  mounted() {
    this.getDataList();
    this.tableH = document.documentElement.clientHeight - 290;
    window.onresize = () => {
      return (() => {
        this.tableH = document.documentElement.clientHeight - 290;
      })();
    };
  },
  computed: {
    userMap: function () {
      const tempMap = new Map();
      for (const k of Object.keys(this.userList)) {
        tempMap.set(k, this.userList[k]);
      }
      return tempMap;
    },
    editable: function () {
      return this.isAuth("project:editable");
    }
  },
  methods: {
    bulkUpload() {
      this.loadFileVisible = true;
      this.$nextTick(() => {
        this.$refs.loadFile.init(this.dataForm.projectId);
      });
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true;
      this.dataForm.projectId = this.project_id;
      this.$http({
        url: this.$http.adornUrl("/labels/umlsconcept/list"),
        method: "get",
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          projectId: this.dataForm.projectId,
          conceptId: this.dataForm.conceptId,
          conceptName: this.dataForm.conceptName,
          preferredName: this.dataForm.preferredName,
          status: this.dataForm.status
        })
      })
        .then(({ data }) => {
          if (data && data.code === 0 && data.data.list) {
            this.dataList = data.data.list;
            this.totalPage = data.data.totalCount;
            if (data.userList) {
              this.userList = data.userList;
            } else {
              this.userList = [];
            }
          } else {
            this.dataList = [];
            this.totalPage = 0;
          }
          this.dataListLoading = false;
        })
        .catch((err) => {
          this.dataListLoading = false;
          this.$message.error(err);
        });
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val;
      this.pageIndex = 1;
      this.getDataList();
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val;
      this.getDataList();
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val;
    },
    // 导出
    handleExport() {
      this.$confirm(`确定导出全部数据?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.$http({
            url: this.$http.adornUrl(
              `/labels/umlsconcept/exportByProjectId/${this.project_id}`
            ),
            method: "get"
          })
            .then(({ data }) => {
              if (data && data.code === 0) {
                let downloadList = data.data;
                if (!data.data || downloadList.length <= 0) {
                  this.$message.info("没有数据导出");
                } else {
                  const headers = [
                    "Concept Id",
                    "Concept Name",
                    "Preferred Name",
                    "Semantic Types"
                  ];
                  const fields = [
                    "conceptId",
                    "conceptName",
                    "preferredName",
                    "semanticTypes"
                  ];
                  const wb = XLSX.utils.book_new();
                  // 生成 Worksheet 对象
                  const ws = XLSX.utils.json_to_sheet(downloadList, {
                    header: fields
                  });
                  XLSX.utils.sheet_add_aoa(ws, [headers], { origin: "A1" });
                  // 添加 Worksheet 到 Workbook
                  XLSX.utils.book_append_sheet(wb, ws, "Sheet1");
                  // 将 Workbook 导出为 Excel 文件
                  XLSX.writeFile(
                    wb,
                    `${this.project_id}-自定义UMLS Concept.xlsx`
                  );
                }
              }
            })
            .catch((err) => {
              this.$message.error(err);
            });
        })
        .catch(() => {});
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true;
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id, this.project_id);
      });
    },
    changeStatus(row) {
      const ids = [row.id];
      this.$http({
        url: this.$http.adornUrl(`/labels/umlsconcept/status/${row.status}`),
        method: "post",
        data: this.$http.adornData(ids, false)
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.$message({
            message: "操作成功",
            type: "success",
            duration: 3000
          });
        } else {
          const map = JSON.parse(data.msg);
          const msg = map.map((x) => x.conceptId);
          this.$message.error(msg + "已被使用,不能禁用!");
          this.getDataList();
        }
      });
    },
    // 删除
    deleteHandle(id) {
      const ids = id
        ? [id]
        : this.dataListSelections.map((item) => {
            return item.id;
          });
      this.$confirm(`确定进行[${id ? "删除" : "批量删除"}]操作?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl("/labels/umlsconcept/delete"),
          method: "post",
          data: this.$http.adornData(ids, false)
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: "操作成功",
              type: "success",
              duration: 1500,
              onClose: () => {
                this.getDataList();
              }
            });
          } else {
            const map = JSON.parse(data.msg);
            const msg = map.map((x) => x.conceptId);
            this.$message.error(msg + "已被使用,不能删除!");
            this.getDataList();
          }
        });
      });
    },
    submitChange(status) {
      const ids = this.dataListSelections.map((item) => {
        return item.id;
      });
      this.$confirm(
        `确定进行【${status === 1 ? "批量启用" : "批量禁用"}】操作?`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      )
        .then(() => {
          this.dataListLoading = true;
          this.$http({
            url: this.$http.adornUrl(`/labels/umlsconcept/status/${status}`),
            method: "post",
            data: this.$http.adornData(ids, false)
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: "操作成功",
                type: "success",
                duration: 3000
              });
            } else {
              const map = JSON.parse(data.msg);
              const msg = map.map((x) => x.conceptId);
              this.$message.error(msg + "已被使用,不能禁用!");
              this.getDataList();
            }
            this.dataListLoading = false;
            this.getDataList();
          });
        })
        .catch(() => {});
    }
  }
};
</script>
<style lang="scss">
.input-width {
  width: 160px;
}
</style>
