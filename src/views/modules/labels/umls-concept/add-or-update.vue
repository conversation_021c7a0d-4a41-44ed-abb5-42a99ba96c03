<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="150px"
    >
      <el-form-item label="Concept Id" prop="conceptId">
        <el-input
          placeholder="消歧数据的唯一ID"
          v-model="dataForm.conceptId"
          :disabled="dataForm.id !== 0"
        ></el-input>
      </el-form-item>
      <el-form-item label="Concept Name" prop="conceptName">
        <el-input
          v-model="dataForm.conceptName"
          placeholder="被消歧词的名称，如“bank” (意思：银行 或 河岸)"
        ></el-input>
      </el-form-item>
      <el-form-item label="Preferred Name" prop="preferredName">
        <el-input
          v-model="dataForm.preferredName"
          placeholder="消歧后选择的首选名称，如“银行”"
        ></el-input>
      </el-form-item>
      <el-form-item label="Semantic Types" prop="semanticTypes">
        <el-input
          v-model="dataForm.semanticTypes"
          placeholder="词语的语义类型，可以当个词语分类使用"
        ></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    const validateConceptId = (rule, value, callback) => {
      if (
        value.trim().toUpperCase() !== this.beforeConceptId.trim().toUpperCase()
      ) {
        this.$http({
          url: this.$http.adornUrl("/labels/umlsconcept/validateConceptId"),
          method: "get",
          params: this.$http.adornParams({
            conceptId: this.dataForm.conceptId,
            projectId: this.dataForm.projectId
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            if (data.data) {
              callback(new Error("该Concept Id已存在"));
            } else {
              callback();
            }
          }
        });
      } else {
        callback();
      }
    };
    return {
      visible: false,
      dataForm: {
        id: 0,
        conceptId: "",
        conceptName: "",
        semanticTypes: "",
        preferredName: "",
        status: 1,
        creater: "",
        createTime: "",
        projectId: "",
        deleted: ""
      },
      beforeConceptId: "",
      dataRule: {
        conceptId: [
          { required: true, message: "conceptId", trigger: "blur" },
          { validator: validateConceptId, trigger: "blur" }
        ],
        conceptName: [
          { required: true, message: "conceptName", trigger: "blur" }
        ],
        preferredName: [
          {
            required: true,
            message: "preferred_name不能为空",
            trigger: "blur"
          }
        ]
      }
    };
  },
  methods: {
    init(id, projectId) {
      this.dataForm.id = id || 0;
      this.dataForm.projectId = projectId;
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.dataForm.resetFields();
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/labels/umlsconcept/info/${this.dataForm.id}`
            ),
            method: "get",
            params: this.$http.adornParams()
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm.conceptId = data.data.conceptId;
              this.dataForm.conceptName = data.data.conceptName;
              this.dataForm.semanticTypes = data.data.semanticTypes;
              this.dataForm.preferredName = data.data.preferredName;
              this.dataForm.status = data.data.status;
              this.dataForm.creater = data.data.creater;
              this.dataForm.createTime = data.data.createTime;
              this.dataForm.projectId = data.data.projectId;
              this.dataForm.deleted = data.data.deleted;
              this.beforeConceptId = data.data.conceptId;
            }
          });
        }
      });
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(
              `/labels/umlsconcept/${!this.dataForm.id ? "save" : "update"}`
            ),
            method: "post",
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              conceptId: this.dataForm.conceptId,
              conceptName: this.dataForm.conceptName,
              semanticTypes: this.dataForm.semanticTypes,
              preferredName: this.dataForm.preferredName,
              status: this.dataForm.status,
              creater: this.dataForm.creater,
              createTime: this.dataForm.createTime,
              projectId: this.dataForm.projectId,
              deleted: this.dataForm.deleted
            })
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: "操作成功",
                type: "success",
                duration: 1500,
                onClose: () => {
                  this.visible = false;
                  this.$emit("refreshDataList");
                }
              });
            } else {
              this.$message.error(data.msg);
            }
          });
        }
      });
    }
  }
};
</script>
