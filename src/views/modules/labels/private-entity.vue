<template>
  <div>
    <div class="back-head">
      <a class="go-back" href="javascript:0;" @click="$router.go(-1)">
        <i class="el-icon-arrow-left"></i>返回
      </a>
      实体标签
    </div>
    <labels :project_id="project_id"></labels>
  </div>
</template>

<script>
import Labels from "./entity-label/list";

export default {
  data() {
    return {
      // 区别共有标签还是私有标签
      // project_id: 1
    };
  },
  components: {
    Labels
  },
  computed: {
    project_id: function () {
      return this.$route.query.projectId;
    }
  },
  mounted() {},
  methods: {}
};
</script>
