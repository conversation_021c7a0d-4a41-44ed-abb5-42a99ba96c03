<template>
  <div class="item">
    <div style="background-color: #f5f6f9; color: #3474fe">
      <i :class="icon"></i>
    </div>
    <div>
      <span>{{ number }}</span>
      <span>{{ content }}</span>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    content: {
      type: String,
      required: true
    },
    number: {
      type: Number,
      default: 0,
      required: false
    },
    color: {
      type: String,
      required: false
    },
    icon: {
      type: String,
      default: "el-icon-s-order",
      required: true
    }
  },
  name: "item-card",
  data() {
    return {};
  }
};
</script>

<style scoped lang="scss">
.mod-home {
  line-height: 1.5;
}
.home-top,
.chart-bar {
  display: flex;
  justify-content: space-between;
  .item {
    padding: 10px 20px;
    flex-grow: 1;
    flex-shrink: 1;
    flex-basis: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    border-radius: 8px;
    &:hover {
      transform: translateY(-0.3333333333rem);
      box-shadow: 0 0.2rem 1rem rgba(33, 40, 50, 0.1);
    }
    div:first-child {
      margin-right: 35px;
      width: 55px;
      height: 55px;
      text-align: center;
      line-height: 63px;
      background-color: #579aff;
      border-radius: 8px;
      .icon-svg,
      i {
        font-size: 1.5rem;
        //color: #fff;
      }
    }
    div:last-child {
      display: flex;
      flex-direction: column;
      text-align: right;
      span:first-child {
        font-size: 1.4rem;
        color: #182524;
        font-weight: 600;
      }
      span:last-child {
        color: #6b7373;
      }
    }
  }
  .item:not(:last-child),
  .el-card:not(:last-child) {
    margin-right: 10px;
  }
  .el-card {
    flex-grow: 1;
    flex-shrink: 1;
    flex-basis: 100%;
    ::v-deep .el-card__body {
      height: 100% !important;
    }
  }
}
</style>
