<template>
  <div class="chart-bar">
    <el-card style="margin-top: 15px">
      <div
        style="height: 50vh; width: 100%"
        id="batch-correct-rate-chart"
      ></div>
    </el-card>
    <el-card style="margin-top: 15px">
      <div style="height: 50vh; width: 100%" id="batch-time-chart"></div>
    </el-card>
  </div>
</template>

<script>
import * as echarts from "echarts";
import { processTime } from "@/utils/tools";

export default {
  name: "batchCorrectChart",
  data() {
    return {
      barMaxWidth: 60
    };
  },
  computed: {
    roleId() {
      return this.$store.state.user.roleId;
    }
  },
  methods: {
    init(projectId) {
      const myChartA = echarts.init(
        document.getElementById("batch-correct-rate-chart")
      );
      const myChartB = echarts.init(
        document.getElementById("batch-time-chart")
      );
      myChartA.showLoading();
      this.$http({
        url: this.$http.adornUrl(`/statistics/batchCorrectChart`),
        method: "get",
        params: this.$http.adornParams({
          projectId: projectId
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.drawChartA(myChartA, data.data || []);
          this.drawChartB(myChartB, data.data || []);
          myChartA.hideLoading();
        }
      });
    },
    drawChartA(myChart, data) {
      let option = {
        graphic: {
          type: "text", // 类型：文本
          left: "center",
          top: "middle",
          silent: true, // 不响应事件
          invisible: data.length > 0, // 有数据就隐藏
          style: {
            fill: "#9d9d9d",
            text: "暂无数据",
            fontSize: "16px"
          }
        },
        title: {
          text: "批次正确与打回率"
        },
        tooltip: {
          formatter: function (params) {
            var value = params.value.toFixed(2) + "%";
            return params.seriesName + ": " + value;
          }
        },
        legend: {
          x: "right"
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "10%",
          containLabel: true
        },
        xAxis: {
          type: "value",
          axisLine: {
            show: data.length > 0
          },
          labelLine: {
            show: data.length > 0
          }
        },
        yAxis: {
          type: "category",
          axisLine: {
            show: data.length > 0
          },
          labelLine: {
            show: data.length > 0
          },
          data: data.map((it) => it.batchName)
        },
        series: [
          {
            name: "正确率",
            type: "bar",
            barMaxWidth: this.barMaxWidth,
            data: data.map((it) => it.correctRate || 0),
            itemStyle: {},
            label: {
              show: true,
              formatter: function (params) {
                if (params.value > 0) {
                  return params.value.toFixed(2) + "%";
                } else {
                  return "";
                }
              }
            }
          },
          {
            name: "打回率",
            type: "bar",
            barMaxWidth: this.barMaxWidth,
            data: data.map((it) => it.repulseRate || 0),
            itemStyle: {},
            label: {
              show: true,
              formatter: function (params) {
                if (params.value > 0) {
                  return params.value.toFixed(2) + "%";
                } else {
                  return "";
                }
              }
            }
          }
        ]
      };
      myChart.setOption(option);
    },
    drawChartB(myChart, data) {
      let option = {
        title: {
          text: "批次平均标注与审核时长"
        },
        graphic: {
          type: "text", // 类型：文本
          left: "center",
          top: "middle",
          silent: true, // 不响应事件
          invisible: data.length > 0, // 有数据就隐藏
          style: {
            fill: "#9d9d9d",
            text: "暂无数据",
            fontSize: "16px"
          }
        },
        tooltip: {
          formatter: function (params) {
            return params.seriesName + "：" + processTime(params.value);
          }
        },
        legend: {
          x: "right"
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "10%",
          containLabel: true
        },
        xAxis: {
          type: "value",
          axisLine: {
            show: data.length > 0
          },
          labelLine: {
            show: data.length > 0
          }
        },
        yAxis: {
          type: "category",
          axisLine: {
            show: data.length > 0
          },
          labelLine: {
            show: data.length > 0
          },
          data: data.map((it) => it.batchName)
        },
        series: [
          {
            name: "平均标注时长",
            type: "bar",
            barMaxWidth: this.barMaxWidth,
            data: data.map((it) => it.annotator || 0),
            itemStyle: {},
            label: {
              show: true,
              formatter: function (params) {
                return processTime(params.value);
              }
            }
          },
          {
            name: "平均审核时长",
            type: "bar",
            barMaxWidth: this.barMaxWidth,
            data: data.map((it) => it.auditor || 0),
            itemStyle: {},
            label: {
              show: true,
              formatter: function (params) {
                return processTime(params.value);
              }
            }
          }
        ]
      };
      myChart.setOption(option);
    }
  },
  components: {}
};
</script>

<style scoped lang="scss">
.chart-bar {
  display: flex;
  justify-content: space-between;
  .item {
    flex-grow: 1;
    flex-shrink: 1;
    flex-basis: 0;
    padding: 10px 0;
    display: flex;
    align-items: center;
    justify-content: space-around;
    background-color: #fff;
    border-radius: 8px;
    &:hover {
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }
    div:first-child {
      margin-right: 35px;
      width: 55px;
      height: 55px;
      text-align: center;
      line-height: 63px;
      background-color: #579aff;
      border-radius: 8px;
      .icon-svg,
      i {
        font-size: 1.5rem;
        //color: #fff;
      }
    }
    div:last-child {
      display: flex;
      flex-direction: column;
      text-align: right;
      span:first-child {
        font-size: 1.4rem;
        color: #182524;
        font-weight: 600;
      }
      span:last-child {
        color: #6b7373;
      }
    }
  }
  .item:not(:last-child),
  .el-card:not(:last-child) {
    margin-right: 10px;
  }
  .el-card {
    flex-grow: 1;
    flex-shrink: 1;
    flex-basis: 100%;
    ::v-deep .el-card__body {
      height: 100% !important;
    }
  }
}
</style>
