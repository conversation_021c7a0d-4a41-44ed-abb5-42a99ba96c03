<template>
  <div class="home-top">
    <itemCard
      color="#3474FE"
      icon="el-icon-document"
      :number="overviewData.total"
      content="共有文献"
    ></itemCard>
    <template v-if="roleId === this.$RoleEnum.annotator">
      <itemCard
        color="#909399"
        icon="el-icon-s-order"
        :number="overviewData.unmarked"
        content="待标注"
      ></itemCard>
      <itemCard
        color="#409eff"
        icon="el-icon-edit-outline"
        :number="overviewData.noting"
        content="标注中"
      ></itemCard>
      <itemCard
        color="#e6a23c"
        icon="el-icon-s-claim"
        :number="overviewData.unReview"
        content="待审核"
      ></itemCard>
      <itemCard
        color="#f56c6c"
        icon="el-icon-document-delete"
        :number="overviewData.repulse"
        content="被打回"
      ></itemCard>
      <itemCard
        color="#91cc75"
        icon="el-icon-folder-checked"
        :number="overviewData.reviewed"
        content="已验收"
      ></itemCard>
    </template>
    <template v-else-if="roleId === this.$RoleEnum.auditor">
      <itemCard
        icon="el-icon-s-order"
        :number="overviewData.unReview"
        content="待审核"
      ></itemCard>
      <itemCard
        icon="el-icon-edit-outline"
        :number="overviewData.reviewing"
        content="审核中"
      ></itemCard>
      <itemCard
        icon="el-icon-document-delete"
        :number="overviewData.repulse"
        content="打回中"
      ></itemCard>
      <itemCard
        icon="el-icon-s-claim"
        :number="overviewData.corrected"
        content="已修正"
      ></itemCard>
      <itemCard
        icon="el-icon-folder-checked"
        :number="overviewData.reviewed"
        content="已验收"
      ></itemCard>
    </template>
    <template v-else>
      <itemCard
        icon="el-icon-s-order"
        :number="overviewData.unmarked"
        content="待标注"
      ></itemCard>
      <itemCard
        icon="el-icon-edit-outline"
        :number="overviewData.noting"
        content="标注中"
      ></itemCard>
      <itemCard
        icon="el-icon-folder-checked"
        :number="overviewData.reviewed"
        content="已验收"
      ></itemCard>
      <itemCard
        icon="el-icon-delete"
        :number="overviewData.corrected"
        content="已废弃"
      ></itemCard>
      <itemCard
        icon="el-icon-date"
        :number="overviewData.expectEndTime"
        content="预计剩余(天)"
      ></itemCard>
    </template>
  </div>
</template>

<script>
import itemCard from "@/views/common/item-card.vue";

export default {
  name: "overview",
  data() {
    return {
      overviewData: {},
      loading: false
    };
  },
  computed: {
    roleId() {
      return this.$store.state.user.roleId;
    }
  },
  mounted() {},
  methods: {
    init(projectId) {
      this.$http({
        url: this.$http.adornUrl(`/statistics/overview`),
        method: "get",
        params: this.$http.adornParams({
          projectId: projectId,
          roleId: this.roleId
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.overviewData = data.data || {};
        }
      });
    }
  },
  components: {
    itemCard
  }
};
</script>

<style scoped lang="scss">
.mod-home {
  line-height: 1.5;
}
.home-top,
.chart-bar {
  display: flex;
  justify-content: space-between;
  .item {
    padding: 10px 20px;
    flex-grow: 1;
    flex-shrink: 1;
    flex-basis: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    border-radius: 8px;
    &:hover {
      transform: translateY(-0.3333333333rem);
      box-shadow: 0 0.2rem 1rem rgba(33, 40, 50, 0.1);
    }
    div:first-child {
      margin-right: 35px;
      width: 55px;
      height: 55px;
      text-align: center;
      line-height: 63px;
      background-color: #579aff;
      border-radius: 8px;
      .icon-svg,
      i {
        font-size: 1.5rem;
        //color: #fff;
      }
    }
    div:last-child {
      display: flex;
      flex-direction: column;
      text-align: right;
      span:first-child {
        font-size: 1.4rem;
        color: #182524;
        font-weight: 600;
      }
      span:last-child {
        color: #6b7373;
      }
    }
  }
  .item:not(:last-child),
  .el-card:not(:last-child) {
    margin-right: 10px;
  }
  .el-card {
    flex-grow: 1;
    flex-shrink: 1;
    flex-basis: 100%;
    ::v-deep .el-card__body {
      height: 100% !important;
    }
  }
}
</style>
