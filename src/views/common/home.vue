<template>
  <div>
    <el-radio-group
      v-if="projectId !== null"
      fill="#3474FE"
      style="margin-bottom: 15px"
      @input="changeProject()"
      v-model="projectId"
      size="medium"
    >
      <el-radio-button
        style="margin: 3px"
        v-for="(item, idx) in projectList"
        :key="'project-radio-' + idx"
        :label="item.projectId"
        ><i v-if="projectId === item.projectId" class="el-icon-menu"></i>
        {{ item.projectName }}
      </el-radio-button>
    </el-radio-group>
    <div class="mod-home">
      <div v-if="projectId !== null">
        <!--统计-->
        <overview ref="overviewRef"></overview>
        <!--项目总进度趋势图-->
        <progress-chart ref="progressChart"></progress-chart>
        <div class="chart-bar">
          <!--标注进度-->
          <work-chart
            chart-id="annWorkChart"
            v-if="roleId !== this.$RoleEnum.annotator"
            ref="annWorkChart"
          ></work-chart>
          <!--审核进度-->
          <work-chart
            chart-id="auditorWorkChart"
            v-if="
              roleId !== this.$RoleEnum.annotator &&
              roleId !== this.$RoleEnum.auditor
            "
            ref="auditorWorkChart"
          ></work-chart>
        </div>
      </div>
      <div v-else>
        <el-card>
          <el-empty
            style="height: calc(100vh - 125px)"
            description="暂无项目"
          ></el-empty>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script>
import overview from "@/views/common/overview.vue";
import progressChart from "@/views/common/progress-chart.vue";
import workChart from "@/views/common/work-chart.vue";

export default {
  data() {
    return {
      projectList: [],
      projectId: null
    };
  },
  components: {
    overview,
    progressChart,
    workChart
  },
  created() {
    if (
      this.roleId === this.$RoleEnum.rootAdmin ||
      this.roleId === this.$RoleEnum.admin
    ) {
      this.$router.replace({ path: "sys-user" });
      return;
    }
    this.getProject();
  },
  methods: {
    getProject() {
      this.$http({
        url: this.$http.adornUrl(`/statistics/getProjects`),
        method: "get",
        params: this.$http.adornParams({
          roleId: this.roleId
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.projectList = data.data || [];
          if (this.projectId === null && this.projectList.length !== 0) {
            this.projectId = this.projectList[0].projectId;
            this.$nextTick(() => {
              this.refreshData();
            });
          } else {
            this.projectId = null;
          }
        }
      });
    },
    changeProject() {
      this.refreshData();
    },
    refreshData() {
      this.$refs.overviewRef.init(this.projectId);
      this.$refs.progressChart.init(this.projectId);
      if (
        this.roleId === this.$RoleEnum.auditor ||
        this.roleId === this.$RoleEnum.projectAdmin ||
        this.roleId === this.$RoleEnum.projectWatcher
      ) {
        this.$refs.annWorkChart.init(this.projectId, this.$RoleEnum.annotator);
      }
      if (
        this.roleId === this.$RoleEnum.projectAdmin ||
        this.roleId === this.$RoleEnum.projectWatcher
      ) {
        this.$refs.auditorWorkChart.init(
          this.projectId,
          this.$RoleEnum.auditor
        );
      }
    }
  },
  computed: {
    roleId: function () {
      return this.$store.state.user.roleId;
    },
    sidebarFold: {
      get() {
        return this.$store.state.common.sidebarFold;
      }
    }
  }
};
</script>

<style scoped lang="scss">
.mod-home {
  line-height: 1.5;
}

.home-top,
.chart-bar {
  display: flex;
  justify-content: space-between;

  .item {
    padding: 10px 20px;
    flex-grow: 1;
    flex-shrink: 1;
    flex-basis: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    border-radius: 8px;

    &:hover {
      transform: translateY(-0.3333333333rem);
      box-shadow: 0 0.2rem 1rem rgba(33, 40, 50, 0.1);
    }

    div:first-child {
      margin-right: 35px;
      width: 55px;
      height: 55px;
      text-align: center;
      line-height: 63px;
      background-color: #579aff;
      border-radius: 8px;

      .icon-svg,
      i {
        font-size: 1.5rem;
        //color: #fff;
      }
    }

    div:last-child {
      display: flex;
      flex-direction: column;
      text-align: right;

      span:first-child {
        font-size: 1.4rem;
        color: #182524;
        font-weight: 600;
      }

      span:last-child {
        color: #6b7373;
      }
    }
  }

  .item:not(:last-child),
  .el-card:not(:last-child) {
    margin-right: 10px;
  }

  .el-card {
    flex-grow: 1;
    flex-shrink: 1;
    flex-basis: 100%;

    ::v-deep .el-card__body {
      height: 100% !important;
    }
  }
}
</style>
