<template>
  <el-card style="margin-top: 15px">
    <div :style="styleHeight" id="chart-dateStatistics"></div>
  </el-card>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "ProgressChart",
  data() {
    return {};
  },
  computed: {
    roleId() {
      return this.$store.state.user.roleId;
    },
    styleHeight() {
      return {
        width: "100%",
        height: this.roleId === this.$RoleEnum.annotator ? "66vh" : "50vh"
      };
    }
  },
  mounted() {},
  methods: {
    init(projectId) {
      const myChart = echarts.init(
        document.getElementById("chart-dateStatistics")
      );
      myChart.showLoading();
      this.$http({
        url: this.$http.adornUrl(`/statistics/dateStatistics`),
        method: "get",
        params: this.$http.adornParams({
          projectId: projectId
        })
      })
        .then(({ data }) => {
          if (data && data.code === 0 && data.data) {
            const dateStatistics = [
              ["date", ...data.data.map((it) => it.createTime)],
              ["未标注", ...data.data.map((it) => it.unmarked)],
              ["标注中", ...data.data.map((it) => it.marked)],
              ["已验收", ...data.data.map((it) => it.reviewed)]
            ];
            this.drawDateStatistics(myChart, dateStatistics);
            myChart.hideLoading();
          }
        })
        .catch((err) => {
          this.$message.error(err);
        });
      myChart.on("updateAxisPointer", function (event) {
        const xAxisInfo = event.axesInfo[0];
        if (xAxisInfo) {
          const dimension = xAxisInfo.value + 1;
          myChart.setOption({
            series: {
              id: "pie",
              label: {
                formatter: "{b}: {@[" + dimension + "]} ({d}%)"
              },
              encode: {
                value: dimension,
                tooltip: dimension
              }
            }
          });
        }
      });
    },
    drawDateStatistics(myChart, data) {
      let lastDate = "";
      if (data != null && data.length > 1 && data[0].length > 1) {
        lastDate = data[0][data[0].length - 1];
      }
      myChart.setOption({
        color: ["#909399", "#5470C6", "#91CC75"],
        title: {
          text: "项目总进度"
        },
        legend: {},
        tooltip: {
          trigger: "axis",
          showContent: true
        },
        dataset: {
          source: data
        },
        xAxis: { type: "category" },
        yAxis: { gridIndex: 0 },
        grid: { left: "53%", right: "0%" },
        series: [
          { type: "line", smooth: true, seriesLayoutBy: "row" },
          { type: "line", smooth: true, seriesLayoutBy: "row" },
          { type: "line", smooth: true, seriesLayoutBy: "row" },
          {
            type: "pie",
            id: "pie",
            radius: ["30%", "70%"],
            center: ["25%", "50%"],
            itemStyle: {
              borderRadius: 10,
              borderColor: "#fff",
              borderWidth: 2
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 14,
                fontWeight: "bold"
              }
            },
            label: {
              formatter: "{b}: {@" + lastDate + "}"
            },
            encode: {
              itemName: "date",
              value: lastDate,
              tooltip: lastDate
            }
          }
        ],
        dataZoom: [
          {
            show: data[0].length > 10,
            realtime: true
          },
          {
            type: "inside",
            show: true,
            xAxisIndex: [0],
            start: 0,
            end: 100
          }
        ]
      });
    }
  },
  components: {}
};
</script>

<style scoped lang="scss">
.mod-home {
  line-height: 1.5;
}
.home-top,
.chart-bar {
  display: flex;
  justify-content: space-between;
  .item {
    padding: 10px 20px;
    flex-grow: 1;
    flex-shrink: 1;
    flex-basis: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    border-radius: 8px;
    &:hover {
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }
    div:first-child {
      margin-right: 35px;
      width: 55px;
      height: 55px;
      text-align: center;
      line-height: 63px;
      background-color: #579aff;
      border-radius: 8px;
      .icon-svg,
      i {
        font-size: 1.5rem;
        //color: #fff;
      }
    }
    div:last-child {
      display: flex;
      flex-direction: column;
      text-align: right;
      span:first-child {
        font-size: 1.4rem;
        color: #182524;
        font-weight: 600;
      }
      span:last-child {
        color: #6b7373;
      }
    }
  }
  .item:not(:last-child),
  .el-card:not(:last-child) {
    margin-right: 10px;
  }
  .el-card {
    flex-grow: 1;
    flex-shrink: 1;
    flex-basis: 100%;
    ::v-deep .el-card__body {
      height: 100% !important;
    }
  }
}
</style>
