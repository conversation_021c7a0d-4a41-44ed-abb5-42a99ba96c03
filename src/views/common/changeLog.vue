<template>
  <el-dialog title="更新记录" :visible.sync="visible" :append-to-body="true">
    <div class="log-dialog-container">
      <div v-for="(item, idx) in changeLogs" :key="`cg-log-ver-${idx}`">
        <div style="padding-left: 10px">
          <span v-text="item.version" style="font-weight: bolder"></span>
          <span> ({{ item.date }})</span>
        </div>

        <div>
          <ul v-if="item.logs && item.logs.length > 0">
            <li
              class="log-li"
              v-for="(log, idx2) in item.logs"
              :key="`cg-log-${idx}-${idx2}`"
            >
              {{ log }}
            </li>
          </ul>
        </div>

        <el-divider></el-divider>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="visible = false">关闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: "changeLog",
  props: {
    changeLogs: {
      type: Array,
      required: false,
      default: () => []
    }
  },
  data() {
    return {
      visible: true
    };
  },
  methods: {
    init() {
      this.visible = true;
    }
  }
};
</script>

<style scoped lang="scss">
.log-li {
  padding: 6px 3px;
}

.log-dialog-container {
  max-height: 600px;
  overflow: auto;
}
</style>
