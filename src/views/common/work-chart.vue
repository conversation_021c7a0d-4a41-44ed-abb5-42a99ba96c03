<template>
  <el-card style="margin-top: 15px">
    <div style="width: 100%; height: 50vh" :id="chartId"></div>
  </el-card>
</template>

<script>
import * as echarts from "echarts";

export default {
  props: {
    chartId: {
      type: String,
      require: true
    }
  },
  name: "WorkChart",
  data() {
    return {
      barMaxWidth: 60
    };
  },
  created() {},
  computed: {
    roleId() {
      return this.$store.state.user.roleId;
    },
    styleHeight() {
      return {
        width: "100%",
        height: this.roleId === this.$RoleEnum.annotator ? "66vh" : "50vh"
      };
    }
  },
  mounted() {},
  methods: {
    init(projectId, roleId) {
      const myChart = echarts.init(document.getElementById(this.chartId));
      myChart.showLoading();
      this.$http({
        url: this.$http.adornUrl(`/statistics/workProgressStatistics`),
        method: "get",
        params: this.$http.adornParams({
          projectId: projectId,
          roleId: roleId
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          myChart.hideLoading();
          this.drawChart(myChart, data.data || [], roleId);
        }
      });
    },
    drawChart(myChart, data, roleId) {
      let option = {
        title: {
          text: `${roleId === this.$RoleEnum.annotator ? "标注" : "审核"}进度`
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow"
          }
        },
        legend: {
          x: "right"
          // data: ["标注中", "待审核", "已验收"]
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "10%",
          containLabel: true
        },
        xAxis: {
          type: "value",
          axisLine: {
            show: true
          }
        },
        yAxis: {
          type: "category",
          data: data.map((it) => it.username)
        },
        series: [
          {
            name: "已验收",
            type: "bar",
            stack: "总量",
            barMaxWidth: this.barMaxWidth,
            data: data.map((it) => it.reviewed || 0),
            itemStyle: {
              color: "#91CC75"
            },
            label: {
              show: true,
              formatter: function (params) {
                if (params.value > 0) {
                  return params.value;
                } else {
                  return "";
                }
              }
            }
          }
        ]
      };
      if (roleId === this.$RoleEnum.annotator) {
        option.series.unshift(
          {
            name: "标注中",
            type: "bar",
            stack: "总量",
            barMaxWidth: this.barMaxWidth,
            data: data.map((it) => it.noting || 0),
            itemStyle: {
              color: "#5470C6"
            },
            label: {
              show: true,
              formatter: function (params) {
                if (params.value > 0) {
                  return params.value;
                } else {
                  return "";
                }
              }
            }
          },
          {
            name: "待审核",
            type: "bar",
            stack: "总量",
            barMaxWidth: this.barMaxWidth,
            data: data.map((it) => it.unReview || 0),
            itemStyle: {
              color: "#91949A"
            },
            label: {
              show: true,
              formatter: function (params) {
                if (params.value > 0) {
                  return params.value;
                } else {
                  return "";
                }
              }
            }
          }
        );
      } else {
        option.series.unshift({
          name: "审核中",
          type: "bar",
          stack: "总量",
          barMaxWidth: this.barMaxWidth,
          data: data.map((it) => it.reviewing || 0),
          itemStyle: {
            color: "#E6A23C"
          },
          label: {
            show: true,
            formatter: function (params) {
              if (params.value > 0) {
                return params.value;
              } else {
                return "";
              }
            }
          }
        });
      }
      myChart.setOption(option);
    }
  },
  components: {}
};
</script>

<style scoped lang="scss">
.mod-home {
  line-height: 1.5;
}
.home-top,
.chart-bar {
  display: flex;
  justify-content: space-between;
  .item {
    flex-grow: 1;
    flex-shrink: 1;
    flex-basis: 0;
    padding: 10px 0;
    display: flex;
    align-items: center;
    justify-content: space-around;
    background-color: #fff;
    border-radius: 8px;
    &:hover {
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }
    div:first-child {
      margin-right: 35px;
      width: 55px;
      height: 55px;
      text-align: center;
      line-height: 63px;
      background-color: #579aff;
      border-radius: 8px;
      .icon-svg,
      i {
        font-size: 1.5rem;
        //color: #fff;
      }
    }
    div:last-child {
      display: flex;
      flex-direction: column;
      text-align: right;
      span:first-child {
        font-size: 1.4rem;
        color: #182524;
        font-weight: 600;
      }
      span:last-child {
        color: #6b7373;
      }
    }
  }
  .item:not(:last-child),
  .el-card:not(:last-child) {
    margin-right: 10px;
  }
  .el-card {
    flex-grow: 1;
    flex-shrink: 1;
    flex-basis: 100%;
    ::v-deep .el-card__body {
      height: 100% !important;
    }
  }
}
</style>
