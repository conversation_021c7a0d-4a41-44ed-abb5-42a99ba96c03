<template>
  <div class="site-wrapper site-page--login">
    <div class="login-main">
      <div class="forms-container">
        <h2 class="title">自然语言标注系统 3.0</h2>
        <el-form
          :model="dataForm"
          :rules="dataRule"
          ref="dataForm"
          @keyup.enter.native="dataFormSubmit()"
          status-icon
        >
          <el-form-item prop="userName">
            <el-input
              icon="el-icon-s-custom"
              v-model="dataForm.userName"
              placeholder="帐号"
            ></el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input
              v-model="dataForm.password"
              type="password"
              placeholder="密码"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button
              :loading="loadStatus"
              class="login-btn-submit"
              type="primary"
              @click="dataFormSubmit()"
              >登录
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="panels-container">
      <div class="panel">
        <img src="@/assets/img/hero.png" alt="" />
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      loadStatus: false,
      dataForm: {
        userName: "",
        password: "",
        uuid: "",
        captcha: ""
      },
      dataRule: {
        userName: [
          { required: true, message: "帐号不能为空", trigger: "blur" }
        ],
        password: [
          { required: true, message: "密码不能为空", trigger: "blur" }
        ],
        captcha: [
          { required: true, message: "验证码不能为空", trigger: "blur" }
        ]
      },
      captchaPath: ""
    };
  },
  created() {},
  methods: {
    // 提交表单
    dataFormSubmit() {
      if (this.loadStatus) {
        return;
      }
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          this.loadStatus = true;
          this.$http({
            url: this.$http.adornUrl("/sys/login"),
            method: "post",
            data: this.$http.adornData({
              username: this.dataForm.userName,
              password: this.dataForm.password
            })
          }).then(
            ({ data }) => {
              if (data && data.code === 0) {
                this.$cookie.set("bnlp-token", data.token);
                this.$router.replace({ name: "home" });
              } else {
                this.dataForm.captcha = "";
              }
              this.loadStatus = false;
            },
            () => {
              this.loadStatus = false;
              console.error("访问后端连接失败");
              this.$message.error("登录失败，请联系管理员！");
            }
          );
        }
      });
    }
  }
};
</script>

<style lang="scss">
.site-page--login {
  background-image: url("~@/assets/img/hero-bg.png");
  background-size: cover;
  background-position: center;
  background-size: cover;
  position: relative;
  width: 100%;
  min-height: 100vh;
  overflow: hidden;

  .login-main {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;

    .forms-container {
      position: absolute;
      top: 45%;
      transform: translate(-50%, -50%);
      left: 75%;
      width: 50%;
      display: grid;
      grid-template-columns: 1fr;
      z-index: 5;

      .title {
        text-align: center;
        font-size: 2.2rem;
        color: #3f74cf;
        margin-bottom: 30px;
      }

      .el-input--medium,
      .el-form-item__content {
        max-width: 380px;
        margin: 0 auto;
        display: flex;
        align-items: center;
        justify-content: center;

        .el-input__inner {
          max-width: 380px;
          width: 100%;
          background-color: #f0f0f0;
          margin: 5px 0;
          height: 55px;
          border-radius: 55px;
        }

        .login-btn-submit {
          box-shadow: 0px 5px 30px rgba(65, 84, 241, 0.4);
          font-size: 1.02rem;
          width: 150px;
          background-color: #5995fd;
          height: 49px;
          border-radius: 49px;
          font-weight: 600;
          cursor: pointer;
          &:hover {
            background: rgba(#5995fd, 0.8);
            transform: translateY(-0.2rem);
          }
        }
      }
    }
  }

  .panels-container {
    position: absolute;
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
    display: grid;
    grid-template-columns: repeat(2, 1fr);

    .panel {
      img {
        position: absolute;
        min-width: 30%;
        left: 3%;
        top: 18%;
      }
    }
  }
}
</style>
