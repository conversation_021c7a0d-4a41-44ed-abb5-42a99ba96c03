<template>
  <nav class="site-navbar" :class="'site-navbar--' + navbarLayoutType">
    <div class="site-navbar__header">
      <h1 class="site-navbar__brand" @click="$router.push({ name: 'home' })">
        <img src="../assets/img/logo.png" alt="" style="width: 43px" />
        <a class="site-navbar__brand-lg title-pane" href="javascript:0;"
          >B N L P</a
        >
      </h1>
    </div>

    <div class="site-navbar__body clearfix">
      <!--            <el-menu class="site-navbar__menu" mode="horizontal">-->
      <!--                <el-menu-item class="site-navbar__switch" index="0" @click="sidebarFold = !sidebarFold">-->
      <!--                    <icon-svg name="zhedie"></icon-svg>-->
      <!--                </el-menu-item>-->
      <!--            </el-menu>-->
      <div class="site-navbar__menu" mode="horizontal">
        <div
          class="site-navbar__switch"
          index="0"
          @click="sidebarFold = !sidebarFold"
        >
          <icon-svg name="zhedie"></icon-svg>
        </div>
      </div>

      <el-menu
        class="site-navbar__menu site-navbar__menu--right"
        mode="horizontal"
      >
        <el-menu-item @click.native="showChangeLog()" index="1">
          {{ currVersionStr }}
        </el-menu-item>

        <!-- 角色信息 -->
        <template v-if="roles.length <= 1">
          <el-menu-item index="4">
            {{ roles[0].roleName }}
          </el-menu-item>
        </template>

        <template v-else>
          <el-submenu index="4">
            <template slot="title"
              >{{ roles.filter((it) => it.roleId === roleId)[0].roleName }}
            </template>
            <el-menu-item
              v-for="role in roles.filter((it) => it.roleId !== roleId)"
              :disabled="role.roleId === roleId"
              @click="changeCurrentRole(role.roleId)"
              :key="role.id"
              >{{ role.roleName }}
            </el-menu-item>
          </el-submenu>
        </template>

        <el-menu-item class="site-navbar__avatar" index="5">
          <el-dropdown :show-timeout="0" placement="bottom">
            <span class="el-dropdown-link">
              <span>用户名：{{ userName }}</span>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="updatePasswordHandle()"
                >修改密码
              </el-dropdown-item>
              <el-dropdown-item @click.native="logoutHandle()"
                >退出
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </el-menu-item>
      </el-menu>
    </div>
    <!-- 弹窗, 修改密码 -->
    <update-password
      v-if="updatePassowrdVisible"
      ref="updatePassowrd"
    ></update-password>

    <!-- 弹窗, 更新记录 -->
    <ChangeLog
      v-if="changeLogVisible"
      :change-logs="changeLogs"
      ref="changeLogDialogRef"
    ></ChangeLog>
  </nav>
</template>

<script>
import UpdatePassword from "./main-navbar-update-password";
import ChangeLog from "@/views/common/changeLog.vue";

import { clearLoginInfo } from "@/utils";
import changeLogs from "@/assets/files/changeLogs.json";

export default {
  data() {
    return {
      updatePassowrdVisible: false,
      changeLogVisible: false,
      roles: this.$store.state.user.roles,
      useRoleId: 0,
      currVersion: "",
      changeLogs: []
    };
  },
  created() {
    this.useRoleId = this.roleId;
  },
  mounted() {
    let changeLogs = require("@/assets/files/changeLogs.json");
    if (changeLogs && changeLogs.length > 0) {
      // 倒序，最新的在最上面
      changeLogs = changeLogs.reverse();
      this.changeLogs = changeLogs;
      this.currVersion = this.changeLogs[0].version;
    }
  },
  components: {
    UpdatePassword,
    ChangeLog
  },
  computed: {
    navbarLayoutType: {
      get() {
        return this.$store.state.common.navbarLayoutType;
      }
    },
    sidebarFold: {
      get() {
        return this.$store.state.common.sidebarFold;
      },
      set(val) {
        this.$store.commit("common/updateSidebarFold", val);
      }
    },
    mainTabs: {
      get() {
        return this.$store.state.common.mainTabs;
      },
      set(val) {
        this.$store.commit("common/updateMainTabs", val);
      }
    },
    userName: {
      get() {
        return this.$store.state.user.name;
      }
    },
    roleId: {
      get() {
        return this.$store.state.user.roleId;
      },
      set(roleId) {
        this.$store.commit("user/updateRoleId", roleId);
      }
    },
    currVersionStr() {
      return "Version: " + this.currVersion;
    }
  },
  methods: {
    showChangeLog() {
      this.changeLogVisible = true;
      this.$nextTick(() => {
        this.$refs.changeLogDialogRef.init();
      });
    },
    // 修改密码
    updatePasswordHandle() {
      this.updatePassowrdVisible = true;
      this.$nextTick(() => {
        this.$refs.updatePassowrd.init();
      });
    },
    // 退出
    logoutHandle() {
      this.$confirm("确定进行[退出]操作?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.$http({
            url: this.$http.adornUrl("/sys/logout"),
            method: "post",
            data: this.$http.adornData()
          }).then(({ data }) => {
            if (data && data.code === 0) {
              clearLoginInfo();
              this.$router.push({ name: "login" });
            }
          });
        })
        .catch(() => {});
    },
    changeCurrentRole(roleId) {
      this.roleId = roleId;
      sessionStorage.removeItem("workEnv");
      const pathName = location.pathname.substring(1);
      let to =
        this.roleId === this.$RoleEnum.rootAdmin ||
        this.roleId === this.$RoleEnum.admin
          ? "sys-user"
          : "home";
      var protocolStr = location.protocol;
      location.href = `${protocolStr}//${location.host}/${pathName.substring(
        0,
        pathName.indexOf("/")
      )}/${to}`;
    }
  }
};
</script>

<style scoped>
.site-navbar__switch {
  color: #909399;
  padding: 0 20px;
  line-height: 50px;
  cursor: pointer;
  position: relative;
  top: 3px;
}
</style>
