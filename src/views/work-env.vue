<template>
  <div>
    <el-dialog
      title="请选择：项目/批次"
      :visible="dialogVisible"
      :show-close="false"
      :append-to-body="true"
      :before-close="() => (this.dialogVisible = false)"
      width="20%"
    >
      <el-row>
        <el-col :span="24">
          <el-cascader
            style="width: 100%"
            class="batch-selected"
            :options="projects"
            @change="changeWorkEnv"
            @focus="getProject"
            :props="props"
            v-model="value"
          ></el-cascader>
        </el-col>
      </el-row>
    </el-dialog>

    <el-cascader
      class="batch-selected"
      :options="projects"
      @change="changeWorkEnv"
      @focus="getProject"
      :props="props"
      v-model="value"
    ></el-cascader>
  </div>
</template>

<script>
export default {
  data() {
    return {
      value: [],
      projects: [],
      dialogVisible: false,
      workEnv: JSON.parse(sessionStorage.getItem("workEnv")) || undefined,
      props: {
        value: "id",
        label: "name",
        children: "batches"
      }
    };
  },
  watch: {
    projects: function () {
      if (this.workEnv) {
        const project = this.$_.find(
          this.projects,
          (project) => project.projectId === this.workEnv.projectId
        );
        if (project && project.batches && project.batches.length > 0) {
          if (
            this.$_.find(
              project.batches,
              (batch) => batch.batchId === this.workEnv.batchId
            )
          ) {
            this.dialogVisible = false;
          }
        }
      }
    }
  },
  created() {
    // init
    this.getProject();
    if (this.workEnv) {
      this.value =
        ["p-" + this.workEnv.projectId, "b-" + this.workEnv.batchId] || [];
      const project = this.$_.find(
        this.projects,
        (project) => project.projectId === this.workEnv.projectId
      );
      if (project && project.batches && project.batches.length > 0) {
        if (
          this.$_.find(
            project.batches,
            (batch) => batch.batchId === this.workEnv.batchId
          )
        ) {
          this.dialogVisible = false;
        }
      }
    } else {
      this.dialogVisible = true;
    }
  },
  methods: {
    getProject: async function () {
      await this.$http({
        url: this.$http.adornUrl("/project/participateProject"),
        method: "get",
        params: this.$http.adornParams({
          roleId: Number(sessionStorage.getItem("roleId"))
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          const projects = data.data || [];
          this.projects = projects.map((project) => {
            // 避免 id 冲突
            project.id = "p-" + project.projectId;
            const batches = project.batches || [];
            project.batches = batches.map((batch) => {
              batch.id = "b-" + batch.batchId;
              return batch;
            });
            return project;
          });
        }
      });
    },
    changeWorkEnv() {
      if (this.value && this.value.length >= 2) {
        sessionStorage.setItem(
          "workEnv",
          JSON.stringify({
            projectId: Number(this.value[0].split("-")[1]),
            batchId: Number(this.value[1].split("-")[1])
          })
        );
      }

      const pathName = location.pathname.substring(1);
      // location.href = 'http://' + location.host + '/' + pathName.substring(0, pathName.indexOf('/')) + '/'
      location.href =
        "http://" +
        location.host +
        "/" +
        pathName.substring(0, pathName.indexOf("/"));
    }
  }
};
</script>

<style scoped lang="scss">
.batch-selected {
  /*width: 300px;*/
}

::v-deep .el-cascader {
  .el-input__inner {
    border: none !important;
  }
}
</style>
