import { trimStr } from "@/utils";

export default {
  namespaced: true,
  state: {
    currAttrTitle: null,
    currAttrId: null,
    currAnnoId: null
  },
  mutations: {
    SET_CURR_ATTR(state, val) {
      if (val) {
        state.currAttrTitle = val.currAttrTitle;
        state.currAttrId = val.currAttrId;
      } else {
        state.currAttrTitle = null;
        state.currAttrId = null;
      }
    },
    SET_CURR_ANNO_ID(state, val) {
      state.currAnnoId = trimStr(val);
    }
  },
  actions: {
    setCurrAttr({ commit }, val) {
      commit("SET_CURR_ATTR", val);
    },
    setCurrAnnoId({ commit }, val) {
      commit("SET_CURR_ANNO_ID", val);
    }
  }
};
