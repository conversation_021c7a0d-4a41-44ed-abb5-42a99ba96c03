export default {
  namespaced: true,
  state: {
    annotation: {
      currAnnoUniId: null,
      isAttr: false,
      projectId: 0,
      batchId: 0,
      documentId: "",
      articleId: "",
      noteId: 0,
      taskId: 0,
      labelId: null,
      conceptId: "",
      source: 1,
      importLogId: null,
      sourceChangeTimes: 0,
      preEntity: false
    },
    navbar: {
      projectName: null,
      hasSpecFile: false,
      batchName: null,
      articleId: null,
      step: null,
      status: null,
      invalid: null,
      taskStep: null,
      taskStatus: null,
      repulseMsg: null,
      editable: null,
      documentSource: null
    },
    editable: false,
    editableByRoleAndStep: false,
    activeTab: "",
    annoTypeSwitch: false,
    batchSwitch: false,
    subjectAnno: undefined,
    objectsAnno: undefined,
    isCtrlPressed: false,
    continuousAnnotations: null,
    // 当前选中实体的批注信息
    annotate: null,
    dialogVisible: false,
    currLabelMap: new Map(),
    // 当前选中的蓝本任务id，只有多人标注界面才会设置，标注员界面不会设置此参数
    masterTaskId: 0,
    showImportBtn: false,
    // 历史版本查看模式
    historyMode: {
      enabled: false,
      discussionId: null
    }
  },
  mutations: {
    setBatch(state, val) {
      state.annotation.projectId = val.projectId;
      state.annotation.batchId = val.batchId;
    },
    setLabelId(state, val) {
      state.annotation.labelId = val ? val.toString() : null;
    },
    setArticleId(state, val) {
      state.annotation.articleId = val;
    },
    setAnno(state, val) {
      if (val) {
        state.annotation.currAnnoUniId = val.uniqueid;
        state.annotation.isAttr = val.isAttr;
        state.annotation.labelId = val.labelId ? val.labelId.toString() : null;
      } else {
        state.annotation.currAnnoUniId = null;
        state.annotation.isAttr = false;
        state.annotation.labelId = null;
      }
    },
    setNoteId(state, val) {
      if (val) {
        state.annotation.noteId = val.noteId;
        state.annotation.taskId = val.taskId;
      } else {
        state.annotation.noteId = null;
        state.annotation.taskId = null;
      }
    },
    setNavbar(state, val) {
      state.navbar.projectName = val.projectName;
      state.navbar.hasSpecFile = val.hasSpecFile;
      state.navbar.batchName = val.batchName;
      state.navbar.articleId = val.articleId;
      state.navbar.invalid = val.invalid;
      state.navbar.step = val.step;
      state.navbar.status = val.status;
      state.navbar.taskStep = val.taskStep;
      state.navbar.taskStatus = val.taskStatus;
      state.navbar.repulseMsg = val.repulseMsg;
      state.navbar.editable = val.editable;
      state.navbar.documentSource = val.documentSource;
    },
    setNavbarInvalid(state, val) {
      state.navbar.invalid = val.invalid;
    },
    setActiveTab(state, val) {
      state.activeTab = val;
    },
    setEditable(state, val) {
      state.editable = val;
    },
    setEditableByRoleAndStep(state, val) {
      state.editableByRoleAndStep = val;
    },
    setDocumentId(state, val) {
      state.annotation.documentId = val;
    },
    setSource(state, val) {
      state.annotation.source = val.id;
      if (val.importLogId) {
        state.annotation.importLogId = val.importLogId;
      } else {
        state.annotation.importLogId = null;
      }
      state.annotation.sourceChangeTimes =
        state.annotation.sourceChangeTimes + 1;
    },
    addSourceChangeTimes(state) {
      state.annotation.sourceChangeTimes =
        state.annotation.sourceChangeTimes + 1;
    },
    setPreEntity(state, val) {
      state.annotation.preEntity = val;
    },
    setSubjectAnno(state, value) {
      state.subjectAnno = value;
    },
    setObjectsAnno(state, value) {
      state.objectsAnno = value;
    },
    setAnnoTypeSwitch(state, val) {
      state.annoTypeSwitch = val;
    },
    setBatchSwitch(state, val) {
      state.batchSwitch = val;
    },
    setIsCtrlPressed(state, value) {
      state.isCtrlPressed = value;
    },
    setContinuousAnnotations(state, value) {
      state.continuousAnnotations = value;
    },
    setCurrAnno(state, value) {
      state.annotation.currAnnoUniId = value;
      state.annotation.isAttr = false;
    },
    setAnnotate(state, value) {
      state.annotate = value;
    },
    setDialogVisible(state, value) {
      state.dialogVisible = value;
    },
    setCurrLabelMap(state, value) {
      state.currLabelMap = value;
    },
    setMasterTaskId(state, value) {
      state.masterTaskId = value;
    },
    setShowImportBtn(state, value) {
      state.showImportBtn = value;
    },
    setHistoryMode(state, value) {
      state.historyMode = value;
    }
  }
};
