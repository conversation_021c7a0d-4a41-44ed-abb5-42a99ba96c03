export default {
  namespaced: true,
  state: {
    id: 0,
    name: "",
    roleId: 0,
    roles: []
  },
  mutations: {
    updateId(state, id) {
      state.id = id;
    },
    updateName(state, name) {
      state.name = name;
    },
    updateRoles(state, roles) {
      state.roles = roles;
    },
    updateRoleId(state, roleId) {
      state.roleId = roleId;
      sessionStorage.setItem("roleId", roleId);
    }
  }
};
