<template lang="html">
  <section class="p-table" :data-id="id">
    <div class="table-wrapper">
      <table>
        <thead>
          <tr v-for="(row, idx) in thead" :key="idx">
            <base-text
              v-for="(cell, idx) in row"
              :data="cell"
              tag="th"
              :colspan="cell.colspan"
              :rowspan="cell.rowspan"
              :key="idx"
            />
          </tr>
        </thead>
        <tbody>
          <tr v-for="(row, idx) in tbody" :key="idx">
            <base-text
              v-for="(cell, idx) in row"
              :data="cell"
              tag="td"
              :colspan="cell.colspan"
              :rowspan="cell.rowspan"
              :key="idx"
            />
          </tr>
        </tbody>
      </table>
    </div>
    <base-section :data="caption" isSub :key="`section_table_${id}`" />
  </section>
</template>

<script lang="js">
import BaseSection from "@/components/annotation/base-section";
import BaseText from "@/components/annotation/base-text";

export default {
  name: "BaseTable",
  data () {
    return {
      id: "",
      thead: [],
      tbody: [],
      caption: ""
    };
  },
  props: [
    "data"
  ],
  components: {
    BaseSection,
    BaseText
  },
  methods: {
    render () {
      const table = this.data;
      if (!table) return;
      this.$assert(table.cls === "Table");

      this.thead = table.thead;
      this.tbody = table.tbody;
      this.caption = table.caption;
      this.id = table.id;
    }
  },
  watch: {
    data: {
      handler () {
        this.render();
      },
      deep: true,
      immediate: true
    }
  }
};
</script>

<style lang="scss" scoped>
.p-table {
  margin: 60px 0;

  .table-wrapper {
    max-width: 100%;
    overflow-x: auto;
  }

  section {
    margin: 30px 0 !important;
  }

  table {
    border-top: solid 1px #000;
    border-bottom: solid 1px #000;
    border-collapse: separate;
    border-spacing: 0;

    th {
      border-bottom: 0.5px solid #000;
    }

    tr {
      font-size: 12px;
      text-align: left;
    }

    td,
    th {
      padding: 6px;
    }
  }
}
</style>
