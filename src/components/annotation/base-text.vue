<template lang="html">
  <component
    :is="tag"
    :ref="`text-${id}`"
    :data-id="id"
    :data-entityid="id"
    @click="changeEntity"
    @dblclick="dbClickAnno"
    @mouseleave="removeListener"
    @mouseover="deleteAnnotate"
    @mouseup="doAnnotate"
    v-html="innerHTML"
  >
  </component>
</template>

<script>
import { AnnoEventsBus } from "@/utils/bus";
import { isAttrTag, joinArray, sleep, trimStr } from "@/utils";
import BaseCss from "@/components/annotation/base-css.vue";

class Tag {
  constructor(isStart, attrs = {}, name = "span") {
    this.isStart = isStart;
    this.attrs = attrs;
    this.name = name;
    if (attrs) {
      this.start = attrs.start;
      this.end = attrs.end;
    }
  }
}

export default {
  name: "BaseText",
  components: { BaseCss },
  props: {
    data: {
      required: true
    },
    tag: {
      required: true
    }
  },
  data() {
    return {
      mouseEvent: "",
      id: "",
      innerHTML: "",
      annotations: []
    };
  },
  computed: {
    currAttrId: function () {
      return trimStr(this.$store.state.attr.currAttrId);
    },
    currAnnoId() {
      return this.$store.state.attr.currAnnoId;
    },
    annotation: function () {
      return this.$store.state.anno.annotation;
    },
    activeTab: function () {
      return this.$store.state.anno.activeTab;
    },
    isAttributeAnno() {
      // 是否为属性标注
      return this.$store.state.anno.activeTab === "attribute";
    },
    editable() {
      return this.$store.state.anno.editable;
    },
    source() {
      return this.$store.state.anno.annotation.source;
    },
    isCtrlPressed() {
      return this.$store.state.anno.isCtrlPressed;
    },
    continuousAnnotations() {
      return this.$store.state.anno.continuousAnnotations;
    }
  },
  watch: {
    data: {
      handler() {
        const text = this.data;
        if (!text || !text.cls) return;
        this.$assert(
          text.cls === "Paragraph" ||
            text.cls === "Title" ||
            text.cls === "Td" ||
            text.cls === "Th"
        );
        this.id = text.id;

        this.removeListener();
        this.annotations = this.$_.clone(text.annotations) || [];
        this.render();
      },
      deep: true,
      immediate: true
    },
    annotations: {
      handler() {
        this.render();
      },
      deep: true,
      immediate: true
    }
  },
  beforeUpdate() {
    AnnoEventsBus.$emit("getScrollTop");
  },
  updated() {
    AnnoEventsBus.$emit("setScrollTop");
  },
  created() {
    // 隐藏标注内容
    AnnoEventsBus.$on("hideAnnotation", () => {
      this.$nextTick(() => {
        this.annotations = [];
      });
    });
    // 添加实体标注
    /* AnnoEventsBus.$on("addAnnotations_" + this.id, (arg) => {
      this.$nextTick(() => {
        // 删除重复的标注
        for (let i = 0; i < this.annotations.length; i++) {
          if (this.annotations[i].start === arg.start) {
            this.annotations.splice(i, 1);
          }
        }
        this.annotations.push(arg);
      });
    }); */
  },
  mounted() {
    this.render();
    window.drag = this.drag;
    window.addEventListener("keyup", this.handleKeyup);
  },
  destroyed() {
    window.removeEventListener("keyup", this.handleKeyup);
    AnnoEventsBus.$off("hideAnnotation");
  },
  methods: {
    // 检查标注权限
    hasMarkPermission(allowFlag, hideMsg) {
      const availableTab =
        allowFlag ||
        this.activeTab === "entity" ||
        this.activeTab === "attribute";
      if (!this.editable || !availableTab) {
        if (!hideMsg && availableTab) {
          this.$message({
            message: "当前状态无编辑权限！",
            type: "error"
          });
        }
        return false;
      }
      return true;
    },
    // 划词标注实体
    doAnnotate(event) {
      /* if (event) {
        event.preventDefault();
      } */
      // 判断标注权限
      if (!this.hasMarkPermission(null, true)) {
        return;
      }
      const selection = window.getSelection();
      let term = "";
      for (let i = 0; i < selection.rangeCount; i++) {
        const range = selection.getRangeAt(i);
        const fragment = range.cloneContents();
        const div = document.createElement("div");
        div.appendChild(fragment);
        term += div.innerHTML.replace(/<[^>]+>/g, "").replace(/&nbsp;/g, " "); // 将 &nbsp; 转换为空格
      }
      term = this.htmlUnEscape(term);
      // 如果未选择则返回
      const blank = trimStr(term);

      if (!term || blank === "") {
        return;
      }

      if (this.source + "" !== "1") {
        this.$message({
          message: "此页面不允许划词标注，请切换到原文后重试！",
          type: "warning"
        });
        return false;
      }

      // 获取当前选中的一级标签ID
      const labelId = this.annotation.labelId;
      if (!labelId && !this.isAttributeAnno) {
        this.$message({
          message: "请先选择一级标签",
          type: "warning"
        });
        return;
      }

      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0); // 获取用户选区的第一个 Range
        const container = document.createElement("div");
        container.appendChild(range.cloneContents()); // 克隆选区内容（包含 HTML）
        if (container.innerHTML.includes("<section data-v-")) {
          this.$message({
            message:
              "单个实体不允许跨段落标注，如果需要标注横跨多个段落的实体，可按住Ctrl标注跳跃实体",
            type: "warning"
          });

          container.remove();
          return;
        }
        container.remove();
      }
      /* if (labelId !== 12345) {
        return false;
      } */

      // 找到选择的最后位置
      const end = this.getCaretCharacterOffsetWithin(
        this.$refs[`text-${this.id}`]
      );
      const start = end - term.length;
      // 检查选择是否重叠
      // for (let anno of this.annotations) {
      //   if (this.$_.max([start, anno.start]) < this.$_.min([end, anno.end])) {
      //     this.$message({
      //       message: '标注的文本不能重叠',
      //       type: 'warning'
      //     })
      //     return
      //   }
      // }

      // 通知UMLSConcept组件重置UMLSConcept
      // AnnoEventsBus.$emit("resetUMLSConcept");
      // 获取UMLSConcept数据
      // AnnoEventsBus.$emit("getUMLSConcept", blank);

      // 更新当前选择的实体到Vuex
      const anno = {
        start: start,
        end: end,
        label: labelId,
        textId: this.id,
        is_attr: this.isAttributeAnno
      };

      // 批量标注
      /*
      const batchSwitch = sessionStorage.getItem("batchSwitch");
      if (batchSwitch === "on") {
        this.$confirm("确认批量标注全文中的【" + blank + "】？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            this.annotations.push(anno);
            this.$store.commit("anno/setAnno", anno);
            // 后端批量新增标注数据
            this.$http({
              url: this.$http.adornUrl("/batch/note/addEntity"),
              method: "post",
              data: this.$http.adornData({
                projectId: this.annotation.projectId,
                batchId: this.annotation.batchId,
                noteId: this.annotation.noteId,
                articleId: this.annotation.articleId,
                textId: this.id,
                labelId: labelId,
                start: start,
                end: end,
                text: blank
              })
            }).then(({ data }) => {
              // 通知标签组件重置标签
              AnnoEventsBus.$emit("resetLabels");
              // 通知UMLSConcept组件重置UMLSConcept
              AnnoEventsBus.$emit("resetUMLSConcept");
              // 获取UMLSConcept数据
              AnnoEventsBus.$emit("getUMLSConcept", blank);
              AnnoEventsBus.$emit("showAnnoTip", event);
              AnnoEventsBus.$emit("changeSource", this.source);

              if (data && data.code !== 0) {
                this.$message({
                  message: "标注失败",
                  type: "error",
                  duration: 1500
                });
              } else {
                this.$message({
                  type: "success",
                  message: "批量标注成功!"
                });
              }
            });
          })
          .catch(() => {});
        return;
      } */

      this.annotations.push(anno);
      // this.$store.commit("anno/setAnno", anno);

      if (this.isCtrlPressed) {
        // 连续标注
        const conAnno = this.getAnnoData(
          this.continuousAnnotations,
          labelId,
          anno,
          blank
        );
        this.$store.commit("anno/setContinuousAnnotations", conAnno);
        return false;
      }
      AnnoEventsBus.$emit(
        "submitAnnoData",
        this.getAnnoData(null, labelId, anno, blank),
        event
      );
    },
    getAnnoData(oldData, labelId, anno, blank) {
      let conAnno = JSON.parse(JSON.stringify(oldData));
      if (!conAnno) {
        conAnno = {
          projectId: this.annotation.projectId,
          batchId: this.annotation.batchId,
          noteId: this.annotation.noteId,
          taskId: this.annotation.taskId,
          articleId: this.annotation.articleId,
          labelId: labelId,
          is_attr: anno.is_attr
        };
      }
      let entityInfos = [];
      if (conAnno.entityInfos) {
        entityInfos = conAnno.entityInfos;
      }
      entityInfos.push({
        start: anno.start,
        end: anno.end,
        content: blank,
        textId: anno.textId
      });
      // 标注信息去重
      /* conAnno.entityInfos = [
        ...new Set(entityInfos.map((item) => JSON.stringify(item)))
      ].map((i) => JSON.parse(i)); */
      conAnno.entityInfos = this.$_.uniqWith(entityInfos, this.$_.isEqual);
      return conAnno;
    },
    getAltPressedOrNot(e) {
      return e.altKey && !(e.ctrlKey || e.shiftKey);
    },
    dbClickAnno(event) {
      if (!event) {
        return;
      }
      let target = event.target;

      const isPre = target.getAttribute("source") === "2";
      const currUniqueid = trimStr(target.getAttribute("uniqueid"));
      if (
        currUniqueid &&
        isPre &&
        target.tagName === "SPAN" &&
        target.classList.contains("tag")
      ) {
        // 预标注选项卡中，双击实体预标注，将该标注及其关联的属性预标注导入原文
        event.preventDefault();
        AnnoEventsBus.$emit("keyPressImportToOriginal", currUniqueid);
      }
    },
    // 切换标注
    changeEntity(event) {
      if (event) {
        event.preventDefault();
      }
      // 找到当前点击的实体，提取出数据
      let target = event.target;
      /* if (target) {
        return false;
      } */

      // 处理特殊的I标签存在外链的情况
      if (target.tagName === "I") {
        const href = target.getAttribute("href");
        if (href && href !== "undefined") {
          window.open(href);
        }
        target = target.parentNode;
      }
      if (!(target.tagName === "SPAN" && target.classList.contains("tag"))) {
        return;
      }

      // 判断是否点击添加属性操作
      // const isPre = target.classList.contains("black");
      const isPre = target.getAttribute("source") === "2";
      // 判断条件：非预处理、当前操作为“属性标注”、已选择实体标签且鼠标已聚焦选中某个属性、当前点击的是属性标签、按下alt键
      let addAttrAction =
        !isPre &&
        this.isAttributeAnno &&
        this.currAnnoId &&
        this.isEntityByUid() &&
        this.currAttrId &&
        target.getAttribute("is_attr") === "1";
      // && this.getAltPressedOrNot(event)
      if (addAttrAction && this.hasMarkPermission(null, true)) {
        // 点击添加属性
        this.clickAddAttr(target);
      } else {
        const currUniqueid = trimStr(target.getAttribute("uniqueid"));
        if (this.isAttributeAnno) {
          // 属性标注界面, 完全重叠的标注点击时，不切换标注，只弹出弹窗
          const start = trimStr(target.getAttribute("start"));
          const end = trimStr(target.getAttribute("end"));
          const curr_text_id = trimStr(target.getAttribute("curr_text_id"));
          const sameTags = document.querySelectorAll(
            `span.tag[curr_text_id="${curr_text_id}"][start="${start}"][end="${end}"][basetag="1"]`
          );
          if (sameTags && sameTags.length > 1) {
            AnnoEventsBus.$emit("showAnnoTip", event, currUniqueid);
            return false;
          }
        }

        this.$store.commit("anno/setAnnotate", null);
        const currAnnoid = trimStr(target.getAttribute("annoid"));

        // 构建实体数据
        const entityData = {
          annoid: currAnnoid,
          uniqueid: currUniqueid,
          labelId: target.getAttribute("labelid"),
          content: target.innerText,
          annotate: target.getAttribute("annotate"),
          is_attr: target.getAttribute("is_attr"),
          attr_ids: target.getAttribute("attr_ids"),
          isPre: isPre,
          showTip: true
        };

        // 检查是否需要跨页面跳转
        if (this.activeTab === "discussion") {
          // 在讨论区页面点击实体，触发跨页面跳转
          AnnoEventsBus.$emit("crossPageEntityClick", entityData);
        } else {
          // 在实体相关页面，正常处理
          if (this.activeTab === "entityList") {
            // 点击实体同步实体清单树选中项
            AnnoEventsBus.$emit("doChangeEntityTree", currAnnoid);
          }
          AnnoEventsBus.$emit("doChangeEntity", entityData);
        }
      }
    },
    isEntityByUid() {
      let id = this.currAnnoId;
      const target = document.querySelector(
        `span.tag[annoid='${id}'][basetag="1"]`
      );
      if (!target) {
        return false;
      }
      return target.getAttribute("is_attr") === "0";
    },
    clickAddAttr(target) {
      const dragInfo = this.initEntityDragInfo(target);
      dragInfo.currAttrId = this.currAttrId;
      AnnoEventsBus.$emit("doAddAttrEv", dragInfo);
      const loading = this.$loading({
        lock: false
      });
      sleep(800).then(() => {
        loading.close();
      });
    },
    // 监听退格和delete键，执行删除
    handleKeyup: function (event) {
      if (this.mouseEvent === "") {
        return;
      }
      // eslint-disable-next-line no-caller
      const e = event || window.event || arguments.callee.caller.arguments[0];
      if (!e) {
        return;
      }
      const { keyCode } = e;
      // 监听删除按键
      if (keyCode === 46 || keyCode === 8) {
        // 46 = Delete  8 = BackSpace
        if (this.activeTab === "relationship") {
          return;
        }
        if (!this.hasMarkPermission(this.activeTab === "entityList")) {
          return;
        }

        const target = this.mouseEvent;
        const annoid = trimStr(target.getAttribute("annoid"));
        if (
          !(
            target.tagName === "SPAN" &&
            target.classList.contains("tag") &&
            !!annoid
          )
        ) {
          return;
        }
        const is_attr = trimStr(target.getAttribute("is_attr"));
        const uniqueid = trimStr(target.getAttribute("uniqueid"));
        const attr_label_info = trimStr(target.getAttribute("attr_label_info"));
        this.deleteEntityEvent(uniqueid, is_attr, attr_label_info);
      }

      if (this.activeTab === "relationship") {
        const target = this.mouseEvent;
        if (!(target.tagName === "SPAN" && target.classList.contains("tag"))) {
          return;
        }
        if (target.classList.contains("black")) return;

        const index = target.getAttribute("index");
        const anno = this.annotations[index];
        anno.text = target.innerText;

        // 关系标注(subject)
        if (keyCode === 49 || keyCode === 97) {
          this.$store.commit("anno/setSubjectAnno", anno);
        }
        // 关系标注(objects)
        if (keyCode === 50 || keyCode === 98) {
          this.$store.commit("anno/setObjectsAnno", anno);
        }
      }
    },
    deleteEntityEvent(uniqueid, is_attr, attr_label_info) {
      AnnoEventsBus.$emit(
        "deleteCurrEntity",
        uniqueid,
        is_attr,
        attr_label_info
      );
    },
    // 监听鼠标移入实体
    deleteAnnotate(event) {
      this.mouseEvent = event.target;
      // 判断当前实体是否是预标注的
      /* const target = event.target;
      if (target.classList.contains("black") && !target.hasAttribute("title")) {
        const entityId = target.getAttribute("entityId");
        //  查询后端
        this.$http({
          url: this.$http.adornUrl("/note/getPreEntityLabel"),
          method: "get",
          params: this.$http.adornParams({
            id:
              this.annotation.noteId +
              "_" +
              entityId +
              "_" +
              this.annotation.source
          })
        }).then(({ data }) => {
          if (data) {
            target.setAttribute("title", data.data);
          }
        });
      } */
    },
    // 移除按钮监听
    removeListener() {
      this.mouseEvent = "";
    },
    annoBreakpoints: function (annotations = [], breakpoints = {}) {
      for (const [i, a] of annotations.entries()) {
        const [start, end] = [a.start || 0, a.end];
        breakpoints[start] = breakpoints[start] || [];
        // 如果是预标注的标签，则默认显示灰色背景   push()和unshift() -> 向数组的 尾部/头部 添加若干元素，并返回 数组的 新长度；
        let classVal = `tag `;
        const uniqueidVal = trimStr(a.uniqueid);
        const currLabel = trimStr(a.label);
        const labelIdOfAttrAnno = trimStr(a.labelIdOfAttrAnno);
        const currSource = trimStr(a.source);
        const isPre = this.$PreSourceType.SELF + "" !== currSource;
        if (a.isCombo) {
          classVal += "combo-anno ";
        }

        if (this.isAttributeAnno && !uniqueidVal) {
          // 前端即时回显(未发送保存请求之前)
          classVal += "tag-attr";
        } else if (isAttrTag(a.isAttr)) {
          //属性标注
          if (labelIdOfAttrAnno) {
            if (labelIdOfAttrAnno === "-1") {
              classVal += `tag-attr`;
            } else {
              classVal += `tag-attr-${labelIdOfAttrAnno}`;
            }
          } else {
            if (isPre) {
              classVal += "tag-attr-gray";
            } else {
              classVal += "tag-attr";
            }
          }
        } else {
          //实体标注
          if (isPre) {
            if (labelIdOfAttrAnno) {
              classVal += "tag-" + labelIdOfAttrAnno;
            } else {
              classVal += "tag-gray-" + currLabel;
            }
          } else {
            classVal += "tag-" + currLabel;
          }
        }

        if (a.questionLogo) {
          classVal += " question-logo";
        }
        const showMultiSameEntity = trimStr(a.showMultiSameEntity) === "1";

        if (showMultiSameEntity) {
          classVal += " multi-same-entity";
        }

        let tagParam = {
          class: classVal,
          index: i,
          // entityId: a.textId + "_" + a.start + "_" + a.end,
          annoid: trimStr(a.annoid),
          uniqueid: uniqueidVal,
          labelid: currLabel,
          annotate: trimStr(a.annotate),
          start: start,
          end: end,
          basetag: "1",
          is_attr: trimStr(a.isAttr),
          curr_text_id: trimStr(a.textId),
          source: currSource
        };
        if (a.attrLabelInfo) {
          tagParam.attr_label_info = a.attrLabelInfo;
        }
        if (a.questionLogo) {
          tagParam.question_logo = a.questionLogo;
        }
        if (showMultiSameEntity) {
          tagParam.show_multi_same_entity = "1";
        }
        if (a.attrLabelUsedInfo) {
          tagParam.attr_label_used_info = a.attrLabelUsedInfo;
          // 隐藏title
          // tagParam.title = a.attrLabelUsedInfo;
        }
        if (a.attrIds) {
          tagParam.attr_ids = a.attrIds;
        }
        if (a.entityIds) {
          tagParam.entity_ids = a.entityIds;
        }
        if (a.batchAnnotateId) {
          tagParam.batch_annotate_id = a.batchAnnotateId;
        }
        const tag = new Tag(true, tagParam);
        if (breakpoints[start].length === 0) {
          breakpoints[start].unshift(tag);
        } else {
          breakpoints[start].push(tag);
        }
        breakpoints[end] = breakpoints[end] || [];
        breakpoints[end].push(new Tag(false));
      }
      // 解决标注嵌套错误的问题
      const keys = Object.keys(breakpoints);
      if (keys.length > 0) {
        for (let i = 0; i < keys.length; i++) {
          const key = keys[i];
          const tags = breakpoints[key];
          if (tags instanceof Array && tags.length > 1) {
            //多个标签重叠时
            //先按照起始位置排序，再按照终止位置排序，最后按照终止位置取倒序
            breakpoints[key] = this.$_.sortBy(tags, ["start", "end"]).reverse();
          }
        }
      }
      return breakpoints;
    },
    fmtBreakpoints: function (formats = [], breakpoints = {}) {
      if (formats != null) {
        for (const a of formats) {
          const [start, end] = [a.start || 0, a.end];
          breakpoints[start] = breakpoints[start] || [];
          breakpoints[start].unshift(
            new Tag(
              true,
              {
                href: a.href,
                target: "_blank"
              },
              a.label
            )
          );
          breakpoints[end] = breakpoints[end] || [];
          breakpoints[end].push(new Tag(false, {}, a.label));
        }
      }
      return breakpoints;
    },
    drag(ev) {
      const target = ev.target;
      const dragInfo = this.initEntityDragInfo(target);
      ev.dataTransfer.setData("id", dragInfo.id);
      ev.dataTransfer.setData("text", dragInfo.text);
      ev.dataTransfer.setData("labelId", dragInfo.labelId);
      ev.dataTransfer.setData("isAttr", dragInfo.isAttr);
    },
    initEntityDragInfo(target) {
      const entityId = target.getAttribute("annoid");
      const dragInfo = {};
      dragInfo.id = entityId;
      dragInfo.labelId = target.getAttribute("labelid");
      dragInfo.isAttr = target.getAttribute("is_attr");

      const spans = document.querySelectorAll(
        `span[annoid='${entityId}'][basetag="1"]`
      );
      const spanTexts = [];
      spans.forEach((span) => {
        const text = span.textContent;
        spanTexts.push(text);
      });
      dragInfo.text = this.popoverPartContent(spanTexts);
      return dragInfo;
    },
    render: function () {
      const text = this.data;
      const breakpoints = this.annoBreakpoints(
        this.annotations,
        this.fmtBreakpoints(text && text.formats)
      );

      if (text && text.content) {
        if (!this.$_.isEmpty(breakpoints)) {
          const pos = Object.keys(breakpoints);
          const segs = this.$_.zip(
            pos,
            pos.slice(1, pos.length).concat([text.content.length])
          );
          let html = text.content.slice(0, pos[0] || text.content.length);
          for (const [start, end] of segs) {
            for (const tag of breakpoints[start]) {
              html += this.renderTag(tag);
            }
            // html += text.content.slice(start, end);
            html += this.replaceSpecialChar(text.content.slice(start, end));
          }
          this.innerHTML = html;
        } else {
          this.innerHTML = this.replaceSpecialChar(text.content);
        }
      }
    },
    // 重新渲染标签
    renderTag: function (tag) {
      if (tag.isStart) {
        let attrString = "";
        for (const [k, v] of Object.entries(tag.attrs)) {
          attrString += ` ${k}="${v}"`;
        }
        if (tag.name === "span") {
          let canDrag;
          if (this.isAttributeAnno) {
            canDrag = isAttrTag(tag.attrs.is_attr);
          } else {
            canDrag = !isAttrTag(tag.attrs.is_attr);
          }
          return `<${tag.name}${attrString} draggable="${canDrag}" ondragstart="drag(event)">`;
        }
        return `<${tag.name}${attrString}">`;
      } else {
        return `</${tag.name}>`;
      }
    },
    popoverPartContent: function (val) {
      return joinArray(val, " / ", 35);
    },
    getCaretCharacterOffsetWithin(element) {
      let caretOffset = 0;
      const doc = element.ownerDocument || element.document;
      const win = doc.defaultView || doc.parentWindow;
      let sel;
      if (typeof win.getSelection !== "undefined") {
        sel = win.getSelection();
        if (sel.rangeCount > 0) {
          const range = win.getSelection().getRangeAt(0);
          const preCaretRange = range.cloneRange();
          preCaretRange.selectNodeContents(element);
          preCaretRange.setEnd(range.endContainer, range.endOffset);
          caretOffset = this.htmlUnEscape(preCaretRange.toString()).length;
        }
      } else if ((sel = doc.selection) && sel.type !== "Control") {
        const textRange = sel.createRange();
        const preCaretTextRange = doc.body.createTextRange();
        preCaretTextRange.moveToElementText(element);
        preCaretTextRange.setEndPoint("EndToEnd", textRange);
        caretOffset = this.htmlUnEscape(preCaretTextRange.text).length;
      }
      return caretOffset;
    },
    // 定义一个转换html还原特殊字符的方法
    htmlUnEscape(str) {
      const div = document.createElement("div");
      div.innerHTML = this.replaceSpecialChar(str);
      return div.innerText;
    },
    replaceSpecialChar(str) {
      return str.replaceAll("<", "&lt;").replaceAll(">", "&gt;");
    }
  }
};
</script>
<style>
.hover-pre {
  background-color: lightgray;
}
</style>

<style lang="scss" scoped>
h1,
h2,
h3,
th {
  user-select: none;
}

p {
  &::selection {
    background-color: #ccc;
  }
}
</style>
