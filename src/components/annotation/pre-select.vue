<template>
  <el-row style="margin-bottom: 3px">
    <el-col :span="24">
      <div
        v-if="entityContent && currAttrId && activeTab === 'attribute'"
        style="
          padding: 6px 10px 6px;
          margin-bottom: 5px;
          border-radius: 5px;
          background-color: rgba(248, 205, 142, 0.49);
        "
      >
        <div>
          <el-descriptions class="margin-top" :column="2" border>
            <el-descriptions-item
              label-class-name="my-attr-label"
              content-class-name="my-entity-content"
            >
              <template slot="label">
                <i class="el-icon-collection"></i>
                实体名称
              </template>
              <span v-text="entityContent" :title="entityContent"></span>
            </el-descriptions-item>

            <el-descriptions-item
              label-class-name="my-attr-label"
              content-class-name="my-attr-content"
            >
              <template slot="label">
                <i class="el-icon-postcard"></i>
                标注中属性
              </template>
              <span v-text="currAttrTitle"></span>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-col>
    <el-col :span="15">
      <el-radio-group v-model="sourceType" @change="changeValue" size="mini">
        <template>
          <el-radio-button
            v-for="(v, k) in sourceList"
            :key="`source-${k}`"
            :label="v.title"
            :task_id="v.taskId"
            :disabled="!showPreSelect && v.value !== 1"
          >
            <i
              v-if="v.master === 1"
              class="el-icon-s-flag"
              :title="v.invalid === 1 ? '原文蓝本 (废弃)' : '原文蓝本'"
              style="color: #f56c6c; padding: 0 2px"
            ></i>
            <span
              v-text="v.invalid === 1 ? `${v.title} (废弃)` : `${v.title}`"
            ></span>
          </el-radio-button>
        </template>
      </el-radio-group>
      <el-button
        v-if="
          isOriginal &&
          showImportBtn &&
          isAuth('task:detail:import') &&
          roleId === this.$RoleEnum.auditor
        "
        @click="setMaster"
        type="primary"
        icon="el-icon-circle-check"
        size="mini"
        class="ml-3"
        round
        >选为蓝本
      </el-button>
    </el-col>
    <el-col :span="9" style="text-align: right; padding-right: 8px">
      <div class="left-btns">
        <div v-if="showImportBtn && isAuth('task:detail:import')">
          <el-button
            @click="selectedImportToOriginal(null)"
            type="primary"
            icon="el-icon-s-promotion"
            size="mini"
            :disabled="!hasSelectTag"
            round
            plain
            >导入原文
          </el-button>
          <el-button
            @click="importToOriginal"
            icon="el-icon-copy-document"
            type="primary"
            size="mini"
            round
            plain
            >全部导入原文
          </el-button>
        </div>
        <div
          class="his-cb"
          v-if="
            roleId === this.$RoleEnum.annotator &&
            navbar.taskStatus === '已合格' &&
            currSourceTypeId === 1
          "
        >
          <el-checkbox @change="showAnnoOnlyFunc" v-model="showAnnoOnly"
            >标注员标注结果
          </el-checkbox>
        </div>
        <!--<div style="padding: 5px 6px">
          字数:
        </div>-->
      </div>
    </el-col>
  </el-row>
</template>

<script>
import { AnnoEventsBus } from "@/utils/bus";
import { sleep, trimStr } from "@/utils";

let preLoadingInstance = null;
export default {
  name: "PreSelect",
  data() {
    return {
      sourceType: "",
      preLoading: false,
      currSourceTypeId: 1,
      currImportLogId: null,
      masterTaskId: 0,
      currSourceData: {},
      showAnnoOnly: false,
      entityContent: "",
      wordsCount: ""
    };
  },
  props: {
    sourceList: {
      type: Array,
      required: true
    },
    showPreSelect: {
      type: Boolean,
      required: false,
      default: true
    },
    roleId: {
      required: true
    },
    hasSelectTag: {
      type: Boolean,
      required: false,
      default: false
    },
    currAnnoUniId: {
      type: String,
      required: false
    },
    annotatorId: {
      required: true
    }
  },
  created() {
    // 获取数据改变前的滚动条位置
    AnnoEventsBus.$on("refreshRequest", () => {
      this.changeValue();
    });
  },
  computed: {
    activeTab() {
      return this.$store.state.anno.activeTab;
    },
    currAttrId() {
      return trimStr(this.$store.state.attr.currAttrId);
    },
    currAttrTitle() {
      return this.$store.state.attr.currAttrTitle;
    },
    currAnnoId() {
      return this.$store.state.attr.currAnnoId;
    },
    navbar() {
      return this.$store.state.anno.navbar;
    },
    noteId() {
      return this.$store.state.anno.annotation.noteId;
    },
    taskId() {
      return this.$store.state.anno.annotation.taskId;
    },
    editable() {
      return this.$store.state.anno.editable;
    },
    editableByRoleAndStep() {
      return this.$store.state.anno.editableByRoleAndStep;
    },
    isOriginal() {
      return this.currSourceTypeId + "" === "1";
    },
    showImportBtn() {
      if (this.navbar.taskStatus === "已合格") {
        return false;
      }
      //判断是否显示多人标注导入按钮
      if (this.masterTaskId) {
        //多人标注
        return !(
          this.masterTaskId === this.currSourceData.taskId &&
          this.currSourceData.master === 1
        );
      } else {
        // 预标注
        return this.currSourceTypeId + "" !== "1";
      }
    }
  },
  mounted() {
    this.entityContent = "";
    this.initSource();
    AnnoEventsBus.$on("entityClick4Attr", (content) => {
      this.entityContent = content;
    });

    AnnoEventsBus.$on("keyPressImportToOriginal", (importUid) => {
      this.keyPressImportToOriginal(importUid);
    });
    // this.statArticleWordNum();
  },
  watch: {
    preLoading: {
      handler(newValue, oldValue) {
        if (newValue) {
          preLoadingInstance = this.$loading({
            text: "Program running...",
            spinner: "el-icon-loading",
            background: "rgba(204,204,204,0.82)"
          });
        } else if (preLoadingInstance != null) {
          preLoadingInstance.close();
        }
      },
      deep: false,
      immediate: true
    },
    sourceList: {
      handler(newValue, oldValue) {
        // 监听选项卡数据值变化
        this.initSource();
      },
      deep: true,
      immediate: false
    },
    showImportBtn: {
      handler(newValue, oldValue) {
        this.$store.commit("anno/setShowImportBtn", newValue);
      },
      deep: true,
      immediate: true
    },
    sourceType: {
      handler(newValue, oldValue) {
        this.showAnnoOnly = false;
      },
      deep: false,
      immediate: true
    },
    activeTab: {
      handler(newValue, oldValue) {
        // console.log("activeTab_change", newValue);
        this.cleanAttrSelect();
      },
      deep: false,
      immediate: true
    },
    currAnnoId: {
      handler(newValue, oldValue) {
        // console.log("currAnnoId_change", newValue);
        this.entityContent = "";
        this.cleanAttrSelect();
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    async statArticleWordNum() {
      // 获取当前文书字数
      this.wordsCount = "";
      let hasCountTimes = 0;
      for (let i = 0; i < 100; i++) {
        await sleep(2500);
        this.$nextTick(() => {
          let articleDom = document.querySelector(
            `article[id="articleContainer"]`
          );
          if (articleDom) {
            let contentLength = trimStr(articleDom.textContent).length;
            this.wordsCount = contentLength > 0 ? contentLength : "";
          }
        });

        if (this.wordsCount === 0 || (this.wordsCount && this.wordsCount > 0)) {
          hasCountTimes++;
        }
        if (hasCountTimes > 5) {
          break;
        }
      }
    },
    cleanAttrSelect() {
      return this.$store.dispatch("attr/setCurrAttr", null);
    },
    initSource() {
      const sourceList = this.sourceList;
      let masterTaskId = 0;
      if (sourceList && sourceList.length > 0) {
        const length = sourceList.length;
        for (let i = 0; i < length; i++) {
          const item = sourceList[i];
          if (i === 0) {
            this.sourceType = item.title;
            this.setCurrSelected(item);
          }
          if (item.master === 1) {
            masterTaskId = item.taskId;
            break;
          }
        }
      }
      this.masterTaskId = masterTaskId;
      this.$store.commit("anno/setMasterTaskId", masterTaskId);
      this.$store.commit("anno/setShowImportBtn", this.showImportBtn);
      this.$nextTick(() => {
        this.changeEditable();
      });
    },
    changeValue() {
      this.$store.commit("anno/setAnnotate", null);
      this.$store.commit("anno/setAnno", null);
      const sourceList = this.sourceList;
      if (sourceList) {
        const length = sourceList.length;
        if (length > 0) {
          let sourceTypeId;
          for (let i = 0; i < length; i++) {
            const item = sourceList[i];
            if (this.sourceType === item.title) {
              sourceTypeId = item.value;
              this.currSourceData = item;
              this.setCurrSelected(item);
              break;
            }
          }
          if (sourceTypeId) {
            AnnoEventsBus.$emit("changeSource");
            AnnoEventsBus.$emit("emptyLabel");
            AnnoEventsBus.$emit("hied-popover");
            AnnoEventsBus.$emit("clearAttrData");
            const all = document.querySelectorAll("span.select");
            for (let i = 0; i < all.length; i++) {
              all[i].classList.remove("select");
            }
          }
        } else {
          this.setCurrSelected();
        }
      }
    },
    setCurrSelected(item) {
      let sourceTypeId = 1;
      let taskId = null;
      let importLogId = null;
      if (item) {
        sourceTypeId = item.value;
        taskId = item.taskId;
        importLogId = item.importLogId;
        this.currSourceData = item;
      } else {
        this.currSourceData = {};
      }
      this.currSourceTypeId = sourceTypeId;
      this.currImportLogId = importLogId;
      if (taskId) {
        // 只有多人标注时taskId才存在
        this.$store.commit("anno/setNoteId", {
          noteId: this.noteId,
          taskId: taskId
        });
        AnnoEventsBus.$emit("taskIdChanged");
      }
      this.$store.commit("anno/setSource", {
        id: sourceTypeId,
        importLogId: importLogId
      });
      // 只能编辑蓝本原文
      this.changeEditable();
    },
    changeEditable() {
      if (this.showImportBtn) {
        this.$store.commit("anno/setEditable", false);
      } else {
        this.$store.commit("anno/setEditable", this.editableByRoleAndStep);
      }
    },
    resetValue() {
      const sourceList = this.sourceList;
      if (sourceList) {
        const length = sourceList.length;
        if (length > 0) {
          let sourceTypeId, taskId;
          for (let i = 0; i < length; i++) {
            const item = sourceList[i];
            if (this.sourceType === item.title) {
              sourceTypeId = item.value;
              taskId = item.taskId;
              break;
            }
          }
          if (!taskId) {
            this.initSource();
          }
        }
      }
      this.changeValue();
    },
    keyPressImportToOriginal(importUid) {
      if (this.showImportBtn && this.hasSelectTag) {
        this.selectedImportToOriginal(importUid);
      }
    },
    selectedImportToOriginal(importUid) {
      /* if (!this.hasMarkPermission()) {
        return;
      } */
      importUid = trimStr(importUid);
      let currAnnoUniId = importUid ? importUid : this.currAnnoUniId;
      if (!this.hasSelectTag || !currAnnoUniId) {
        this.$message({
          message: "请先选中一个标注！",
          type: "error"
        });
        return;
      }
      this.preLoading = true;
      const param = {
        noteId: this.noteId,
        taskId: this.taskId,
        source: this.currSourceTypeId,
        importLogId: this.currImportLogId,
        roleId: this.roleId,
        currAnnoUniId: currAnnoUniId
      };
      if (this.isOriginal) {
        param.masterTaskId = this.masterTaskId;
      }
      this.$http({
        url: this.$http.adornUrl(`/note/selectedImportToOriginal`),
        method: "post",
        data: this.$http.adornData(param)
      })
        .then(({ data }) => {
          this.preLoading = false;
          if (data.code === 0) {
            this.$message({
              message: "导入完毕！",
              type: "success",
              duration: 1500
            });
            this.changeValue();
          }
        })
        .finally(() => {
          this.preLoading = false;
        });
    },
    importToOriginal() {
      /* if (!this.hasMarkPermission()) {
        return;
      } */
      this.$confirm("确认要执行一键导入到原文吗？", "警告", {
        confirmButtonText: "导入",
        cancelButtonText: "取消"
      }).then(() => {
        this.preLoading = true;
        const param = {
          noteId: this.noteId,
          taskId: this.taskId,
          source: this.currSourceTypeId,
          importLogId: this.currImportLogId,
          roleId: this.roleId
        };
        if (this.isOriginal) {
          param.masterTaskId = this.masterTaskId;
        }
        this.$http({
          url: this.$http.adornUrl(`/note/preImportToEntity`),
          method: "post",
          data: this.$http.adornData(param)
        })
          .then(({ data }) => {
            this.preLoading = false;
            if (data.code === 0) {
              this.$message({
                message: "导入完毕！",
                type: "success",
                duration: 1500
              });
              this.changeValue();
            }
          })
          .finally(() => {
            this.preLoading = false;
          });
      });
    },
    // 检查标注权限
    hasMarkPermission() {
      if (!this.editable) {
        this.$message({
          message: "当前状态无编辑权限！",
          type: "error"
        });
        return false;
      }
      return true;
    },
    setMaster() {
      this.$confirm("确定要将当前标注员文书设置为蓝本吗？", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消"
      }).then(() => {
        this.preLoading = true;
        const annotatorId = trimStr(this.$route.query.annotatorId);
        const param = {
          taskId: this.currSourceData.taskId,
          roleId: this.roleId,
          annotatorId: annotatorId
        };
        this.$http({
          url: this.$http.adornUrl(`/note/setMaster`),
          method: "post",
          data: this.$http.adornData(param)
        })
          .then(({ data }) => {
            this.preLoading = false;
            if (data.code === 0) {
              this.$message({
                message: "设置完毕！",
                type: "success",
                duration: 1500
              });
              AnnoEventsBus.$emit("changeMaster", data.data);
            }
          })
          .finally(() => {
            this.preLoading = false;
          });
      });
    },
    showAnnoOnlyFunc(val) {
      if (
        this.roleId !== this.$RoleEnum.annotator ||
        this.navbar.taskStatus !== "已合格"
      ) {
        return;
      }

      let item = this.currSourceData;
      let annotatorId;
      if (item) {
        annotatorId = trimStr(item.userId);
      } else {
        annotatorId = trimStr(this.annotatorId);
      }
      if (annotatorId) {
        AnnoEventsBus.$emit("changeSource", val, annotatorId);
      }
    }
  },
  destroyed() {
    AnnoEventsBus.$off("refreshRequest");
    AnnoEventsBus.$off("entityClick4Attr");
    AnnoEventsBus.$off("keyPressImportToOriginal");
  }
};
</script>

<style scoped lang="scss">
.left-btns {
  display: flex;
  justify-content: flex-end;

  .his-cb {
    margin: 5px 5px 0;
  }
}

::v-deep .my-attr-label {
  width: 110px;
}

::v-deep .my-entity-content {
  max-width: 300px;
}

::v-deep .my-attr-content {
  color: #f56c6c;
  font-weight: bolder !important;
}
</style>
