<template lang="html">
  <section class="figure" :data-id="id">
    <img :src="src" />
    <base-section :data="caption" isSub :key="`section_figure_${id}`" />
  </section>
</template>

<script lang="js">
import BaseSection from "@/components/annotation/base-section";

export default {
  name: "Figure",
  data () {
    return {
      id: "",
      name: "",
      caption: "",
      src: ""
    };
  },
  props: [
    "data"
  ],
  mounted () {

  },
  components: {
    BaseSection
  },
  methods: {
    render: function () {
      const figure = this.data;
      if (!figure) return;

      this.$assert(figure.cls === "Figure");
      this.id = figure.id;
      this.name = figure.name;
      this.caption = figure.caption;
      if (!this.caption) {
        this.caption = {
          title: {
            content: `(${this.name})`,
            cls: "Title"
          },
          cls: "Section"
        };
      }
      this.src = figure.src;
    }
  },
  watch: {
    data: {
      handler () {
        this.render();
      },
      deep: true,
      immediate: true
    }
  }
};
</script>

<style lang="scss" scoped>
.figure {
  margin: 100px 0;

  img {
    max-width: 100%;
  }

  section {
    margin: 20px 0 0 0 !important;
  }
}
</style>
