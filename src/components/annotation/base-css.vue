<template>
  <span></span>
</template>

<script lang="js">
export default {
  name: "BaseCss",
  props: {
    data: {
      required: true
    },
    id: {
      required: true
    },
    type: {
      required: false
    }
  },
  mounted() {
    this.addStyle();
  },
  watch: {
    data: {
      handler() {
        this.addStyle();
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    addStyle() {
      const head = document.head;
      const style = document.createElement("style");
      style.innerText = this.data;
      style.id = "style-" + this.id;
      if (this.type) {
        style.setAttribute("annotype", this.type+"");
      }
      head.appendChild(style);
    }
  }
};
</script>

<style scoped lang="scss"></style>
