<template lang="html">
  <div v-loading="articleLoading">
    <article
      id="articleContainer"
      :style="{ height: fullHeight + 'px' }"
      class="article"
      ref="article"
      :data-id="article.id"
    >
      <!--标题-->
      <!--<h4 class="article-id">ID:{{ article.articleId }}</h4>-->
      <!--<h3 class="article-title">{{ article.title.content }}</h3>-->
      <!--摘要-->
      <base-section
        :change_times="change_times"
        :data="article.abstracts"
        hideTitle
        :key="`sec-title-${currentTaskId}-${source}`"
      />
      <!--期刊简介-->
      <!--      <div class="info-lists" v-if="article.info != null">
        <base-section
          :change_times="change_times"
          :data="i"
          v-for="(i, idx) in article.info"
          :key="`info-${idx}-${currentTaskId}-${source}`"
        />
      </div>-->
      <!--内容-->
      <!--      <hr v-if="article.abstracts" />-->
      <base-section
        :change_times="change_times"
        :data="sec"
        v-for="(sec, idx) in article.body"
        :cols="1"
        :key="`sec-${idx}-${currentTaskId}-${source}`"
      />
      <!--图片-->
      <!--      <hr v-if="article.body" />-->
      <base-figure
        :data="fig"
        v-for="(fig, idx) in article.figures"
        :key="`fig-${idx}-${currentTaskId}-${source}`"
      />
      <!--表格-->
      <!--      <hr v-if="article.figures" />-->
      <base-table
        :data="tab"
        v-for="(tab, idx) in article.tables"
        :key="`tab-${idx}-${currentTaskId}-${source}`"
      />
    </article>
  </div>
</template>

<script lang="js">
import BaseSection from "@/components/annotation/base-section";
import BaseFigure from "@/components/annotation/base-figure";
import BaseTable from "@/components/annotation/base-table";
import { AnnoEventsBus } from "@/utils/bus";
import { trimStr } from "@/utils";

export default {
  name: "BaseArticle",
  data() {
    return {
      articleLoading: true,
      tableH: undefined,
      articleHeight: 150,
      fullHeight: document.documentElement.clientHeight - this.articleHeight,
      change_times: 0,
      article: {
        id: "",
        title: "",
        abstracts: "",
        info: [],
        body: [],
        figures: [],
        tables: []
      },
      scrollTop: 0
    };
  },
  components: {
    BaseSection,
    BaseFigure,
    BaseTable
  },
  props: [
    "documentId"
  ],
  computed: {
    currNoteId() {
      return this.$store.state.anno.annotation.noteId ?  this.$store.state.anno.annotation.noteId: this.$route.query.noteId;
    },
    source() {
      return this.$store.state.anno.annotation.source;
    },
    importLogId() {
      return this.$store.state.anno.annotation.importLogId;
    },
    currentTaskId() {
      return this.$store.state.anno.annotation.taskId ? this.$store.state.anno.annotation.taskId: this.$route.query.taskId;
    },
    isHistoryMode() {
      return this.$store.state.anno.historyMode.enabled;
    }
  },
  created() {
    // 获取数据改变前的滚动条位置
    AnnoEventsBus.$on("getScrollTop", () => {
      if (this.$refs.article) {
        this.scrollTop = this.$refs.article.scrollTop;
      }
    });
    // 数据改变完成后设置滚动条的位置
    AnnoEventsBus.$on("setScrollTop", () => {
      this.$nextTick(() => {
        if (this.$refs.article) {
          this.$refs.article.scrollTop = this.scrollTop;
        }
      });
    });
    // 切换不同的预标注数据来源
    AnnoEventsBus.$on("changeSource", (showAnnoOnly, annotatorId) => {
      this.$nextTick(() => {
        this.getArticle(this.source, showAnnoOnly, annotatorId);
      });
    });
  },
  mounted() {
    AnnoEventsBus.$on("updateCurrDoc", (data, selectedAnnoId, selectedUniqueid) => {
      if (data === -1) {
        // 重新刷新标注信息（例如标注失败的时候）
        let articleBodyData = this.$_.clone(this.article.body);
        this.article.body = [];
        this.$nextTick(() => {
          this.article.body = articleBodyData;
        });
        return false;
      }
      this.article = data;
      if (selectedAnnoId) {
        this.$nextTick(() => {
          const domObj = document.querySelector(`span.tag[annoid="${selectedAnnoId}"]`);
          if (domObj) {
            domObj.click();
          }
        });
      } else if (selectedUniqueid) {
        this.$nextTick(() => {
          const domObj = document.querySelector(`span.tag[uniqueid="${selectedUniqueid}"]`);
          if (domObj) {
            domObj.click();
          }
        });
      }
    });
    AnnoEventsBus.$on("refreshCurrDoc", () => {
      this.$nextTick(() => {
        this.change_times = this.change_times + 1;
      });
    });
    this.getArticle();
    this.fullHeight = document.documentElement.clientHeight - this.articleHeight;
    window.onresize = () => {
      return (() => {
        this.fullHeight = document.documentElement.clientHeight - this.articleHeight;
      })();
    };
  },
  methods: {
    // 获取当前文书内容
    getArticle(source, showAnnoOnly, annotatorId) {
      showAnnoOnly = !!showAnnoOnly;
      annotatorId = trimStr(annotatorId);
      this.articleLoading = true;
      /* if (DEFAULT_SOURCE_TYPE === source || !source) {
        source = "BNLP";
      } */
      if (!source) {
        source = 1;
      }
      let currentTaskId = this.currentTaskId;
      if (currentTaskId === undefined) {
        currentTaskId = 0;
      }
      let currImportLogId = null;
      if (source + "" !== "1") {
        //预标注
        const masterTaskId = this.$store.state.anno.masterTaskId;
        if (masterTaskId) {
          currentTaskId = masterTaskId;
        }
        if (this.importLogId) {
          currImportLogId = this.importLogId;
        }
      }
      let getDocUrl = `/note/getDocument/${this.documentId}/${currentTaskId}/${source}?showAnnoOnly=${showAnnoOnly}&annotatorId=${annotatorId}`;
      if (currImportLogId) {
        getDocUrl = getDocUrl + `&currImportLogId=${currImportLogId}`;
      }
      // 如果是历史模式，添加discussionId参数
      if (this.isHistoryMode && this.$store.state.anno.historyMode.discussionId) {
        getDocUrl = getDocUrl + `&discussionId=${this.$store.state.anno.historyMode.discussionId}`;
      }
      this.$http({
        url: this.$http.adornUrl(getDocUrl),
        method: "get",
        params: this.$http.adornParams({
          noteId: this.currNoteId
        })
      }).then(({ data }) => {
        if (data.code === 0 && data.data) {
          const article = data.data;
          this.$assert(article.cls === "Article");
          this.article = article;
        } else {
          this.$message({
            message: "拉取文书失败，请联系管理员！",
            type: "error",
            duration: 1500
          });
        }
        this.articleLoading = false;
      });
    }
  },
  destroyed() {
    AnnoEventsBus.$off("getScrollTop");
    AnnoEventsBus.$off("setScrollTop");
    AnnoEventsBus.$off("changeSource");
    AnnoEventsBus.$off("updateCurrDoc");
    AnnoEventsBus.$off("refreshCurrDoc");
  }
};
</script>

<style scoped lang="scss">
.el-row {
  margin-top: 10px;
}

.article {
  overflow-y: scroll;
  padding: 0 10px 0 0;

  h1 {
    text-align: center;
    margin: 40px;
    font-size: 30px;
  }

  * {
    font-family: "Times New Roman", Times, serif;
  }
}
</style>

<style lang="scss">
*::selection {
  background-color: #ccc;
}

article a {
  color: #2fbd00;
  text-decoration: underline;

  &:hover {
    text-decoration: underline;
  }

  &:active {
    color: black;
  }

  &:visited {
    color: #2fbd00;
  }

  &::after {
    content: " ⤴";
    opacity: 0.7;
    border-radius: 4px;
  }
}

.article-id {
  color: #5a5a5a;
  margin: 0;
}

.article-title {
  text-align: center;
  margin: 0;
}

.info-lists {
  columns: 2;
  column-gap: 40px;
  padding: 40px;
  background-color: #eee;
  user-select: none;

  .section {
    margin: 0 !important;
    -webkit-column-break-inside: avoid;

    & + .section {
      margin-top: 20px !important;
    }

    h2 {
      font-size: 14px !important;
      margin: 10px 0 !important;
    }

    p {
      font-size: 16px !important;
      text-indent: 0 !important;
      line-height: 1.2 !important;
      // word-break: break-all !important;

      &::before {
        content: "-";
        margin-right: 10px;
      }
    }
  }
}

.section {
  .tag {
    user-select: none;
    padding: 2px 2px;
    //margin:  3px;
    border-radius: 4px;
    cursor: pointer;
    position: relative;
    text-indent: 0;
    z-index: 0;
    transition: background-color 0.3s ease-in-out;

    &:hover {
      //background-color: #ddd;
    }

    &::after {
      margin-left: 5px;
      font-size: 12px;
      font-family: monospace;
      font-weight: bold;
      z-index: 1;

      opacity: 1;
      transition: opacity 0.3s ease-in-out;

      display: inline-block;
      vertical-align: middle;
      line-height: normal;
      transform: translateY(-1px);
    }
  }
}

.section-body {
  p + p {
    margin-top: 8px;
    text-indent: 26px;
  }
}
</style>
