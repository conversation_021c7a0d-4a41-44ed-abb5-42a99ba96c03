<template lang="html">
  <section class="section" :data-id="id">
    <base-text
      v-if="dialogVisible && !hideTitle"
      :data="title"
      :tag="`h${isSub ? 3 : 2}`"
    />
    <div class="section-body" :style="{ columnCount: cols }">
      <template v-for="(item, idx) in items">
        <base-text
          :data="item"
          tag="p"
          v-if="dialogVisible && item.cls === 'Paragraph'"
          :key="`text_paragraph_${idx}_${item.id}`"
        />
        <base-section
          :data="item"
          :isSub="true"
          v-if="dialogVisible && item.cls === 'Section'"
          :key="`section_${idx}_${item.id}`"
        />
      </template>
    </div>
  </section>
</template>

<script lang="js">
import BaseSection from "@/components/annotation/base-section";
import BaseText from "@/components/annotation/base-text";

export default {
  name: "BaseSection",
  data() {
    return {
      id: "",
      title: "",
      items: "",
      isAbstract: false
    };
  },
  props: {
    data: {
      required: true
    },
    isSub: {
      type: Boolean,
      default: false
    },
    cols: {
      type: Number,
      default: 1
    },
    hideTitle: {
      type: Boolean,
      default: false
    },
    change_times: {
      type: Number
    }
  },
  components: {
    BaseSection,
    BaseText
  },
  created() {
  },
  methods: {
    //切换主题
    render: function() {
      const section = this.data;
      if (!section) return;
      this.$assert(section.cls === "Paragraph" || section.cls === "Section");

      this.title = section.title;
      this.items = section.items;
      if (this.title && this.title.content) {
        this.isAbstract = this.title.content === "Abstract";
      }
      this.id = section.id;
    }
  },
  watch: {
    data: {
      handler() {
        this.render();
      },
      deep: true,
      immediate: true
    },
    change_times: {
      handler() {
        this.render();
      }
    }
  },
  computed: {
    dialogVisible: function () {
      return this.$store.state.anno.dialogVisible;
    }
  }
};
</script>

<style scoped lang="scss">
.section {
  //margin: 50px 0;

  & > .section-body {
    column-gap: 40px;
    padding-top: 5px;
  }

  h2 {
    font-size: 19px;
    //margin: 30px 0;
    margin: 0;
  }

  h3 {
    font-size: 16px;
    margin: 0;
  }

  p {
    margin-top: -10px;
    margin-bottom: 12px;
    font-size: 16px;
    line-height: 2;
    text-align: justify;

    // FIXME: Why this not working?
    word-break: break-word;
    -ms-hyphens: auto;
    -webkit-hyphens: auto;
    hyphens: auto;
  }
}
</style>
