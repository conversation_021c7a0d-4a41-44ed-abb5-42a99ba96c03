<template>
  <div>
    <div @click="changeFull">
      <icon-svg
        :name="isFullscreen ? 'unfull' : 'screenfull'"
        class="full-screen"
      />
    </div>
  </div>
</template>

<script>
import screenfull from "screenfull";

export default {
  name: "ScreenFull",
  data() {
    return {
      isFullscreen: false
    };
  },
  mounted() {
    this.init();
  },
  beforeDestroy() {
    this.destroy();
  },
  computed: {
    sidebarFold: {
      get() {
        return this.$store.state.common.sidebarFold;
      },
      set(val) {
        this.$store.commit("common/updateSidebarFold", val);
      }
    }
  },
  methods: {
    changeFull() {
      if (!screenfull.enabled) {
        this.$message({
          message: "you browser can not work",
          type: "warning"
        });
        return false;
      }
      screenfull.toggle();
      this.sidebarFold = !this.sidebarFold;
    },
    change() {
      this.isFullscreen = screenfull.isFullscreen;
    },
    init() {
      if (screenfull.enabled) {
        screenfull.on("change", this.change);
      }
    },
    destroy() {
      if (screenfull.enabled) {
        screenfull.off("change", this.change);
      }
    }
  }
};
</script>

<style scoped>
.full-screen {
  margin-top: 5px;
  width: 18px;
  height: 18px;
}
</style>
