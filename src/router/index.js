/**
 * 全站路由配置
 *
 * 建议:
 * 1. 代码中路由统一使用name属性跳转(不使用path属性)
 */
import Vue from "vue";
import Router from "vue-router";
import http from "@/utils/httpRequest";
import { isURL } from "@/utils/validate";
import { clearLoginInfo } from "@/utils";

Vue.use(Router);

// 开发环境不使用懒加载, 因为懒加载页面太多的话会造成webpack热更新太慢, 所以只有生产环境使用懒加载
const _import = require("./import-" + process.env.NODE_ENV);

// 全局路由(无需嵌套上左右整体布局)
const globalRoutes = [
  {
    path: "/404",
    component: _import("common/404"),
    name: "404",
    meta: { title: "404未找到" }
  },
  {
    path: "/login",
    component: _import("common/login"),
    name: "login",
    meta: { title: "登录" }
  }
];

// 主入口路由(需嵌套上左右整体布局)
const mainRoutes = {
  path: "/",
  component: _import("main"),
  name: "main",
  redirect: { name: "home" },
  meta: { title: "主入口整体布局" },
  children: [
    // 通过meta对象设置路由展示方式
    // 1. isTab: 是否通过tab展示内容, true: 是, false: 否
    // 2. iframeUrl: 是否通过iframe嵌套展示内容, '以http[s]://开头': 是, '': 否
    // 提示: 如需要通过iframe嵌套展示内容, 但不通过tab打开, 请自行创建组件使用iframe处理!
    {
      path: "/home",
      component: _import("common/home"),
      name: "home",
      meta: { title: "首页" }
    },
    {
      path: "/theme",
      component: _import("common/theme"),
      name: "theme",
      meta: { title: "主题" }
    },
    {
      path: "/project/statistics/:projectId",
      props: true,
      component: _import("modules/project/statistics"),
      name: "statistics",
      meta: { title: "项目进度" }
    },
    {
      path: "/project/batch/:projectId",
      props: true,
      component: _import("modules/project/batch/list"),
      name: "batch",
      meta: { title: "批次列表" }
    },
    {
      path: "/project/verify/:projectId",
      props: true,
      component: _import("modules/project/verify/list"),
      name: "verify",
      meta: { title: "数据验证" }
    },
    {
      path: "/batch/article/:batchId",
      props: true,
      component: _import("modules/project/batch/article-list"),
      name: "article-list",
      meta: { title: "查看文书列表" }
    },
    {
      path: "/batch/statistics/:projectId/:batchId",
      props: true,
      component: _import("modules/project/batch/batch-statistics"),
      name: "BatchStatistics",
      meta: { title: "批次进度" }
    },
    {
      path: "/project/private-entity",
      component: _import("modules/labels/private-entity"),
      name: "private-entity",
      meta: { title: "私有标签" }
    },
    {
      path: "/project/private-relation",
      component: _import("modules/labels/private-relation"),
      name: "private-relation",
      meta: { title: "私有标签" }
    },
    {
      path: "/project/private-pattern",
      component: _import("modules/labels/private-pattern")
    },
    {
      path: "/project/private-umls",
      component: _import("modules/labels/private-umls")
    },
    {
      path: "/project/private-entity/entity-list",
      component: _import("modules/labels/entity-label/entity-list")
    },
    {
      path: "/project/private-pattern/relation-list",
      component: _import("modules/labels/relation-pattern/relation-list")
    },
    {
      path: "/labels/relation/schema",
      component: _import("modules/labels/relation-pattern/relation-schema")
    },
    {
      path: "/annotask/detail",
      component: _import("modules/annotask/anno-detail"),
      name: "annotask-detail",
      meta: { title: "批注详情", hidden: true }
    },
    {
      path: "/tool/ai-pre-anno/add",
      component: _import("modules/tool/add"),
      name: "ai-pre-anno-add",
      meta: { title: "添加AI预标注任务" }
    }
  ],
  beforeEnter(to, from, next) {
    const token = Vue.cookie.get("bnlp-token");
    if (!token || !/\S/.test(token)) {
      clearLoginInfo();
      next({ name: "login" });
    }
    next();
  }
};

const router = new Router({
  // mode: 'hash', // 如果使用hash模式，需要删除base和vue.config.js的publicPath和outputDir配置，还需要修改Nginx
  base: `/${process.env.VUE_APP_BASE_URL}/`,
  mode: "history",
  scrollBehavior: () => ({ y: 0 }),
  isAddDynamicMenuRoutes: false, // 是否已经添加动态(菜单)路由
  routes: globalRoutes.concat(mainRoutes)
});

router.beforeEach((to, from, next) => {
  // 添加动态(菜单)路由
  // 1. 已经添加 or 全局路由, 直接访问
  // 2. 获取菜单列表, 添加并保存本地存储
  if (
    router.options.isAddDynamicMenuRoutes ||
    fnCurrentRouteType(to, globalRoutes) === "global"
  ) {
    next();
  } else {
    http({
      url: http.adornUrl("/sys/menu/nav"),
      method: "get",
      params: http.adornParams({
        roleId: sessionStorage.getItem("roleId")
      })
    })
      .then(({ data }) => {
        if (data && data.code === 0) {
          fnAddDynamicMenuRoutes(data.menuList);
          router.options.isAddDynamicMenuRoutes = true;
          // 权限管理和菜单渲染
          sessionStorage.setItem(
            "menuList",
            JSON.stringify(data.menuList || "[]")
          );
          sessionStorage.setItem(
            "permissions",
            JSON.stringify(data.permissions || "[]")
          );
          next({ ...to, replace: true });
        } else {
          sessionStorage.setItem("menuList", "[]");
          sessionStorage.setItem("permissions", "[]");
          next();
        }
      })
      .catch((e) => {
        console.log(
          `%c${e} 请求菜单列表和权限失败，跳转至登录页！！`,
          "color:blue"
        );
        router.push({ name: "login" });
      });
  }
});

/**
 * 判断当前路由类型, global: 全局路由, main: 主入口路由
 * @param {*} route 当前路由
 */
function fnCurrentRouteType(route, globalRoutes = []) {
  let temp = [];
  for (let i = 0; i < globalRoutes.length; i++) {
    if (route.path === globalRoutes[i].path) {
      return "global";
    } else if (
      globalRoutes[i].children &&
      globalRoutes[i].children.length >= 1
    ) {
      temp = temp.concat(globalRoutes[i].children);
    }
  }
  return temp.length >= 1 ? fnCurrentRouteType(route, temp) : "main";
}

/**
 * 添加动态(菜单)路由
 * @param {*} menuList 菜单列表
 * @param {*} routes 递归创建的动态(菜单)路由
 */
function fnAddDynamicMenuRoutes(menuList = [], routes = []) {
  let temp = [];
  for (let i = 0; i < menuList.length; i++) {
    if (menuList[i].list && menuList[i].list.length >= 1) {
      temp = temp.concat(menuList[i].list);
    } else if (menuList[i].url && /\S/.test(menuList[i].url)) {
      menuList[i].url = menuList[i].url.replace(/^\//, "");
      const route = {
        path: menuList[i].url.replace("/", "-"),
        component: null,
        name: menuList[i].url.replace("/", "-"),
        meta: {
          menuId: menuList[i].menuId,
          title: menuList[i].name,
          isDynamic: true,
          isTab: true,
          iframeUrl: ""
        }
      };
      // url以http[s]://开头, 通过iframe展示
      if (isURL(menuList[i].url)) {
        route.path = `i-${menuList[i].menuId}`;
        route.name = `i-${menuList[i].menuId}`;
        route.meta.iframeUrl = menuList[i].url;
      } else {
        try {
          route.component = _import(`modules/${menuList[i].url}`) || null;
        } catch (e) {
          console.log(e);
        }
      }
      routes.push(route);
    }
  }
  if (temp.length >= 1) {
    fnAddDynamicMenuRoutes(temp, routes);
  } else {
    mainRoutes.name = "main-dynamic";
    mainRoutes.children = routes;
    router.addRoutes([mainRoutes, { path: "*", redirect: { name: "404" } }]);
    sessionStorage.setItem(
      "dynamicMenuRoutes",
      JSON.stringify(mainRoutes.children || "[]")
    );
    // console.log('\n')
    // console.log('%c!<-------------------- 动态(菜单)路由 s -------------------->', 'color:blue')
    // console.log(mainRoutes.children)
    // console.log('%c!<-------------------- 动态(菜单)路由 e -------------------->', 'color:blue')
  }
}

const originalPush = Router.prototype.push;
Router.prototype.push = function push(location, onResolve, onReject) {
  if (onResolve || onReject)
    return originalPush.call(this, location, onResolve, onReject);
  return originalPush.call(this, location).catch((err) => err);
};

export default router;
