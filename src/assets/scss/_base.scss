*,
*:before,
*:after {
  box-sizing: border-box;
}
body {
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB",
    "Microsoft YaHei", "微软雅黑", Aria<PERSON>, sans-serif;
  font-size: 14px;
  line-height: 1.15;
  color: #303133;
  background-color:#F0F3F4;
  //overflow: hidden;
}
a {
  color: mix(#fff, $--color-primary, 20%);
  text-decoration: none;
  &:focus,
  &:hover {
    color: $--color-primary;
    text-decoration: underline;
  }
}
img {
  vertical-align: middle;
}

i.pointer  {
  margin: auto 5px;
  cursor:pointer
}
.el-button--medium {
  padding: 10px!important;
}
.icon-red {
  margin: auto 5px;
  color: #f56c6c;
  cursor:pointer
}

.icon-primary {
  margin: auto 5px;
  color: #3474FE;
  cursor:pointer
}

.icon-success {
  margin: auto 5px;
  color: #17B3A3;
  cursor:pointer
}

.icon-warning {
  margin: auto 5px;
  color: #e6a23c;
  cursor:pointer
}

.icon-gray {
  margin: auto 5px;
  color: #909399;
  cursor:pointer
}
/* Utils
------------------------------ */
.clearfix:before,
.clearfix:after {
  content: " ";
  display: table;
}
.clearfix:after {
  clear: both;
}

/* Animation
------------------------------ */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}

/* Reset element-ui
------------------------------ */
.site-wrapper {
  .el-pagination {
    margin-top: 15px;
    text-align: right;
  }
}

/* Layout
------------------------------ */
.site-wrapper {
  position: relative;
  min-width: 1180px;
}

/* Sidebar fold
------------------------------ */
//.site-sidebar--fold {
//  aside {
//    .el-menu-item{
//      text-align: center;
//      padding: 0!important;
//    }
//  }
//}

.site-sidebar--fold aside .el-menu-item{
      text-align: center;
      padding: 0!important;
}

.nav-slider > div:first-child{
  padding: 0!important;
  text-align: center;
  line-height: 43px;
}
.site-sidebar--fold {
  .site-navbar__header,
  .site-navbar__brand,
  .site-sidebar,
  .site-sidebar__inner,
  .el-menu.site-sidebar__menu {
    width: 64px;
  }
  .site-navbar__body,
  .site-content__wrapper {
    margin-left: 64px;
  }
  .site-navbar__brand {
    &-lg {
      display: none;
    }
    &-mini {
      display: inline-block;
    }
  }
  .site-sidebar,
  .site-sidebar__inner {
    overflow: initial;
  }
  .site-sidebar__menu-icon {
    margin-right: 0;
    font-size: 20px;
  }
  .site-content--tabs > .el-tabs > .el-tabs__header {
    left: 64px;
  }
}
.icon-inner {
  margin: 0 5px -1px -5px;
}

// animation
.site-navbar__header,
.site-navbar__brand,
.site-navbar__body,
.site-sidebar,
.site-sidebar__inner,
.site-sidebar__menu.el-menu,
.site-sidebar__menu-icon,
.site-content__wrapper,
.site-content--tabs > .el-tabs .el-tabs__header {
  transition: inline-block 0.3s, left 0.3s, width 0.3s, margin-left 0.3s,
    font-size 0.3s;
}

/* Navbar
------------------------------ */
//超级管理员去除下划线
.el-menu--horizontal>.el-menu-item.is-active{
  border-bottom: none;
}
.site-navbar {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1030;
  height: 50px;
  //box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  background-color: #FFFFFF;

  &--inverse {
    .site-navbar__body {
      background-color: transparent;
    }
    .el-menu {
      > .el-menu-item,
      > .el-submenu > .el-submenu__title {
        color: #fff;
        &:focus,
        &:hover {
          color: #fff;
          background-color: mix(#000, $navbar--background-color, 15%);
        }
      }
      > .el-menu-item.is-active,
      > .el-submenu.is-active > .el-submenu__title {
        border-bottom-color: mix(#fff, $navbar--background-color, 85%);
      }
      .el-menu-item i,
      .el-submenu__title i,
      .el-dropdown {
        color: #fff;
      }
    }
    .el-menu--popup-bottom-start {
      background-color: $navbar--background-color;
    }
  }

  &__header {
    position: relative;
    float: left;
    width: $navbar--width;
    height: 50px;
    overflow: hidden;
  }
  &__brand {
    align-items: center;
    justify-content: center;
    display: table-cell;
    vertical-align: middle;
    width: $navbar--width;
    height: 50px;
    margin: 0;
    font-size: 20px;
    text-align: center;
    text-transform: uppercase;
    white-space: nowrap;
    color: #fff;
    border-right: 1px solid #F0F1F6;

    &-lg,
    &-mini {
      margin: 0 5px;
      color: #0B1F3D;
      &:focus,
      &:hover {
        color: #0B1F3D;
        text-decoration: none;
      }
    }
    &-mini {
      display: none;
    }
  }
  &__switch {
    font-size: 18px;
    border-bottom: none !important;
  }
  &__avatar {
    border-bottom: none !important;
    * {
      vertical-align: inherit;
    }
    .el-dropdown-link {
      > img {
        width: 36px;
        height: auto;
        margin-right: 5px;
        border-radius: 100%;
        vertical-align: middle;
      }
    }
  }
  &__body {
    position: relative;
    margin-left: $navbar--width;
    padding-right: 15px;
    background-color: var(--bg-color);
  }
  &__menu {
    float: left;
    background-color: transparent;
    border-bottom: 0;

    &--right {
      float: right;
    }
    a:focus,
    a:hover {
      text-decoration: none;
    }
    .el-menu-item,
    .el-submenu > .el-submenu__title {
      height: 50px;
      //line-height: 50px;
      display: flex;
      align-items: center;
    }
    .el-submenu > .el-menu {
      top: 55px;
    }
    .el-badge {
      display: inline;
      z-index: 2;
      &__content {
        line-height: 16px;
      }
    }
  }
}

/* Sidebar
------------------------------ */
.site-sidebar {
  border-right: 1px solid #F0F1F6;
  padding-top: 20px;
  position: fixed;
  top: 50px;
  left: 0;
  bottom: 0;
  z-index: 1020;
  width: $navbar--width;
  overflow: hidden;

  &--dark,
  &--dark-popper {
    background-color: $sidebar--background-color-dark;
    .site-sidebar__menu.el-menu,
    > .el-menu--popup {
      background-color: $sidebar--background-color-dark;
      .el-menu-item,
      .el-submenu > .el-submenu__title {
        color: $sidebar--color-text-dark;
        &:focus,
        &:hover {
          color: mix(#fff, $sidebar--color-text-dark, 50%);
          background-color: mix(#fff, $sidebar--background-color-dark, 2.5%);
        }
      }
      .el-menu,
      .el-submenu.is-opened {
        background-color: mix(#000, $sidebar--background-color-dark, 15%);
      }
      .el-menu-item.is-active,
      .el-submenu.is-active > .el-submenu__title {
        color: mix(#fff, $sidebar--color-text-dark, 80%);
      }
    }
  }
  &__inner {
    position: relative;
    z-index: 1;
    width: 250px;
    height: 100%;
    padding-bottom: 15px;
    overflow-y: scroll;
  }
  &__menu.el-menu {
    width: $navbar--width;
    border-right: 0;
  }
  &__menu-icon {
    width: 24px;
    margin-right: 5px;
    text-align: center;
    font-size: 16px;
    color: inherit !important;
  }
}
.site-sidebar--dark .site-sidebar__menu.el-menu .el-menu-item,
.site-sidebar--dark .site-sidebar__menu.el-menu .el-submenu > .el-submenu__title,
.site-sidebar--dark .site-sidebar__menu.el-menu .el-menu-item,
.site-sidebar--dark-popper > .el-menu--popup .el-menu-item{
  margin: 0px 6px;
  color: #61677F;
  height: 44px;
  //line-height: 38px;
  display: flex;
  align-items: center;
  transition: none;
  &:hover{
    color: rgba(44, 109, 255);
    //background-color: rgba(44, 109, 255, 0.2) !important;
    border-radius: 6px;
    background-color: transparent;
  }
}
.site-sidebar__menu .el-menu-item.is-active,
.site-sidebar--dark-popper .el-menu-item.is-active{
  height: 44px;
  border-radius: 6px;
  margin: 0px 6px;
  background-color: #3474FE !important;
  color: #fff!important;
  border: 1px solid #F6F0F1;
}

.el-submenu.is-active > .el-submenu__title{
  color: #61677F!important;
}
.el-menu-item>span{
  display: inline-block;
  height: 44px;
  line-height: 46px;
}
//二级标签展开背景色

.el-submenu.is-opened,
.site-sidebar--dark .site-sidebar__menu.el-menu .el-menu,{
  background-color: #fff!important;
}

  /* Content
  ------------------------------ */
.site-content {
  position: relative;
  padding: 15px;

  &__wrapper {
    position: relative;
    padding-top: 50px;
    margin-left: $navbar--width;
    min-height: 100%;
    background: $content--background-color;
  }
  &--tabs {
    //padding: 55px 0 0;
  }
  > .el-tabs {
    > .el-tabs__header {
      position: fixed;
      top: 50px;
      left: $navbar--width;
      right: 0;
      z-index: 930;
      padding: 0 55px 0 15px;
      box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12), 0 0 6px 0 rgba(0, 0, 0, 0.04);
      background-color: #fff;
      > .el-tabs__nav-wrap {
        margin-bottom: 0;
        &:after {
          display: none;
        }
      }
    }
    > .el-tabs__content {
      padding: 0 15px 15px;
      > .site-tabs__tools {
        position: fixed;
        top: 50px;
        right: 0;
        z-index: 931;
        height: 40px;
        padding: 0 12px;
        font-size: 16px;
        line-height: 40px;
        background-color: $content--background-color;
        cursor: pointer;
        .el-icon--right {
          margin-left: 0;
        }
      }
    }
  }
}
.el-table__expand-icon {
  display: inline-block;
  width: 14px;
  vertical-align: middle;
  margin-right: 5px;
}
.el-card{
  border-radius: 10px !important;
}

.el-menu.el-menu--horizontal {
  border-bottom: none;
}

.title-pane {
  margin: 0 10px;
}

body {
  /* chrome & safari 浏览器 重置滚动条样式 */
  ::-webkit-scrollbar {
    width: 5px;
    height: 5px;
    /**/
  }
  ::-webkit-scrollbar-track {
    background: #e5e5e5;
    border-radius: 2px;
  }
  ::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 10px;
  }
  ::-webkit-scrollbar-thumb:hover {
    background: #969698;
  }
  ::-webkit-scrollbar-corner {
    background: #c1c1c1;
  }
  /*定义右下角汇合处的样式*/
  ::-webkit-scrollbar-corner {
    background: #dcdfe6;
  }
  /* IE 浏览器 重置滚动条样式 */

  scrollbar-face-color: #c1c1c1; /* 滚动条颜色 */
  scrollbar-shadow-color: #c1c1c1; /* 滚动条的边框 */
  scrollbar-track-color: #c1c1c1; /* 浏览器滚动条背景色 */
  scrollbar-arrow-color: #c1c1c1; /* 上下箭头颜色 */
}
