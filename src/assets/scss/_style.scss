label {
  font-weight: 600;
}

.d-flex {
  display: flex !important;
}

.d-space-between {
  justify-content: space-between !important;
}

.align-items-center {
  align-items: center !important;
}

.ml-2 {
  margin-left: 8px;
}

.ml-3 {
  margin-left: 12px;
}

.ml-4 {
  margin-left: 16px;
}

.m-0 {
  margin: 0;
}

.m-1 {
  margin: 4px;
}

.mt-0 {
  margin-top: 0 !important;
}

.mb-0 {
  margin-bottom: 0 !important;
}

.ml-1 {
  margin-left: 4px;
}

.ml-3 {
  margin-left: 12px;
}

.mr-1 {
  margin-right: 4px;
}

.mr-2 {
  margin-right: 8px;
}

.mb-1 {
  margin-bottom: 4px;
}

.mb-2 {
  margin-bottom: 8px;
}

.mb-3 {
  margin-bottom: 12px;
}

.mt-1 {
  margin-top: 4px;
}

.mt-2 {
  margin-top: 8px;
}

.mt-3 {
  margin-top: 12px;
}

.my-1 {
  margin-top: 4px;
  margin-bottom: 4px;
}

.ml-auto {
  margin-left: auto;
}

.pb-1 {
  padding-bottom: 4px;
}

.pb-2 {
  padding-bottom: 8px;
}

.el-table {
  border-radius: 4px;
  border-top: 1px solid #DEDEDE;
  border-left: 1px solid #DEDEDE;
  border-bottom: 1px solid #DEDEDE;
  width: 100%;
}

a:hover {
  text-decoration: none;
}

// 表头的背景色
th.el-table__cell {
  height: 44px;
  background: #F6F6F6 !important;
  padding: 0;
}

.el-upload__tip {
  color: #999999;
}

//  上传文件 下载模板按钮
.upload-btn {
  padding: 7px 8px;
  height: 28px;
  width: 76px;

  span {
    margin-left: 0 !important;
  }
}

.el-dialog__body {
  padding: 20px !important;
}

.download-btn {
  text-align: center;
  margin-right: 8px;
  display: inline-block;
  //height: 28px;
  //width: 77px;
  background-color: #3474FE;
  //color: #4dcd4d;
  color: #fff;
  border-radius: 4px;
  padding: 8px!important;
  font-size: 12px;
  margin-left: 8px;
  border: 1px solid;
  span {
   margin-left: 0!important;
  }
  //&:visited {
  //  color: #fff!important;
  //  text-decoration: none!important;
  //}
  //&:hover {
  //  color: #fff!important;
  //  text-decoration: none !important;
  //}
  //&:active {
  //  color: #fff!important;
  //  text-decoration: none !important;
  //}
  .ml-1 {
    margin-left: -4px;
  }
}

.el-icon-arrow-left {
  font-weight: 600;
}

.label-name {
  font-weight: normal;
  font-size: 15px;
  color: #333333;
}

.label-content {
  font-size: 15px;
  color: #666666;
  margin-left: 8px;
}

.mt-20 {
  margin-bottom: 20px;
}

.tab-pane {
  margin-bottom: 8px;

  .pane-upload {
    display: flex;
    align-items: center
  }
}

.label-name {
  font-weight: normal;
  font-size: 15px;
  color: #333333;
}

.label-content {
  font-size: 15px;
  color: #666666;
  margin-left: 8px;
}

.p-8 {
  padding: 8px;
}

::v-deep.el-popover__reference-wrapper {
  vertical-align: middle;
}

.operate {
  position: relative;
  //top: -14px;
  right: 10px;
}

.el-form-item--medium .el-form-item__label {
  font-weight: 600;
}

.el-form {
  .el-form-item {
    margin-bottom: 20px;
  }
}
.task-detail{
  .el-card__body {
    padding:10px 15px!important;
  }
}
