import Vue from "vue";
import router from "@/router";
import store from "@/store";

/**
 * 获取uuid
 */
export function getUUID() {
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (c) => {
    return (c === "x" ? (Math.random() * 16) | 0 : "r&0x3" | "0x8").toString(
      16
    );
  });
}

/**
 * 是否有权限
 * @param {*} key
 */
export function isAuth(key) {
  return (
    JSON.parse(sessionStorage.getItem("permissions") || "[]").indexOf(key) !==
      -1 || false
  );
}

/**
 * 树形数据转换
 * @param {*} data
 * @param {*} id
 * @param {*} pid
 */
export function treeDataTranslate(data, id = "id", pid = "parentId") {
  const res = [];
  const temp = {};
  for (let i = 0; i < data.length; i++) {
    temp[data[i][id]] = data[i];
  }
  for (let k = 0; k < data.length; k++) {
    if (temp[data[k][pid]] && data[k][id] !== data[k][pid]) {
      if (!temp[data[k][pid]].children) {
        temp[data[k][pid]].children = [];
      }
      if (!temp[data[k][pid]]._level) {
        temp[data[k][pid]]._level = 1;
      }
      data[k]._level = temp[data[k][pid]]._level + 1;
      temp[data[k][pid]].children.push(data[k]);
    } else {
      res.push(data[k]);
    }
  }
  return res;
}

/**
 * 清除登录信息
 */
export function clearLoginInfo() {
  Vue.cookie.delete("bnlp-token");
  store.commit("resetStore");
  sessionStorage.removeItem("roleId");
  sessionStorage.removeItem("workEnv");
  router.options.isAddDynamicMenuRoutes = false;
}

// 使用Promise实现js睡眠,配合async/await使用该函数
export function sleep(time) {
  if (!time || typeof time !== "number") {
    time = 200;
  }
  return new Promise((resolve) => setTimeout(resolve, time));
}

export function getCurrEvent(event) {
  return event || window.event || arguments.callee.caller.arguments[0];
}

export function trimStr(strVal) {
  if (strVal !== null && strVal !== undefined) {
    // const reg = new RegExp('(^[\\s\\t\\xa0\\u3000]+)|([\\u3000\\xa0\\s\\t]+\x24)', 'g')
    // const rtrimReg = /^[\s\uFEFF\xA0\u3000]+|[\s\uFEFF\xA0\u3000]+$/g;
    // return (strVal + "").replace(rtrimReg, "");
    return (strVal + "").trim();
  } else {
    return "";
  }
}

export function isBlank(strVal) {
  return trimStr(strVal) === "";
}

/**
 * 数组按照指定连接符拼接为字符串，超长部分使用...省略
 * @param arr
 * @param connector
 * @param maxLength
 * @returns {string|string}
 */
export function joinArray(arr = [], connector = "", maxLength = 0) {
  const valStr = arr.join(connector);
  if (maxLength && maxLength > 0) {
    return valStr.length > maxLength
      ? valStr.substring(0, maxLength) + "..."
      : valStr;
  } else {
    return valStr;
  }
}

/**
 * 判断标注是否为属性标注
 * @param is_attr
 * @returns {boolean}
 */
export function isAttrTag(is_attr) {
  return is_attr + "" === "1";
}
