/**
 * 复制内容。返回一个Promise对象，用于判断成功或者失败
 * @param strVal
 * @returns {Promise<void>|Promise<unknown>}
 */
export function copy(strVal) {
  // 判断navigator.clipboard是否兼容
  if (
    !navigator.clipboard ||
    !window.isSecureContext ||
    strVal === null ||
    strVal === undefined
  ) {
    // document.execCommand函数已废弃
    return new Promise((resolve, reject) => {
      let tempInput;
      if (strVal !== undefined && strVal !== null) {
        tempInput = document.createElement("input");
        tempInput.value = strVal + "";
        document.body.appendChild(tempInput);
        tempInput.focus();
        tempInput.select();
      }
      if (document.execCommand("copy")) {
        document.execCommand("copy");
        if (tempInput) {
          document.body.removeChild(tempInput);
        }
        resolve();
      } else {
        if (tempInput) {
          document.body.removeChild(tempInput);
        }
        reject(new Error("Copy failed"));
      }
    });
  } else {
    return navigator.clipboard.writeText(strVal + "").catch(() => {
      console.log("Copy failed");
    });
  }
}

export function getUUID() {
  const s = [];
  const hexDigits = "0123456789abcdef";
  for (let i = 0; i < 32; i++) {
    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
  }
  s[14] = "4";
  s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1);
  s[8] = s[13] = s[18] = s[23];
  return s.join("");
}

export function reworkFormat(value) {
  if (value === 0) {
    return "0";
  }
  if (!value) {
    return "-";
  }
  return value.toFixed(2) + "%";
}

export function processTime(value) {
  if (!value) {
    return "-";
  }
  if (value !== 0) {
    if (value > 60) {
      const min = Math.floor(value / 60);
      if (min > 60) {
        const hour = Math.floor(min / 60);
        return `${hour}小时${min % 60}分${value % 60}秒`;
      }
      return `${min}分 ${value % 60}秒`;
    }
    return `${value % 60}秒`;
  }
  return "-";
}

export function updateDateTime(row, column) {
  let datetime = row[column.property];
  if (datetime == null) {
    return;
  }
  // 将日期转换成毫秒值
  const postTimeSecond = Date.parse(datetime);
  // 获取上周的日期
  const lastWeek = new Date();
  lastWeek.setDate(lastWeek.getDate() - 7);
  // 获取上周的时间戳
  const lastWeekTimestamp = lastWeek.getTime();
  // 超过一周直接显示日期
  if (postTimeSecond < lastWeekTimestamp) {
    return datetime;
  }
  // 当前的时间
  const now = new Date();
  // 现在日历对象
  const nowC = new Date();
  nowC.setHours(0, 0, 0, 0);
  // 把昨天的日历转换成日期
  const yesterday = new Date(nowC.getTime());
  yesterday.setDate(nowC.getDate() - 1);
  // 把前天的日历转换成日期
  const beforeYesterday = new Date(nowC.getTime());
  beforeYesterday.setDate(nowC.getDate() - 2);

  // 此时毫秒值
  const nowSecond = now.getTime();
  // 昨天毫秒值
  const ySecondTemp = yesterday.getTime();
  // 前天毫秒值
  const bSecondTemp = beforeYesterday.getTime();
  // 将两个日期转换成毫秒值,之后得到秒差值
  const second = Math.floor((nowSecond - postTimeSecond) / 1000);
  const ySecond = Math.floor((nowSecond - ySecondTemp) / 1000);
  const bSecond = Math.floor((nowSecond - bSecondTemp) / 1000);
  // 调用毫秒差值判断方法
  if (second >= bSecond) {
    return Math.floor(second / 60 / 60 / 24) + "天前";
  } else if (second >= ySecond) {
    return "昨天";
  } else if (second >= 0) {
    if (second < 60) {
      return "刚刚";
    } else if (second / 60 < 30) {
      return Math.floor(second / 60) + "分钟前";
    } else if (second / 60 < 60) {
      return "半小时前";
    } else if (second / 60 / 60 < 24) {
      return Math.floor(second / 60 / 60) + "小时前";
    }
  }
  return datetime;
}

export function truncateText(text, maxLength) {
  let length = 0;
  let truncated = "";

  for (let i = 0; i < text.length; i++) {
    const c = text.charAt(i);
    if (c.match(/[a-zA-Z]/)) {
      // 如果是英文字母
      length += 1;
    } else if (c.match(/[\u4e00-\u9fff]/)) {
      // 如果是汉字
      length += 2;
    } else {
      // 其他字符
      length += 1;
    }

    if (length > maxLength) {
      // 如果字符串长度超过了限制
      truncated = text.slice(0, i) + "..."; // 将字符串超过部分用省略号替换
      break;
    }
  }

  return truncated || text; // 如果没有超过限制，则返回原字符串
}

export default {
  copy,
  updateDateTime,
  processTime,
  reworkFormat,
  getUUID,
  truncateText
};
