// 角色枚举对象
export const Role = {
  // 标注员
  annotator: 2,
  // 审核员
  auditor: 3,
  // 项目管理员
  projectAdmin: 8,
  // 项目观察员
  projectWatcher: 9,
  // 系统普通管理员
  admin: 10,
  // 系统管理员
  rootAdmin: 1
};

// 文书状态
export const Note = {
  // 未标注
  unmarked: 0,
  // 标注中
  noting: 1,
  // 已标注
  marked: 2,
  // 审核中
  reviewing: 3,
  // 已审核
  reviewed: 4,
  // 被打回
  repulse: 5,
  // 已修正
  corrected: 6
};

// 文书正常/废弃状态
export const Invalid = {
  // 废弃
  invalid: 1,
  // 正常
  normal: 0
};

// 预标注类型
export const PreSourceType = {
  // 原文
  SELF: 1,

  // 人工导入的预标注
  CUSTOMIZE: 2
};

// 数据导入的类型
export const ImportType = {
  // 自有文书导入系统
  article_bnlp: 1,

  // bfms文书导入系统
  article_bfms: 2,

  // 预标注实体导入
  pre_anno_entity: 3,

  // 预标注属性导入
  pre_anno_attr: 4,

  // 预标注关系导入
  pre_anno_relation: 5,

  // 数据验证实体导入
  verify_anno_entity: 6
};
