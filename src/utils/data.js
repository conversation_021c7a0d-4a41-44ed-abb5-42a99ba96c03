const data = {
  nodes: [
    {
      id: "M01",
      label: `1,1-bis(3"-indolyl)-1`,
      value: "MESH:C492909",
      comboId: "a"
    },
    {
      id: "P01",
      label: "PPARG1",
      value: "Uniprot:P37231",
      comboId: "a"
    },
    {
      id: "M02",
      label: `2,2-bis(5"-indolyl)-2`,
      value: "MESH:C354343",
      comboId: "b"
    },
    {
      id: "P02",
      label: "PPARG2",
      value: "Uniprot:P47235",
      comboId: "b"
    },
    {
      id: "E1",
      label: "Manganese Chloride",
      value: "MESH:C025340",
      comboId: "d"
    },
    {
      id: "E2",
      label: "TNF",
      value: "MESH:D053449",
      comboId: "d"
    },
    {
      id: "E3",
      label: "IFNG",
      value: "MESH:C573415",
      comboId: "d"
    },
    {
      id: "S1",
      label: "1,1,1-trichloroethan",
      value: "MESH:C024566"
    },
    {
      id: "S2",
      label: "GRIN1",
      value: "Uniprot:Q234232",
      comboId: "e"
    },
    {
      id: "S3",
      label: "GRIN2A",
      value: "Uniprot:Q12879",
      comboId: "e"
    },
    {
      id: "S7",
      label: "Node-7",
      value: "Hit-7"
    },
    {
      id: "S8",
      label: "Node-8",
      value: "Hit-8",
      comboId: "d"
    },
    {
      id: "S9",
      label: "Node-9",
      value: "Hit-9"
    },
    {
      id: "S10",
      label: "Node-10",
      value: "Hit-10"
    },
    {
      id: "S11",
      label: "Node-11",
      value: "Hit-11"
    },
    {
      id: "S12",
      label: "Node-12",
      value: "Hit-12"
    }
  ],
  edges: [
    {
      label: "decrease activity",
      source: "S1",
      target: "e",
      size: 1,
      labelCfg: {
        // 改线段标签文字
        autoRotate: true, // 随着拖拽自动旋转
        style: {
          stroke: "#fff",
          fill: "#FF0000",
          lineWidth: 5,
          fontSize: 10
        }
      },
      style: {
        // stroke:    '#5B8FF9', 修改线段颜色
        stroke: "gray",
        endArrow: true
      }
    },
    {
      label: "Brind to",
      source: "S2",
      target: "S3",
      size: 1,
      style: {
        stroke: "gray",
        endArrow: true
      }
    },
    {
      label: "S7->S8",
      source: "S7",
      target: "S8"
    },
    {
      label: "S9->S10",
      source: "S9",
      target: "S10"
    },
    {
      label: "S11->S12",
      source: "S11",
      target: "S12"
    },
    {
      source: "M01",
      target: "P01",
      label: "binds to",
      style: {
        endArrow: true
      }
    },
    {
      source: "M02",
      target: "P02",
      label: "increase activity",
      style: {
        endArrow: true
      }
    },
    {
      source: "a",
      target: "b",
      label: "result",
      size: 1,
      color: "#5B8FF9",
      labelCfg: {
        autoRotate: true,
        style: {
          stroke: "#fff",
          lineWidth: 5,
          fontSize: 10
        }
      },
      style: {
        stroke: "gray",
        endArrow: true
      }
    },
    {
      source: "c",
      target: "d",
      label: "decrease susceptibility",
      size: 1,
      labelCfg: {
        // 改线段标签文字
        autoRotate: true, // 随着拖拽自动旋转
        style: {
          stroke: "#fff",
          fill: "#FF0000",
          lineWidth: 5,
          fontSize: 10
        }
      },
      style: {
        // stroke:    '#5B8FF9', 修改线段颜色
        stroke: "gray",
        endArrow: true
      }
    }
  ],
  combos: [
    {
      id: "a",
      label: `1,1-bis(3"-indolyl)-1 [binds to] PPARG1`,
      parentId: "c",
      padding: 1
    },
    {
      id: "b",
      label: "自定义的实体名称",
      parentId: "c",
      padding: 1
    },
    {
      id: "c",
      label: "实体数：2",
      padding: 1
      // type:  'rect',
    },
    {
      label: "实体数：3",
      id: "d"
    },
    {
      label: "实体数：2",
      id: "e"
    }
  ]
};

export default data;
