// 上/下页切换数据缓存工具栏
import { trimStr } from "@/utils/index";

export const PAGE_STORAGE_KEY = "bnlp_page_storage_";
export const PAGE_STORAGE_TIME_KEY = "bnlp_page_create_time_";

// 上/下页切换数据缓存过期时间（单位毫秒）
const STORAGE_TIMEOUT = 10 * (1000 * 60 * 60);

function initStKey(pre, fromType) {
  return pre + trimStr(fromType);
}

export function getPageIds(fromType = "") {
  let idsStr = localStorage.getItem(initStKey(PAGE_STORAGE_KEY, fromType));
  let timeStr = localStorage.getItem(
    initStKey(PAGE_STORAGE_TIME_KEY, fromType)
  );
  if (idsStr) {
    if (timeStr) {
      if (new Date().getTime() - Number(timeStr) > STORAGE_TIMEOUT) {
        removePageIds(fromType);
        return null;
      }
    }
    return JSON.parse(idsStr);
  } else {
    return null;
  }
}

export function setPageIds(fromType = "", ids) {
  if (ids) {
    localStorage.setItem(
      initStKey(PAGE_STORAGE_KEY, fromType),
      JSON.stringify(ids)
    );

    localStorage.setItem(
      initStKey(PAGE_STORAGE_TIME_KEY, fromType),
      new Date().getTime() + ""
    );
  } else {
    removePageIds(fromType);
  }
}

export function removePageIds(fromType = "") {
  localStorage.removeItem(initStKey(PAGE_STORAGE_KEY, fromType));
  localStorage.removeItem(initStKey(PAGE_STORAGE_TIME_KEY, fromType));
}

export const FromTypeEnum = {
  entity_list: "entityList",
  article_list_of_batch: "articleListOfBatch",
  anno_task_list: "annoTaskList",
  relation_list: "relationList"
};
