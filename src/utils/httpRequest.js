import Vue from "vue";
import axios from "axios";
import router from "@/router";
import qs from "qs";
import merge from "lodash/merge";
import { clearLoginInfo } from "@/utils";
import { Message } from "element-ui";

const http = axios.create({
  timeout: 1000 * 30,
  withCredentials: true,
  headers: {
    "Content-Type": "application/json; charset=utf-8"
  }
});

/**
 * 请求拦截
 */
http.interceptors.request.use(
  (config) => {
    config.headers.token = Vue.cookie.get("bnlp-token"); // 请求头带上token
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

/**
 * 响应拦截
 */
http.interceptors.response.use(
  (response) => {
    if (response.data && response.data.code === 401) {
      // 401, token失效
      clearLoginInfo();
      router.push({ name: "login" });
      return response;
    }
    if (response.data && response.data.code === 500) {
      Message.error(response.data.msg);
    }
    return response;
  },
  (error) => {
    return Promise.reject(error);
  }
);

/**
 * 请求地址处理
 * @param {*} actionName action方法名称
 */
http.adornUrl = (actionName) => {
  return process.env.VUE_APP_BASE_API + actionName;
};

/**
 * get请求参数处理
 * @param {*} params 参数对象
 * @param {*} openDefultParams 是否开启默认参数?
 */
http.adornParams = (params = {}, openDefultParams = true) => {
  const defaults = {
    t: new Date().getTime()
  };
  return openDefultParams ? merge(defaults, params) : params;
};

/**
 * post请求数据处理
 * @param {*} data 数据对象
 * @param {*} openDefultdata 是否开启默认数据?
 * @param {*} contentType 数据格式
 *  json: 'application/json; charset=utf-8'
 *  form: 'application/x-www-form-urlencoded; charset=utf-8'
 */
http.adornData = (data = {}, openDefultdata = true, contentType = "json") => {
  const defaults = {
    t: new Date().getTime()
  };
  data = openDefultdata ? merge(defaults, data) : data;
  return contentType === "json" ? JSON.stringify(data) : qs.stringify(data);
};

export function downloadFile(url, filename, displayName) {
  http({
    url: http.adornUrl(url),
    method: "get",
    responseType: "blob",
    params: http.adornParams({
      filename: filename,
      displayName: displayName
    })
  })
    .then((resp) => {
      if (resp.headers["content-type"] !== "application/octet-stream") {
        throw "下载失败";
      }
      const url = window.URL.createObjectURL(new Blob([resp.data]));
      const link = document.createElement("a");
      const filename = decodeURI(resp.headers.filename);
      link.href = url;
      link.download = filename;
      link.click();
    })
    .catch((err) => {
      Message.error(err);
    });
}

export function downloadTemplate(filename, displayName) {
  downloadFile("/file/downloadTemplate", filename, displayName);
}
export default http;
