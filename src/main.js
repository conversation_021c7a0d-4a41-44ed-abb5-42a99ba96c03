import Vue from "vue";
import App from "@/App";
import router from "@/router"; // api: https://github.com/vuejs/vue-router
import store from "@/store"; // api: https://github.com/vuejs/vuex
import VueCookie from "vue-cookie"; // api: https://github.com/alfhen/vue-cookie
import "@/element-ui"; // api: https://github.com/ElemeFE/element
import "@/icons"; // api: http://www.iconfont.cn/
import "@/element-ui-theme";
import "@/assets/scss/index.scss";
import httpRequest, { downloadTemplate } from "@/utils/httpRequest"; // api: https://github.com/axios/axios
import tools, { reworkFormat } from "@/utils/tools"; // api: https://github.com/axios/axios
import { isAuth } from "@/utils";
import cloneDeep from "lodash/cloneDeep";
import assert from "assert";
import _ from "lodash";
import ElementTreeLine from "element-tree-line";
import "element-tree-line/dist/style.css";

import { ImportType, Invalid, Note, Role, PreSourceType } from "@/utils/enums";
import loadMore from "@/directive/select/loadMore";

Vue.use(VueCookie);

// 注册全局指令
Vue.directive('loadMore', loadMore);

Vue.config.productionTip = false;

Vue.component(ElementTreeLine.name, ElementTreeLine);
// 挂载全局
Vue.prototype.$http = httpRequest; // ajax请求方法
Vue.prototype.isAuth = isAuth; // 权限方法
Vue.prototype.$assert = assert;
Vue.prototype.$_ = _;
Vue.prototype.$tools = tools;
Vue.prototype.$downloadTemplate = downloadTemplate;

// 枚举
Vue.prototype.$RoleEnum = Role;
Vue.prototype.$NoteEnum = Note;
Vue.prototype.$InvalidEnum = Invalid;
Vue.prototype.$ImportType = ImportType;
Vue.prototype.$PreSourceType = PreSourceType;

// 全局过滤器
Vue.filter("processTime", (value) => {
  return tools.processTime(value);
});
Vue.filter("reworkFormat", (value) => {
  return tools.reworkFormat(value);
});

// 保存整站vuex本地储存初始状态
window.SITE_CONFIG.storeState = cloneDeep(store.state);

new Vue({
  router,
  store,
  render: (h) => h(App)
}).$mount("#app");
