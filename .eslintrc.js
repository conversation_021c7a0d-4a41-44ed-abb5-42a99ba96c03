module.exports = {
  root: true,
  env: {
    node: true
  },
  extends: [
    "plugin:vue/essential",
    "eslint:recommended",
    "plugin:prettier/recommended"
  ],
  parserOptions: {
    parser: "@babel/eslint-parser"
  },
  rules: {
    // 强制使用双引号，同时可以使用``模板
    quotes: ["error", "double", { allowTemplateLiterals: true }],
    // 必须以分号结尾
    semi: ["error", "always"],
    // 以windows换行符结尾
    "linebreak-style": ["error", "windows"],
    // 对象字面量项尾不能有逗号
    "comma-dangle": ["error", "never"],
    // 方法名和括号之间需要一个空格：关闭
    "space-before-function-paren": 0,
    // 允许异步等待:关闭
    "generator-star-spacing": "off",
    // allow debugger during development
    "no-console": process.env.NODE_ENV === "production" ? "warn" : "off",
    "no-debugger": process.env.NODE_ENV === "production" ? "warn" : "off",
    "no-unreachable": "error",
    "vue/multi-word-component-names": "off",
    "vue/no-unused-vars": "off",
    "no-unused-vars": "off",
    // 固定换行符
    // "prettier/prettier": [
    //   "error",
    //   {
    //     endOfLine: "crlf",
    //     trailingComma: "none",
    //     singleQuote: false,
    //     semi: true
    //   }
    // ],
    "vue/no-mutating-props": "warn",
    "vue/no-useless-template-attributes": "warn",
    "vue/no-side-effects-in-computed-properties": "warn"
  }
};
