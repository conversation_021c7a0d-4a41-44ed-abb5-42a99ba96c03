pipeline {

  agent any



    environment {
        DOCKER_CREDENTIAL_ID = 'dev-biosino-bnlp-builder'
        KUBECONFIG_CREDENTIAL_ID = 'kubecfg-bnlp-admin'

        REGISTRY = 'dev.biosino.org'
        DOCKERHUB_NAMESPACE = 'biosino'
        APP_NAME = 'bnlp-nginx'
        PROJ_ROOTFS = '/zjbdp/nservice/bnlp/proxy-nginx/'
    }


    stages {

        stage ('checkout scm') {
            steps {
                checkout(scm)
            }
        }

        // 替换静态文件实现更新
        stage('replace && restart'){

            steps {
                sh "cp deploy/bnlp-v3.zip ${PROJ_ROOTFS}/bnlp-v3.${BUILD_ID}.zip"
                sh "rm -r ${PROJ_ROOTFS}/bnlp-v3 && unzip -d ${PROJ_ROOTFS} ${PROJ_ROOTFS}/bnlp-v3.${BUILD_ID}.zip"
            }
        }
    }
}

