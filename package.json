{"name": "bnlp", "version": "1.0.0", "private": true, "scripts": {"serve": "vue-cli-service serve --mode development", "build": "vue-cli-service build --mode production", "build test101": "vue-cli-service build --mode test101", "lint": "vue-cli-service lint --fix"}, "dependencies": {"@antv/g6": "^4.3.11", "axios": "0.17.1", "color": "^4.2.3", "core-js": "^3.8.3", "echarts": "^5.4.2", "element-tree-line": "^0.2.1", "element-ui": "2.15.13", "file-saver": "^2.0.5", "lodash": "4.17.21", "mavon-editor": "^2.10.4", "moment": "^2.29.1", "sass": "^1.26.5", "sass-loader": "^9.0.2", "screenfull": "4.2.0", "svg-sprite-loader": "3.7.3", "vue": "^2.6.14", "vue-click-outside": "^1.1.0", "vue-cookie": "1.1.4", "vue-json-tool": "^1.0.4", "vue-router": "3.1.1", "vuex": "3.0.1", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "^4.4.6", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "^4.4.6", "compression-webpack-plugin": "5.0.2", "cross-env": "^7.0.3", "element-theme-chalk": "^2.15.7", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.0.3", "node-polyfill-webpack-plugin": "^1.1.4", "postcss": "^8.4.13", "prettier": "^2.4.1", "vue-template-compiler": "^2.6.14"}, "overrides": {"webpack": "4.47.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}