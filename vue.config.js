const path = require("path");
const NodePolyfillPlugin = require("node-polyfill-webpack-plugin");
const CompressionPlugin = require("compression-webpack-plugin");

function resolve(dir) {
  return path.join(__dirname, dir);
}

module.exports = {
  // publicPath: process.env.NODE_ENV === 'production' ? '/bfms/' : '/',
  publicPath: `/${process.env.VUE_APP_BASE_URL}/`,
  outputDir: `${process.env.VUE_APP_BASE_URL}/`,
  lintOnSave: true,
  productionSourceMap: false,
  chainWebpack: (config) => {
    const svgRule = config.module.rule("svg");
    svgRule.uses.clear();
    svgRule.exclude.add(/node_modules/);
    svgRule
      .test(/\.svg$/)
      .use("svg-sprite-loader")
      .loader("svg-sprite-loader");
    const imagesRule = config.module.rule("images");
    imagesRule.exclude.add(resolve("src/icons"));
    config.module.rule("images").test(/\.(png|jpe?g|gif|svg)(\?.*)?$/);
    return config;
  },
  configureWebpack: {
    plugins: [
      new NodePolyfillPlugin(),
      new CompressionPlugin({
        test: /\.(js|css|html)?$/i, // 压缩文件格式
        filename: "[path].gz[query]", // 压缩后的文件名
        algorithm: "gzip", // 使用gzip压缩
        minRatio: 0.8 // 压缩率小于1才会压缩
      })
    ]
  }
};
